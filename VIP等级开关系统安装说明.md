# VIP等级开关系统安装说明

## 系统概述

VIP等级开关系统允许后台管理员动态控制各个VIP等级的任务接单功能。当某个VIP等级被关闭时，该等级的用户将无法接取对应等级的任务，只能接取更低等级且开启状态的任务。

## 功能特点

- **动态控制**：后台可实时开启/关闭任意VIP等级
- **权限验证**：用户接单时自动验证VIP等级权限
- **批量操作**：支持批量开启/关闭多个VIP等级
- **缓存优化**：使用缓存机制提高性能
- **友好界面**：基于LayUI的现代化管理界面

## 安装步骤

### 1. 数据库初始化

首先执行数据库脚本创建VIP等级开关表：

```bash
# 在MySQL中执行以下SQL文件
mysql -u用户名 -p数据库名 < vip_level_switch.sql
```

或者直接在数据库管理工具中执行 `vip_level_switch.sql` 文件内容。

### 2. 添加后台管理菜单

修改 `add_vip_switch_menu.php` 文件中的数据库配置：

```php
$database = [
    'hostname' => '127.0.0.1',
    'database' => '你的数据库名',
    'username' => '你的数据库用户名',
    'password' => '你的数据库密码',
    'charset'  => 'utf8'
];
```

然后在浏览器中访问：
```
http://你的域名/add_vip_switch_menu.php
```

### 3. 文件部署

确保以下文件已正确部署到对应目录：

```
application/admin/controller/VipLevelSwitch.php
application/admin/view/vip_level_switch/index.html
application/common/service/VipLevelService.php
application/admin/model/Convey.php (已修改)
application/index/controller/Index.php (已修改)
```

### 4. 清除缓存

删除以下缓存目录中的文件：
- `runtime/cache/`
- `runtime/temp/`
- `application/runtime/cache/`
- `application/runtime/temp/`

## 使用说明

### 后台管理

1. **登录后台管理系统**
2. **找到"VIP等级开关管理"菜单**
3. **进入开关管理页面**

### 功能操作

#### 单个开关操作
- 点击对应VIP等级的"开启"或"关闭"按钮
- 确认操作后立即生效

#### 批量操作
- 勾选需要操作的VIP等级
- 点击"批量开启"或"批量关闭"按钮
- 确认操作

#### 全部重置
- 点击"全部重置"按钮
- 将所有VIP等级重置为开启状态

### 前端效果

#### 用户接单限制
- VIP3用户默认可接VIP1、VIP2、VIP3任务
- 当VIP3被关闭时，VIP3用户只能接VIP1、VIP2任务
- 当VIP2也被关闭时，VIP3用户只能接VIP1任务

#### 任务分类过滤
- 首页任务列表自动过滤不可接的任务分类
- 接单时进行二次验证，确保权限正确

## 技术架构

### 数据库设计
```sql
xy_vip_level_switch 表结构：
- id: 主键
- vip_level: VIP等级 (1-6)
- is_enabled: 是否开启 (0关闭, 1开启)
- switch_name: 开关名称
- description: 描述
- created_time: 创建时间
- updated_time: 更新时间
```

### 服务层设计
- `VipLevelService`: 核心业务逻辑服务
- 缓存机制：减少数据库查询
- 权限验证：多层验证确保安全

### 控制器设计
- `VipLevelSwitch`: 后台管理控制器
- RESTful API设计
- 统一返回格式

## API接口

### 获取开关状态
```
GET /admin/VipLevelSwitch/getSwitchStatus
返回：所有VIP等级开关状态
```

### 切换单个开关
```
POST /admin/VipLevelSwitch/toggleSwitch
参数：vip_level, status
```

### 批量操作
```
POST /admin/VipLevelSwitch/batchToggle
参数：vip_levels, status
```

### 重置所有开关
```
POST /admin/VipLevelSwitch/resetAll
```

## 注意事项

1. **权限控制**：确保只有管理员可以访问开关管理功能
2. **缓存更新**：修改开关状态后会自动清除相关缓存
3. **数据备份**：重要操作前建议备份数据库
4. **测试验证**：部署后请充分测试各种场景

## 故障排除

### 常见问题

1. **菜单不显示**
   - 检查数据库连接配置
   - 清除系统缓存
   - 重新登录后台

2. **开关操作无效**
   - 检查文件权限
   - 验证数据库表是否创建成功
   - 查看错误日志

3. **前端接单异常**
   - 确认VipLevelService文件存在
   - 检查Convey模型是否正确修改
   - 验证数据库数据完整性

### 日志查看
- 系统日志：`runtime/log/`
- 错误日志：检查服务器错误日志
- 数据库日志：查看MySQL慢查询日志

## 版本信息

- **版本**：1.0.0
- **兼容性**：ThinkPHP 5.x
- **数据库**：MySQL 5.7+
- **PHP版本**：7.0+

## 技术支持

如遇到问题，请检查：
1. 文件是否完整部署
2. 数据库表是否正确创建
3. 缓存是否已清除
4. 权限配置是否正确

---

**重要提醒**：部署完成后请删除 `add_vip_switch_menu.php` 文件，避免安全风险。 