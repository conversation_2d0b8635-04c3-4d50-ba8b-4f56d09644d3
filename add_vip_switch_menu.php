<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',  // 请填写您的数据库名
    'username' => 'danss',  // 请填写您的数据库用户名
    'password' => 'MTbhcsYaFBrnMiX6',  // 请填写您的数据库密码
    'charset'  => 'utf8'
];

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>开始添加VIP等级开关管理菜单</h1>";
    
    // 查找系统管理菜单ID（通常VIP管理会放在系统管理下）
    $stmt = $db->query("SELECT id FROM system_menu WHERE title='系统管理' OR title='会员管理' OR title='用户管理' LIMIT 1");
    $parent_menu = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if(!$parent_menu) {
        // 如果没有找到合适的父菜单，查找顶级菜单
        $stmt = $db->query("SELECT id FROM system_menu WHERE pid=0 ORDER BY sort ASC LIMIT 1");
        $parent_menu = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    if(!$parent_menu) {
        die("<p style='color:red'>错误：找不到合适的父菜单！</p>");
    }
    
    $parent_id = $parent_menu['id'];
    echo "<p>父菜单ID: {$parent_id}</p>";
    
    // 检查是否已存在VIP等级开关管理菜单
    $stmt = $db->prepare("SELECT id FROM system_menu WHERE title='VIP等级开关管理' LIMIT 1");
    $stmt->execute();
    $existing = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if($existing) {
        echo "<p style='color:orange'>VIP等级开关管理菜单已存在，ID: {$existing['id']}</p>";
    } else {
        // 添加VIP等级开关管理菜单
        $sql = "INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
                VALUES (:pid, 'VIP等级开关管理', 'admin/VipLevelSwitch/index', 'admin/VipLevelSwitch/index', '', 'fa fa-toggle-on', 200, 1)";
        $stmt = $db->prepare($sql);
        $stmt->execute(['pid' => $parent_id]);
        $menu_id = $db->lastInsertId();
        
        echo "<p>已添加VIP等级开关管理菜单，ID: {$menu_id}</p>";
        
        // 添加子菜单项
        $submenus = [
            ['title' => '开关列表', 'node' => 'admin/VipLevelSwitch/index', 'url' => 'admin/VipLevelSwitch/index'],
            ['title' => '切换开关', 'node' => 'admin/VipLevelSwitch/toggleSwitch', 'url' => 'admin/VipLevelSwitch/toggleSwitch'],
            ['title' => '批量操作', 'node' => 'admin/VipLevelSwitch/batchToggle', 'url' => 'admin/VipLevelSwitch/batchToggle'],
            ['title' => '重置开关', 'node' => 'admin/VipLevelSwitch/resetAll', 'url' => 'admin/VipLevelSwitch/resetAll'],
            ['title' => '获取状态', 'node' => 'admin/VipLevelSwitch/getSwitchStatus', 'url' => 'admin/VipLevelSwitch/getSwitchStatus']
        ];
        
        foreach($submenus as $index => $submenu) {
            $sql_sub = "INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
                        VALUES (:pid, :title, :node, :url, '', '', :sort, 1)";
            $stmt_sub = $db->prepare($sql_sub);
            $stmt_sub->execute([
                'pid' => $menu_id,
                'title' => $submenu['title'],
                'node' => $submenu['node'],
                'url' => $submenu['url'],
                'sort' => ($index + 1) * 10
            ]);
        }
        
        echo "<p>已添加" . count($submenus) . "个子菜单项</p>";
    }
    
    // 清除可能的缓存
    echo "<p>正在清除系统缓存...</p>";
    
    // 如果有缓存表，清除缓存
    try {
        $db->exec("TRUNCATE TABLE system_cache");
        echo "<p>已清除系统缓存表</p>";
    } catch(Exception $e) {
        echo "<p>系统可能没有缓存表，跳过此步骤</p>";
    }
    
    // 尝试删除缓存文件
    $cache_dirs = [
        'runtime/cache',
        'runtime/temp',
        'application/runtime/cache',
        'application/runtime/temp'
    ];
    
    foreach($cache_dirs as $dir) {
        if(is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach($files as $file) {
                if(is_file($file)) {
                    unlink($file);
                }
            }
            echo "<p>已清除缓存目录: {$dir}</p>";
        }
    }
    
    echo "<h2>操作完成！</h2>";
    echo "<p style='color:green'>VIP等级开关管理菜单已成功添加到后台管理系统</p>";
    echo "<p>请<a href='/admin.html' target='_blank'>重新登录后台</a>查看菜单</p>";
    echo "<p><strong>注意：</strong>请确保已经执行了数据库初始化脚本 vip_level_switch.sql</p>";
    
} catch(PDOException $e) {
    die("<p style='color:red'>数据库错误: " . $e->getMessage() . "</p>");
}
?> 