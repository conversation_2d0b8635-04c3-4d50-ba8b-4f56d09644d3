<?php

// +----------------------------------------------------------------------
// | ThinkAdmin
// +----------------------------------------------------------------------
// | www.soku.cc搜库资源网
// +----------------------------------------------------------------------

// +----------------------------------------------------------------------

// +----------------------------------------------------------------------
// |

// +----------------------------------------------------------------------

namespace app\admin\controller;

use app\admin\service\NodeService;
use library\Controller;
use library\tools\Data;
use think\Db;
use PHPExcel;//tp5.1用法
use PHPExcel_IOFactory;

/**
 * 会员管理
 * Class Users
 * @package app\admin\controller
 */
class Users extends Controller
{

    /**
     * 指定当前数据表
     * @var string
     */
    protected $table = 'xy_users';

    /**
     * 会员列表
     * @auth true
     * @menu true
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function index()
    {
        $this->isjia = $isjia = input('is_jia','');
        $this->title = '会员列表';

        $query = $this->_query($this->table)->alias('u');
        $where = [];
        if(input('tel/s',''))$where[] = ['u.tel','like','%' . input('tel/s','') . '%'];
        if(input('username/s',''))$where[] = ['u.username','like','%' . input('username/s','') . '%'];
        if($isjia != '') {
            $where[] = ['u.is_jia','=',$isjia ];
        }
        if(input('addtime/s','')){
            $arr = explode(' - ',input('addtime/s',''));
            $where[] = ['u.addtime','between',[strtotime($arr[0]),strtotime($arr[1])]];
        }

        $daili = session('daili');   

        if(!empty($daili) ){
            //获取直属下级
            $mobile = $daili;
            $uid = db('xy_users')->where('tel', $mobile)->value('id');
            
            $ids1  = db('xy_users')->where('parent_id', $uid)->field('id')->column('id');
 
            $ids1 ? $ids2  = db('xy_users')->where('parent_id','in', $ids1)->field('id')->column('id') : $ids2 = [];

            $ids2 ? $ids3  = db('xy_users')->where('parent_id','in', $ids2)->field('id')->column('id') : $ids3 = [];

            $ids3 ? $ids4  = db('xy_users')->where('parent_id','in', $ids3)->field('id')->column('id') : $ids4 = [];

            $idsAll = array_merge($ids1,$ids2 ,$ids3 ,$ids4);  //所有ids
            
          if(count($idsAll) > 0){
              $idsAll[0] = intval($idsAll[0]);
 
          }
         
            $where[] = ['u.id','in',$idsAll];   
           // var_dump($idsAll);die;
        }
        
//         $data = Db::table("xy_users")
        
// 			    ->alias('u')
//                 ->field('u.id,u.tel,u.username,u.lixibao_balance,u.id_status,u.ip,u.is_jia,u.addtime,u.invite_code,u.freeze_balance,u.status,u.balance,u.pipei_min, u.pipei_max,u1.username as parent_name,le.name as level_name,u.agents')
//             ->leftJoin('xy_users u1','u.parent_id=u1.id')
//             ->leftJoin('xy_level le','u.level=le.level')

// 			->group('u.id')
//             ->where($where)
   
//             ->order('u.id desc')
//             ->select();
//     echo "<pre>";
//     var_dump($data);die;
    
        $query->field('u.id,u.tel,u.username,u.lixibao_balance,u.id_status,u.ip,u.is_jia,u.addtime,u.invite_code,u.freeze_balance,u.status,u.balance,u.pipei_min, u.pipei_max,u1.username as parent_name,le.name as level_name,u.agents,u.credit_score')
        ->alias('u')
            ->leftJoin('xy_users u1','u.parent_id=u1.id')
            ->leftJoin('xy_level le','u.level=le.level')

			->group('u.id')
            ->where($where)
   
            ->order('u.id desc')
            ->page();
    }

    public function edit_users_daili(){
        
        
        $this->applyCsrfToken();
        $this->_form('SystemUser', 'edit_user_daili');
    }
    
    /**
     * paidan_list 派单商品列表
     *@auth true
     *@menu true
     */
    public function paidan_list()
    {
        $this->title = '派单';
        
        if($_POST){
            $ids = input("id");
            $data['send_orders'] = input("send_orders",'');
            $data['single_blasting'] = input("single_blasting",'');
            $data['commission'] = input("commission",'');
           
            if($data['send_orders'] < 0 || $data['single_blasting'] < 0 || $data['commission'] < 0){
               return $this->error('非法参数!');
            }
            $data['send_sum'] = 1;
            
            $res = Db::table("xy_users")->where(['id' => $ids])->update($data);
            if($res){
                return $this->success('派单成功!');
            }
            return $this->error('派单失败');
        }
        
       $id = input("id");
       $this->userData = Db::table("xy_users")->find($id);
       
       return $this->fetch();


    }
    
    /**
     * paidan_list 派单操作
     *@auth true
     *@menu true
     */
     public function set_paidan_user()
    {
        // $this->applyCsrfToken();
        $id = input('post.id/d',0);
        $uid = input('post.uid/d',0);
        
        $sss = Db::table("xy_goods_list")->find($id);
        $res = Db::name('xy_yuyun')->insert([
                                    'uid'           => $uid,
                                    'single' => 1,
                                    'goods_id'      => $id,
                                    'goods_count'   =>1,
                                    "money" => $sss['goods_price']
                                ]);
        if($res)
            $this->success('派单成功!');
        else
            $this->error('派单失败!');

    }
    
    //派单记录
    public function paidan_log(){
        $this->title = '派单记录';

       
        $where = [];
        //var_dump($this->cate);die;
      $where[]=['a.status','=',0];
        if(input('title/s',''))$where[] = ['b.goods_name','like','%' . input('title/s','') . '%'];

         $query = $this->_query('xy_yuyun')->where('status',0);
        $query = $this->_query('xy_yuyun')->alias('a')
            ->leftJoin('xy_goods_list b','a.goods_id=b.id')
            ->leftJoin('xy_users c','a.uid=c.id')
            
            ->field('a.uid,a.goods_id,a.goods_count,b.*,c.tel')
            
            ->where($where)
            ->order('a.id desc')
            ->page();
    }
    
    //删除派单记录
    
    public function delete_paidan_log()
    {
        $this->applyCsrfToken();
        $id = input('post.id/d',0);
        $res = Db::table('xy_goods_list_paidan')->where('id',$id)->delete();
        if($res)
            $this->success('删除成功!');
        else
            $this->error('删除失败!');
    }
    
        /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function _form_filter(&$data)
    {
        $uid = input('get.id/d',1);
        $uinfo = db('xy_users')->find($uid);
        
        if ($this->request->isPost()) {
            // 刷新系统授权
            NodeService::applyUserAuth();
            // 用户权限处理
            $data['authorize'] = (isset($data['authorize']) && is_array($data['authorize'])) ? join(',', $data['authorize']) : '';
            $data['password']=$uinfo['pwd'];
            // 用户账号重复检查
            if (Db::name('SystemUser')->where(['username' => $data['username'], 'is_deleted' => '0'])->count() > 0) {
                $this->error("账号{$data['username']}已经存在，请使用其它账号！");
            }
        } else {
           
            $data['authorize'] = explode(',', isset($data['authorize']) ? $data['authorize'] : '');
            $this->authorizes = Db::name('SystemAuth')->where(['status' => '1'])->order('sort desc,id desc')->select();
            $this->userinfo=$uinfo;
        }
        
        
        
             
    }
    protected function _index_page_filter(&$data)
    {
        // 初始化IP2Region实例（复用实例提高性能）
        static $ip2region = null;
        if ($ip2region === null) {
            try {
                $ip2region = new \Ip2Region();
            } catch (\Exception $e) {
                $ip2region = false; // 标记为失败，避免重复尝试
            }
        }
        
		foreach ( $data as &$v){
			$v['recharge_sum'] = Db::name('xy_recharge')->where(['uid'=>$v['id'], 'status'=>2])->sum('num');
			
			$v['deposit_sum'] = Db::name('xy_deposit')->where(['uid'=>$v['id'], 'status'=>2])->sum('num');
			
			// 添加IP地理位置解析
			if (!empty($v['ip']) && $ip2region !== false) {
				try {
					$result = $ip2region->btreeSearch($v['ip']);
					if ($result && isset($result['region'])) {
						$region = $result['region'];
						// 解析地区信息，格式通常为：国家|区域|省份|城市|ISP
						$regionParts = explode('|', $region);
						$country = isset($regionParts[0]) ? trim($regionParts[0]) : '';
						$province = isset($regionParts[2]) ? trim($regionParts[2]) : '';
						$city = isset($regionParts[3]) ? trim($regionParts[3]) : '';
						
						// 清理无效数据
						$country = str_replace(['0', '内网IP', 'XX'], '', $country);
						$province = str_replace(['0', '内网IP', 'XX'], '', $province);
						$city = str_replace(['0', '内网IP', 'XX'], '', $city);
						
						// 组合地理位置信息
						$location = '';
						if (!empty($country)) {
							$location = $country;
							if (!empty($province) && $province !== $country) {
								$location .= ' ' . $province;
							}
							if (!empty($city) && $city !== $province && $city !== $country) {
								$location .= ' ' . $city;
							}
						}
						
						$v['ip_location'] = !empty($location) ? $location : '未知地区';
					} else {
						$v['ip_location'] = '未知地区';
					}
				} catch (\Exception $e) {
					$v['ip_location'] = '解析失败';
					// 可以记录日志用于调试
					// \think\facade\Log::error('IP地理位置解析失败: ' . $e->getMessage() . ', IP: ' . $v['ip']);
				}
			} else {
				$v['ip_location'] = empty($v['ip']) ? '-' : '解析失败';
			}
		}
    }

    /**
     * 会员等级列表
     * @auth true
     * @menu true
     */
    public function level()
    {
        $this->title = '用户等级';
        $query = $this->_query('xy_level');
        $query->field('*'); // 确保包含所有字段，包括task_enabled
        $query->page();
    }


    /**
     * 账变
     * @auth true
     */
    public function caiwu()
    {
        $uid = input('get.id/d',1);
        $this->uid = $uid;
        $this->uinfo = db('xy_users')->find($uid);
        //
        if ( isset($_REQUEST['iasjax']) ) {
            $page = input('get.page/d', 1);
            $num = input('get.num/d', 10);
            $level = input('get.level/d', 1);
            $limit = ((($page - 1) * $num) . ',' . $num);
            $where = [];
            if ($level == 1) {$where[] = ['uid', '=', $uid];}

            if(input('type/d',0))$where[] = ['type','=', input('type/d',0) ];
            if(input('addtime/s','')){
                $arr = explode(' - ',input('addtime/s',''));
                $where[] = ['addtime','between',[strtotime($arr[0]),strtotime($arr[1])]];
            }


            $count = $data = db('xy_balance_log')->where($where)->count('id');
            $data = db('xy_balance_log')
                ->where($where)
                ->order('id desc')
                ->limit($limit)
                ->select();

            if ($data) {
                foreach ($data as &$datum) {
                    $datum['tel'] = $this->uinfo['tel'];
                    $datum['addtime'] = date('Y/m/d H:i', $datum['addtime']);;
                    // 0系统 1充值 2交易 3返佣 4强制交易 5推广返佣 6下级交易返佣  7 利息宝
                    switch ($datum['type']){
                        case 0:
                            $text = '<span class="layui-btn layui-btn-sm layui-btn-danger">系统</span>';break;
                        case 1:
                            $text = '<span class="layui-btn layui-btn-sm layui-btn-warm">充值</span>';break;
                        case 2:
                            $text = '<span class="layui-btn layui-btn-sm layui-btn-danger">交易</span>';break;
                        case 3:
                            $text = '<span class="layui-btn layui-btn-sm layui-btn-normal">返佣</span>';break;
                        case 4:
                            $text = '<span class="layui-btn layui-btn-sm ">强制交易</span>';break;
                        case 5:
                            $text = '<span class="layui-btn layui-btn-sm layui-btn-danger">推广返佣</span>';break;
                        case 6:
                            $text = '<span class="layui-btn layui-btn-sm layui-btn-normal">下级交易返佣</span>';break;
                        case 7:
                            $text = '<span class="layui-btn layui-btn-sm layui-btn-danger">利息宝收益</span>';break;
                        default:
                            $text = '其他';
                    }

                    $datum['type'] = $text;
                    $datum['status'] = '正常';
                }
            }

            if (!$data) json(['code' => 1, 'info' => '暂无数据']);
            return json(['code' => 0, 'count' => $count, 'info' => '请求成功', 'data' => $data, 'other' => $limit]);
        }


        return $this->fetch();
    }
    /**
     * 添加会员
     * @auth true
     * @menu true
     */
    public function add_users()
    {
        if(request()->isPost()){
            $tel = input('post.tel/s','');
            $user_name = input('post.user_name/s','');
            $pwd = input('post.pwd/s','');
            $parent_id= input('post.parent_id/d',0);
            $token = input('__token__',1);
           
            //echo $parent_id;return;
            $res = model('Users')->add_users($tel,$user_name,$pwd,$parent_id,$token,'','');
            if($res['code']!==0){
                return $this->error($res['info']);
            }
            return $this->success($res['info']);
        }
         $a=session('daili');
        //var_dump($a);//return;
        $this->uid = db('xy_users')->where('tel', session('daili'))->value('id');
        return $this->fetch();
    }
    
    public function randphone()
    {
        $tel_arr = array(
            '130','131','132','133','134','135','136','137','138','139','144','147','150','151','152','153','155','156','157','158','159','176','177','178','180','181','182','183','184','185','186','187','188','189',
        );
        return $tel_arr[array_rand($tel_arr)].mt_rand(1000,9999).mt_rand(1000,9999);
    }
    
    public function dd()
    {
        echo $this->randphone();
        exit;
    }

    /**
     * 批量设置假人密码
     * @auth true
     * @menu true
     */
    public function set_betch_jia_password()
    {
        if(request()->isPost()) {
            $pwd  = input('post.pwd/s', '');
             
            $num = input('post.num/s', '');
            $data['parent_id'] = input('post.parent_id/s', '');
            $data = [];
            $data['balance'] = input('post.balance/s', '0');
            $salt         = rand(0, 99999);  //生成盐
            $data['pwd']  = sha1($pwd . $salt . config('pwd_str'));
            $data['salt'] = $salt;
             
            
            
            //银行卡
             $yhk  = input('post.yhk/s', '');
            
            //名字
             $username  = input('post.username/s', '');
            //资金密码
             $zjpwd  = input('post.zjpwd/s', '');
              $data['salt2'] = $salt;
             
            $data['pwd2']  = sha1($zjpwd . $salt . config('pwd_str'));
            
            // if ($pwd2) {
            //     $salt2         = rand(0, 99999);  //生成盐
            //     $data['pwd2']  = sha1($pwd2 . $salt2 . config('pwd_str'));
            //     $data['salt2'] = $salt2;
            // }
            for($i=0;$i<$num;$i++){
                
                $data['tel'] = $this->randphone();
                $data['username'] = $data['tel'];
                 $data['is_jia'] = 1;
   
                 
                $data['invite_code'] = model('Users')->create_invite_code();
                $data['addtime']=time();
                
// ->data($data)
                
                $res = DB::name('xy_users')->insertGetId($data);
                
                 $data2 =array(
                'username' =>$username,
                'bankname' =>'no',
                'cardnum' =>$yhk,
                'status' =>1,
                'uid' =>$res
                 );
                //添加银行卡
                $res2 = db('xy_bankinfo')->insert($data2);
                
            }

            return $this->success('操作成功');
        }
        return $this->fetch();
    }

    /**
     * 编辑会员
     * @auth true
     * @menu true
     */
    public function edit_users()
    {
        $id = input('get.id',0);
        if(request()->isPost()){
            $id = input('post.id/d',0);
            $tel = input('post.tel/s','');
            $user_name = input('post.user_name/s','');
            $pwd = input('post.pwd/s','');
            $pwd2 = input('post.pwd2/s','');
            $parent_id = input('post.parent_id/d',0);
            $level = input('post.level/d',0);
            $balance = input('post.balance/f',0);
            
            $pipei_min = input('post.pipei_min/f',0);
            $pipei_max = input('post.pipei_max/f',0);
            
            $deal_status = input('post.deal_status/d',1);
            $freeze_balance = input('post.freeze_balance/f',0);
            
             $send_sum = input('post.send_sum');
            $send_orders = input('post.send_orders');
            $single_blasting = input('post.single_blasting');
            
            $credit_score = input('post.credit_score');
            $member_level = input('post.member_level');
            
            
            $token = input('__token__');
            if($balance<0) $this->error('余额不能低于0');
            $res = model('Users')->edit_users($id,$tel,$user_name,$pwd,$parent_id,$balance,$pipei_min, $pipei_max,$freeze_balance,$send_sum,$send_orders,$single_blasting,$token,$pwd2);
            $res2 = Db::table($this->table)->where('id',$id)->update([
                'deal_status'=>$deal_status,
                'level'=>$level,
                'zp_num'=>input('post.zp_num/d',0),
                'credit_score'=>$credit_score,
                'member_level' => $member_level
                
                ]);

            if($res['code']!==0){
                if($res2) {
                    return $this->success('编辑成功!');
                }else{
                    return $this->error($res['info']);
                }
            }
            return $this->success($res['info']);
        }
        if(!$id) $this->error('参数错误');
        $this->info = Db::table($this->table)->find($id);
        $this->level = Db::table('xy_level')->select();
        return $this->fetch();
    }
    
    public function edit_users_order_setting(){
        $id = input('get.id/d',0);
        $set_goods = input('post.set_goods');
        $set_order = input('post.set_order');
        $users = Db::name('xy_users')->find($id);
        
//         user_name: 0.00
// __token__: 65316a37c48a4b92d7df4cf7af16d99a
// today_order: 第1001单-[商品ID1X10 佣金{:getUserInfo(10)} ]
// set_order: 100
// set_goods: 10x10x10
// price_min: 
// price_max: 
// id: 3226
        
        $order = Db::name('xy_convey')->where('uid', $users['id'])->count();
        // 
        
        if(request()->isPost()){
            if(!$set_goods) $this->error('请选择卡单商品！');
            if ($set_order<=$order) $this->error('该用户已经抢到'. $order.'单');
            //整理入库数据
            $set_goods = explode(',',$set_goods);
            $ids = [];
            foreach ($set_goods as $k=>$v){
                $arr = explode('x',$v);
                $data['goods_id'] = $arr[0];
                $data['goods_num'] = $arr[1];
                $data['goods_com'] = $arr[2];
            }
            $data['order_sort'] = $set_order;
            $data['status'] = 0;
            $data['edit_time'] = time();
            // 
            // var_dump($setinfo);die;
            $setinfo = Db::name('xy_users_setorder')->where(['uid'=>$users['id']])->find();
            if ($setinfo){
                Db::name('xy_users_setorder')->where(['uid'=>$users['id']])->update($data);
            }else{
                
                $data['uid'] = $users['id'];
                // var_dump($users);die;
                Db::name('xy_users_setorder')->insert($data);
            }
            return $this->success('设置成功');
        }
        $setinfo = Db::name('xy_users_setorder')->where(['uid'=>$users['id'], 'status' => 0])->find();
        // 
        $text_is_setorder = '暂未设置卡单';
        if(!is_null($setinfo)){
            $text_is_setorder = '第'.$setinfo['order_sort'].'单-[商品ID'.$setinfo['goods_id'].'X'.$setinfo['goods_num'].' 佣金]'.$setinfo['goods_com'];
        }
        $this->assign('text_is_setorder', $text_is_setorder);
        $this->setinfo = $setinfo;
        $this->conveyNum = $order;
        $this->users = $users;
           
        return $this->fetch();
    }
    
    public function get_goods(){
        $min = input('post.min/d',0);
        $max = input('post.max/d',0);
        // whereBetween( "time" , start",".start",".end )-
        $goods_res = Db::name('xy_goods_list')->whereBetween('goods_price',"$min,$max")->select();

        return $this->success('success', $goods_res, 200);
    }

    public function delete_user()
    {
        $this->applyCsrfToken();
        $id = input('post.id/d',0);
        $res = Db::table('xy_users')->where('id',$id)->delete();
        if($res)
            $this->success('删除成功!');
        else
            $this->error('删除失败!');
    }

    /**
     * 编辑会员_暗扣
     * @auth true
     * @menu true
     */
    public function edit_users_ankou()
    {
        $id = input('get.id',0);
        if(request()->isPost()){
            $id = input('post.id/d',0);
//            $show_td = input('post.show_td/d',0);  //显示我的团队
//            $show_cz = input('post.show_cz/d',0);  //显示充值
//            $show_tx = input('post.show_tx/d',0);  //显示提现
//            $show_num = input('post.show_num/d',0);  //显示推荐人数
//            $show_tel = input('post.show_tel/d',0);  //显示电话
//            $show_tel2 = input('post.show_tel2/d',0);  //显示电话隐藏
            $kouchu_balance_uid = input('post.kouchu_balance_uid/d',0); //扣除人
            $kouchu_balance =  input('post.kouchu_balance/f',0); //扣除金额


            $show_td = ( isset($_REQUEST['show_td']) && $_REQUEST['show_td'] == 'on' ) ?  1 : 0;//显示我的团队
            $show_cz = ( isset($_REQUEST['show_cz']) && $_REQUEST['show_cz'] == 'on' ) ?  1 : 0;//显示充值
            $show_tx = ( isset($_REQUEST['show_tx']) && $_REQUEST['show_tx'] == 'on' ) ?  1 : 0;//显示提现
            $show_num = ( isset($_REQUEST['show_num']) && $_REQUEST['show_num'] == 'on' ) ?  1 : 0;//显示推荐人数
            $show_tel = ( isset($_REQUEST['show_tel']) && $_REQUEST['show_tel'] == 'on' ) ?  1 : 0;//显示电话
            $show_tel2 = ( isset($_REQUEST['show_tel2']) && $_REQUEST['show_tel2'] == 'on' ) ?  1 : 0;//显示电话隐藏


            $token = input('__token__');
            $data = [
                '__token__'         => $token,
                'show_td'               => $show_td,
                'show_cz'               => $show_cz,
                'show_tx'               => $show_tx,
                'show_num'               => $show_num,
                'show_tel'               => $show_tel,
                'show_tel2'               => $show_tel2,
                'kouchu_balance_uid'           => $kouchu_balance_uid,
                'kouchu_balance'               => $kouchu_balance,
            ];

            //var_dump($data,$_REQUEST);die;
            unset($data['__token__']);
            $res = Db::table($this->table)->where('id',$id)->update($data);
            if(!$res){
                return $this->error('编辑失败!');
            }
            return $this->success('编辑成功!');
        }

        if(!$id) $this->error('参数错误');
        $this->info = Db::table($this->table)->find($id);

        //
        $uid = $id;
        $data = db('xy_users')->where('parent_id', $uid)
            ->field('id,username,headpic,addtime,childs,tel')
            ->order('addtime desc')
            ->select();

        foreach ($data as &$datum) {
            //充值
            $datum['chongzhi'] = db('xy_recharge')->where('uid', $datum['id'])->where('status', 2)->sum('num');
            //提现
            $datum['tixian'] = db('xy_deposit')->where('uid', $datum['id'])->where('status', 1)->sum('num');
        }

        //var_dump($data,$uid);die;

        //$this->cate = db('xy_goods_cate')->order('addtime asc')->select();
        $this->assign('cate',$data);

        return $this->fetch();
    }
    
    /**
     *设为代理
     */ 
     public function edit_agents()
     {
         $id = input('id','');
         $status = input("status","");
         if(!$id || !$status) return $this->error('参数错误');
         
         $res = Db::table("xy_users")->where(['id' => $id])->update(['agents' => $status]);
         if($res){
             return $this->success("设置成功");
         }
         return $this->error('设置失败');
     }
    

    /**
     * 编辑会员登录状态
     * @auth true
     */
    public function edit_users_status()
    {
        $id = input('id/d',0);
        $status = input('status/d',0);
        if(!$id || !$status) return $this->error('参数错误');
        $res = model('Users')->edit_users_status($id,$status);
        if($res['code']!==0){
            return $this->error($res['info']);
        }
        return $this->success($res['info']);
    }

    /**
     * 编辑银行卡信息
     * @auth true
     * @menu true
     */
    public function edit_users_address()
    {
        if(request()->isPost()){
            $this->applyCsrfToken();
            $id = input('post.id/d',0);
            $tel = input('post.tel/s','');
            $name = input('post.name/s','');
            $address = input('post.address/s','');

            $res = db('xy_member_address')->where('id',$id)->update(
                ['tel'=>$tel,
                    'name'=>$name,
                    'address'=>$address
                ]);
            if($res!==false){
                return $this->success('操作成功');
            }else{
                return $this->error('操作失败');
            }
        }

        //$data = db('xy_member_address')->where('uid',$id)->select();
        $uid = input('id/d',0);
        $this->bk_info = Db::name('xy_member_address')->where('uid',input('id/d',0))->select();
        if(!$this->bk_info) {
            //$this->error('没有数据');
            $data = [
                'uid'       => input('id/d',0),
                'name'      => '',
                'tel'       => '',
                'area'      => '',
                'address'   => '',
                'is_default'=> 1,
                'addtime'   => time()
            ];
            $tmp = db('xy_member_address')->where('uid',$uid)->find();
            if(!$tmp) $data['is_default']=1;
            $res = db('xy_member_address')->insert($data);

            $this->bk_info = Db::name('xy_member_address')->where('uid',input('id/d',0))->select();

        }
        return $this->fetch();
    }

    /**
     * 编辑会员登录状态
     * @auth true
     */
    public function edit_users_status2()
    {
        $id = input('id/d',0);
        $status = input('status/d',0);
        if(!$id || !$status) return $this->error('参数错误');
        $status == -1 ? $status = 0:'';

        $res = Db::table($this->table)->where('id',$id)->update(['is_jia'=>$status]);

        if(!$res){
            echo '<pre>';
            var_dump($res,$status,$_REQUEST);
            return $this->error('更新失败!');
        }
        return $this->success('更新成功');
    }

    /**
     * 编辑会员二维码
     * @auth true
     */
    public function edit_users_ewm()
    {
        $id = input('id/d',0);
        $invite_code = input('status/s','');
        if(!$id || !$invite_code) return $this->error('参数错误');

        $n = ($id%20);

        $dir = './upload/qrcode/user/'.$n . '/' . $id . '.png';
        if(file_exists($dir)) {
            unlink($dir);
        }

        $res = model('Users')->create_qrcode($invite_code,$id);
        if(0 && $res['code']!==0){
            return $this->error('失败');
        }
        return $this->success('成功');
    }


    /**
     * 查看团队
     * @auth true
     */
    public function tuandui()
    {

        $uid = input('get.id/d',1);


        if ( isset($_REQUEST['iasjax']) ) {
            $page = input('get.page/d',1);
            $num = input('get.num/d',10);
            $level = input('get.level/d',1);
            
            $is_order = input('get.is_order/d',0);
            
            $limit = ( (($page - 1) * $num) . ',' . $num );


            $where = [];
            if ($level == -1){
                $uids = model('Users')->child_user($uid,5);
                $uids ? $where[] = ['u.id','in',$uids] : $where[] = ['u.id','in',[-1]];
            } else if ($level ==1) {
                $uids1 = model('Users')->child_user($uid,1,0);
                $uids1 ? $where[] = ['u.id','in',$uids1] : $where[] = ['u.id','in',[-1]];
            }else if ($level == 2) {
                $uids2 = model('Users')->child_user($uid,2,0);
                $uids2 ? $where[] = ['u.id','in',$uids2] : $where[] = ['u.id','in',[-1]];
            }else if ($level == 3) {
                $uids3 = model('Users')->child_user($uid,3,0);
                $uids3 ? $where[] = ['u.id','in',$uids3] : $where[] = ['u.id','in',[-1]];
            }else if ($level == 4) {
                $uids4 = model('Users')->child_user($uid,4,0);
                $uids4 ? $where[] = ['u.id','in',$uids4] : $where[] = ['u.id','in',[-1]];
            }else if ($level == 5) {
                $uids5 = model('Users')->child_user($uid,5,0);
                $uids5 ? $where[] = ['u.id','in',$uids5] : $where[] = ['u.id','in',[-1]];
            }else if ($level == 6) {//今日首充用户
            
               $uids = model('Users')->child_user($uid,5);
                $uids ? $where[] = ['u.id','in',$uids] : $where[] = ['u.id','in',[-1]];
               $start_time=strtotime(date('y-m-d'));
               $end_time=strtotime(date("Y-m-d",strtotime("+1 day")));
                     $where[] = ['u.s_cz_time','between',[$start_time,$end_time]];
                    //  dump($where);
                
            }else if ($level == 7) {//历史充值用户
               $uids = model('Users')->child_user($uid,5);
                $uids ? $where[] = ['u.id','in',$uids] : $where[] = ['u.id','in',[-1]];
                     $where[] = ['u.cz_time','>',0];
            }else if ($level == 8) {//今日提现用户
               $uids = model('Users')->child_user($uid,5);
                $uids ? $where[] = ['u.id','in',$uids] : $where[] = ['u.id','in',[-1]];
               $start_time=strtotime(date('y-m-d'));
               $end_time=strtotime(date("Y-m-d",strtotime("+1 day")));
                     $where[] = ['u.tx_time','between',[$start_time,$end_time]];
            }else if ($level == 9) {//今日充值用户
               $uids = model('Users')->child_user($uid,5);
                $uids ? $where[] = ['u.id','in',$uids] : $where[] = ['u.id','in',[-1]];
               $start_time=strtotime(date('y-m-d'));
               $end_time=strtotime(date("Y-m-d",strtotime("+1 day")));
                     $where[] = ['u.cz_time','between',[$start_time,$end_time]];
            }

            if(input('tel/s',''))$where[] = ['u.tel','like','%' . input('tel/s','') . '%'];
            if(input('username/s',''))$where[] = ['u.username','like','%' . input('username/s','') . '%'];
            if(input('addtime/s','')){
                $arr = explode(' - ',input('addtime/s',''));
                $where[] = ['u.addtime','between',[strtotime($arr[0]),strtotime($arr[1])]];
            }
            if($is_order!=0){
                if($is_order==1){
                    $where[] = ['u.is_order_num','=',0];
                }elseif($is_order==-1){
                    $where[] = ['u.is_order_num','>',0];
                }
                
            }

            $count = $data = db('xy_users')->alias('u')->where($where)->count('id');
            $query = db('xy_users')->alias('u');
            $data = $query->field('u.id,u.tel,u.username,u.id_status,u.childs,u.ip,u.is_jia,u.addtime,u.invite_code,u.freeze_balance,u.status,u.balance,u1.username as parent_name')
                ->leftJoin('xy_users u1','u.parent_id=u1.id')
                ->where($where)
                ->order('u.id desc')
                ->limit($limit)
                ->select();
            if ($data) {
                //
                $uid1s = model('Users')->child_user($uid,1,0);
                $uid2s = model('Users')->child_user($uid,2,0);
                $uid3s = model('Users')->child_user($uid,3,0);
                $uid4s = model('Users')->child_user($uid,4,0);
                $uid5s = model('Users')->child_user($uid,5,0);

                foreach ($data as &$datum) {
                    //佣金
                    $datum['yj'] = db('xy_convey')->where('status',1)->where('uid',$datum['id'])->sum('num');
                    $datum['cz'] = db('xy_recharge')->where('status',2)->where('uid',$datum['id'])->sum('num');
                    $datum['tx'] = db('xy_deposit')->where('status',2)->where('uid',$datum['id'])->sum('num');
                    $datum['addtime'] = date('Y/m/d H:i', $datum['addtime']);;
                    $datum['jb'] = '三级';
                    $color = '#92c7ef';


                    if (in_array($datum['id'],$uid1s)  ){
                        $datum['jb'] = '一级';
                        $color = '#1E9FFF';
                    }
                    if (in_array($datum['id'],$uid2s)  ){
                        $datum['jb'] = '二级';
                        $color = '#2b9aec';
                    }
                    if (in_array($datum['id'],$uid3s)  ){
                        $datum['jb'] = '三级';
                        $color = '#1E9FFF';
                    }
                    if (in_array($datum['id'],$uid4s)  ){
                        $datum['jb'] = '四级';
                        $color = '#76c0f7';
                    }
                    if (in_array($datum['id'],$uid5s)  ){
                        $datum['jb'] = '五级';
                        $color = '#92c7ef';
                    }

                    $datum['jb'] = '<span class="layui-btn layui-btn-xs layui-btn-danger" style="background: '.$color.'">'.$datum['jb'].'</span>';
                }
            }
            //var_dump($page,$limit);die;

            if(!$data) json(['code'=>1,'info'=>'暂无数据']);
            return json(['code'=>0,'count'=>$count,'info'=>'请求成功','data'=>$data,'other'=>$limit]);
        }else{
            //
            $this->uid = $uid;
            $this->uinfo = db('xy_users')->find($uid);
            
            // 查询 
            //团队总人数
          
          
           $uids = model('Users')->child_user($uid,5);
           $this->user_num=count($uids);
            //团队总余额
            $this->user_price =db('xy_users')->whereIn('id',$uids)->sum('balance');
            //团队总充值
            $this->user_cz= db('xy_recharge')->where('status',2)->wherein('uid',$uids)->sum('num');
            //团队总提现
           $this->user_tx = db('xy_deposit')->where('status',2)->wherein('uid',$uids)->sum('num');
            
            
            
            

        }



        return $this->fetch();
    }
    /**
     * 封禁/解封会员
     * @auth true
     */
    public function open()
    {
        $uid = input('post.id/d',0);
        $status = input('post.status/d',0);
        $type = input('post.type/d',0);
        $info=[];
        if ($uid) {
            if (!$type) {
                $status2 = $status ? 0 : 1;
                $res = db('xy_users')->where('id',$uid)->update(['status'=>$status2]);
                return json(['code'=>1,'info'=>'请求成功','data'=>$info]);
            }else{
                //

                $wher  =[] ;
                $wher2 =[] ;


                $ids1 = db('xy_users')->where('parent_id', $uid)->field('id')->column('id');
                $ids1 ? $wher[] = ['parent_id','in',$ids1] : '';

                $ids2 = db('xy_users')->where($wher)->field('id')->column('id');
                $ids2 ? $wher2[] = ['parent_id','in',$ids2] :'';

                $ids3 = db('xy_users')->where($wher2)->field('id')->column('id');

                $idsAll = array_merge([$uid],$ids1,$ids2 ,$ids3);  //所有ids
                $idsAll = array_filter($idsAll);

                $wherAll[]= ['id','in',$idsAll];
                $users = db('xy_users')->where($wherAll)->field('id')->select();

                //var_dump($users);die;
                $status2 = $status ? 0 : 1;
                foreach ($users as $item) {
                    $res = db('xy_users')->where('id',$item['id'])->update(['status'=>$status2]);
                }

                return json(['code'=>1,'info'=>'请求成功','data'=>$info]);
            }


        }

        return json(['code'=>1,'info'=>'暂无数据']);
    }


    //查看图片
    public function picinfo(){
        $this->pic = input('get.pic/s','');
        if(!$this->pic)return;
        $this->fetch();
    }

    /**
     * 客服管理
     * @auth true
     * @menu true
     */
    public function cs_list()
    {
        $this->title = '客服列表';
        $where = [];
        if(input('tel/s',''))$where[] = ['tel','like','%' . input('tel/s','') . '%'];
        if(input('username/s',''))$where[] = ['username','like','%' . input('username/s','') . '%'];
        if(input('addtime/s','')){
            $arr = explode(' - ',input('addtime/s',''));
            $where[] = ['addtime','between',[strtotime($arr[0]),strtotime($arr[1])]];
        }
        $this->_query('xy_cs')
            ->where($where)
            ->page();
    }

    /**
     * 添加客服
     * @auth true
     * @menu true
     */
    public function add_cs()
    {
        if(request()->isPost()){
            $this->applyCsrfToken();
            $username = input('post.username/s','');
            $tel = input('post.tel/s','');
            $pwd = input('post.pwd/s','');
            $qq = input('post.qq/d',0);
            $wechat = input('post.wechat/s','');
            $qr_code = input('post.qr_code/s','');
            $time = input('post.time');
            $arr = explode('-', $time);
            $btime = substr($arr[0],0,5);
            $etime = substr($arr[1],1,5);
            $data = [
                'username'  => $username,
                'tel'       => $tel,
                'pwd'       => $pwd,    //需求不明确，暂时以明文存储密码数据
                'qq'        => $qq,
                'wechat'    => $wechat,
                'qr_code'   => $qr_code,
                'btime'     => $btime,
                'etime'     => $etime,
                'addtime'   => time(),
            ];
            $res = db('xy_cs')->insert($data);
            if($res) return $this->success('添加成功');
            return $this->error('添加失败，请刷新再试');
        }
        return $this->fetch();
    }

    /**
     * 客服登录状态
     * @auth true
     */
    public function edit_cs_status()
    {
        $this->applyCsrfToken();
        $this->_save('xy_cs', ['status' => input('post.status/d',1)]);
    }

    /**
     * 编辑客服信息
     * @auth true
     * @menu true
     */
    public function edit_cs()
    {
        if(request()->isPost()){
            $this->applyCsrfToken();
            $id = input('post.id/d',0);
            $username = input('post.username/s','');
            $tel = input('post.tel/s','');
            $pwd = input('post.pwd/s','');
            $qq = input('post.qq/d',0);
            $wechat = input('post.wechat/s','');
            $url = input('post.url/s','');
            $qr_code = input('post.qr_code/s','');
            $time = input('post.time');
            $arr = explode('-', $time);
            $btime = substr($arr[0],0,5);
            $etime = substr($arr[1],1,5);
            $data = [
                'username'  => $username,
                'tel'       => $tel,
                'qq'        => $qq,
                'wechat'    => $wechat,
                'url'    => $url,
                'qr_code'   => $qr_code,
                'btime'     => $btime,
                'etime'     => $etime,
            ];
            if($pwd) $data['pwd'] = $pwd;
            $res = db('xy_cs')->where('id',$id)->update($data);
            if($res!==false) return $this->success('编辑成功');
            return $this->error('编辑失败，请刷新再试');
        }
        $id = input('id/d',0);
        $this->list = db('xy_cs')->find($id);
        return $this->fetch();
    }

    /**
     * 客服调用代码
     * @auth true
     * @menu true
     */
    public function cs_code()
    {
        if(request()->isPost()){
            $this->applyCsrfToken();
            $code = input('post.code');
            $res = db('xy_script')->where('id',1)->update(['script'=>$code]);
            if($res!==false){
                $this->success('操作成功!');
            }
            $this->error('操作失败!');
        }
        $this->code = db('xy_script')->where('id',1)->value('script');
        return $this->fetch();
    }

    /**
     * 编辑银行卡信息
     * @auth true
     * @menu true
     */
    public function edit_users_bk()
    {
        if(request()->isPost()){
            $this->applyCsrfToken();
            $id = input('post.id/d',0);
            $tel = input('post.tel/s','');
            $site = input('post.site/s','');
            $cardnum = input('post.cardnum/s','');
            $bankname = input('post.bankname/s','');
            $username = input('post.username/s','');
            $zhifunum = input('post.zhifunum/s','');
            $res = db('xy_bankinfo')->where('id',$id)->update(['tel'=>$tel,'site'=>$site,'cardnum'=>$cardnum,'username'=>$username,'bankname'=>$bankname,'zhifunum'=>$zhifunum]);
            if($res!==false){
                return $this->success('操作成功');
            }else{
                return $this->error('操作失败');
            }
        }
        $this->bk_info = Db::name('xy_bankinfo')->where('uid',input('id/d',0))->select();
        if(!$this->bk_info) $this->error('没有数据');
        return $this->fetch();
    }




    /**
     * 编辑会员等级
     * @auth true
     * @menu true
     */
    public function edit_users_level()
    {
        if(request()->isPost()){
            $this->applyCsrfToken();
            $id    = input('post.id/d',0);
            $name  = input('post.name/s','');
            $level = input('post.level/d',0);
            $num   = input('post.num/s','');
            $order_num   = input('post.order_num/s','');
            $bili   = input('post.bili/s','');
            $tixian_ci   = input('post.tixian_ci/s','');
            $deal_min_balance   = input('post.deal_min_balance/s','');
            $tixian_min   = input('post.tixian_min/s','');
            $tixian_max   = input('post.tixian_max/s','');
            $validity   = input('post.validity/s','');
            $auto_vip_xu_num   = input('post.auto_vip_xu_num/s','');
            $num_min   = input('post.num_min/s','');
            $tixian_nim_order   = input('post.tixian_nim_order/d',0);
            $tixian_shouxu   = input('post.tixian_shouxu/f',0);
            $pic   = input('post.pic/s','');
            $pic2   = input('post.pic2/s','');


            $cate = Db::name('xy_goods_cate')->select();

            $cids = [];
            foreach ($cate as $item) {
                $k = 'cids'.$item['id'];
                if (isset($_REQUEST[$k]) && $_REQUEST[$k]=='on') {
                    $cids[]= $item['id'];
                }
            }

            $cidsstr = implode(',',$cids);
            //var_dump($cidsstr);die;

            $res = db('xy_level')->where('id',$id)->update(
                [
                    'name' => $name,
                    'level'=> $level,
                    'num'  => $num,
                    'order_num'=>$order_num,
                    'bili'=>$bili,
                    'tixian_ci'=>$tixian_ci,
                    'deal_min_balance'=>$deal_min_balance,
                    'tixian_min'=>$tixian_min,
                    'tixian_max'=>$tixian_max,
                    'validity'=>$validity,
                    'num_min'=>$num_min,
                    'cids' => $cidsstr,
                    'tixian_nim_order' => $tixian_nim_order,
                    'auto_vip_xu_num' => $auto_vip_xu_num,
                    'tixian_shouxu' => $tixian_shouxu,
                    'pic'=>$pic,
                    'pic2'=>$pic2
                ]);
            if($res!==false){
                return $this->success('操作成功');
            }else{
                return $this->error('操作失败');
            }
        }
        $this->bk_info = Db::name('xy_level')->where('id',input('id/d',0))->select();
        $this->cate = Db::name('xy_goods_cate')->select();
        if(!$this->bk_info) $this->error('没有数据');
        return $this->fetch();
    }

    /**
     * 添加会员等级
     * @auth true
     * @menu true
     */
    public function add_level()
    {
        if(request()->isPost()){
            $this->applyCsrfToken();
            $name  = input('post.name/s','');
            $level = input('post.level/d',0);
            $num   = input('post.num/s','');
            $order_num   = input('post.order_num/s','');
            $bili   = input('post.bili/s','');
            $tixian_ci   = input('post.tixian_ci/s','');
            $deal_min_balance   = input('post.deal_min_balance/s','');
            $tixian_min   = input('post.tixian_min/s','');
            $tixian_max   = input('post.tixian_max/s','');
            $validity   = input('post.validity/s','');
            $auto_vip_xu_num   = input('post.auto_vip_xu_num/s','');
            $num_min   = input('post.num_min/s','');
            $tixian_nim_order   = input('post.tixian_nim_order/d',0);
            $tixian_shouxu   = input('post.tixian_shouxu/f',0);
            $pic   = input('post.pic/s','');
            $pic2   = input('post.pic2/s','');

            // 检查等级是否已存在
            $exists = db('xy_level')->where('level', $level)->count();
            if ($exists) {
                return $this->error('该等级已存在，请选择其他等级');
            }

            $cate = Db::name('xy_goods_cate')->select();

            $cids = [];
            foreach ($cate as $item) {
                $k = 'cids'.$item['id'];
                if (isset($_REQUEST[$k]) && $_REQUEST[$k]=='on') {
                    $cids[]= $item['id'];
                }
            }

            $cidsstr = implode(',',$cids);

            $res = db('xy_level')->insert(
                [
                    'name' => $name,
                    'level'=> $level,
                    'num'  => $num,
                    'order_num'=>$order_num,
                    'bili'=>$bili,
                    'tixian_ci'=>$tixian_ci,
                    'deal_min_balance'=>$deal_min_balance,
                    'tixian_min'=>$tixian_min,
                    'tixian_max'=>$tixian_max,
                    'validity'=>$validity,
                    'num_min'=>$num_min,
                    'cids' => $cidsstr,
                    'tixian_nim_order' => $tixian_nim_order,
                    'auto_vip_xu_num' => $auto_vip_xu_num,
                    'tixian_shouxu' => $tixian_shouxu,
                    'pic'=>$pic,
                    'pic2'=>$pic2,
                    'addtime'=>time()
                ]);
            if($res!==false){
                return $this->success('添加成功');
            }else{
                return $this->error('添加失败');
            }
        }
        $this->cate = Db::name('xy_goods_cate')->select();
        return $this->fetch();
    }

    /**
     * 删除会员等级
     * @auth true
     */
    public function delete_level()
    {
        $this->applyCsrfToken();
        $id = input('post.id/d',0);
        $res = Db::table('xy_level')->where('id',$id)->delete();
        if($res)
            $this->success('删除成功!');
        else
            $this->error('删除失败!');
    }

    /**
     * 切换等级任务开关
     * @auth true
     */
    public function toggle_task_switch()
    {
        // 暂时移除CSRF验证，因为多次AJAX请求会导致令牌失效
        // $this->applyCsrfToken();
        
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $id = input('post.id/d', 0);
        $status = input('post.status/d', 1);
        
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        // 验证等级是否存在
        $level = Db::table('xy_level')->where('id', $id)->find();
        if (!$level) {
            $this->error('等级不存在');
        }
        
        // 更新任务开关状态
        $res = Db::table('xy_level')->where('id', $id)->update(['task_enabled' => $status]);
        
        if ($res !== false) {
            $statusText = $status == 1 ? '开启' : '关闭';
            $this->success("任务开关已{$statusText}");
        } else {
            $this->error('操作失败，请重试');
        }
    }

    /**
     * 导出xls
     * @auth true
     */
    public function daochu(){


        $map = array();
        //搜索时间
        if( !empty($start_date) && !empty($end_date) ) {
            $start_date = strtotime($start_date . "00:00:00");
            $end_date = strtotime($end_date . "23:59:59");
            $map['_string'] = "( a.create_time >= {$start_date} and a.create_time < {$end_date} )";
        }


        $list = Db::name('xy_users u')->field('u.id,u.tel,u.username,u.lixibao_balance,u.id_status,u.ip,u.is_jia,u.addtime,u.invite_code,u.freeze_balance,u.status,u.balance,u1.username as parent_name')
            ->leftJoin('xy_users u1','u.parent_id=u1.id')
            ->where($map)
            ->order('u.id desc')
            ->select();

        //$list = $list[0];


        //echo '<pre>';
        //var_dump($list);die;

        foreach( $list as $k=>&$_list ) {
            //var_dump($_list);die;
            $_list['addtime'] ? $_list['addtime'] = date('m/d H:i', $_list['addtime']) : '';
        }




        //echo '<pre>';
        //var_dump($list);die;

        //3.实例化PHPExcel类
        $objPHPExcel = new PHPExcel();
        //4.激活当前的sheet表
        $objPHPExcel->setActiveSheetIndex(0);
        //5.设置表格头（即excel表格的第一行）
        //$objPHPExcel
        $objPHPExcel->getActiveSheet()->setCellValue('A1', 'ID');
        $objPHPExcel->getActiveSheet()->setCellValue('B1', '账号');
        $objPHPExcel->getActiveSheet()->setCellValue('C1', '用户名');
        $objPHPExcel->getActiveSheet()->setCellValue('D1', '账号余额');
        $objPHPExcel->getActiveSheet()->setCellValue('E1', '冻结金额');
        $objPHPExcel->getActiveSheet()->setCellValue('F1', '利息宝余额');
        $objPHPExcel->getActiveSheet()->setCellValue('G1', '上级用户');
        $objPHPExcel->getActiveSheet()->setCellValue('H1', '邀请码');
        $objPHPExcel->getActiveSheet()->setCellValue('I1', '注册时间');
        $objPHPExcel->getActiveSheet()->setCellValue('J1', '最后登录IP');

        //设置A列水平居中
        $objPHPExcel->setActiveSheetIndex(0)->getStyle('A')->getAlignment()
            ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        //设置单元格宽度
        $objPHPExcel->setActiveSheetIndex(0)->getColumnDimension('A')->setWidth(10);
        $objPHPExcel->setActiveSheetIndex(0)->getColumnDimension('B')->setWidth(30);


        //6.循环刚取出来的数组，将数据逐一添加到excel表格。
        for($i=0;$i<count($list);$i++){
            $objPHPExcel->getActiveSheet()->setCellValue('A'.($i+2),$list[$i]['id']);//ID
            $objPHPExcel->getActiveSheet()->setCellValue('B'.($i+2),$list[$i]['tel']);//标签码
            $objPHPExcel->getActiveSheet()->setCellValue('C'.($i+2),$list[$i]['username']);//防伪码
            $objPHPExcel->getActiveSheet()->setCellValue('D'.($i+2),$list[$i]['balance']);//防伪码
            $objPHPExcel->getActiveSheet()->setCellValue('E'.($i+2),$list[$i]['freeze_balance']);//防伪码
            $objPHPExcel->getActiveSheet()->setCellValue('F'.($i+2),$list[$i]['lixibao_balance']);//防伪码
            $objPHPExcel->getActiveSheet()->setCellValue('G'.($i+2),$list[$i]['parent_name']);//防伪码
            $objPHPExcel->getActiveSheet()->setCellValue('H'.($i+2),$list[$i]['invite_code']);//防伪码
            $objPHPExcel->getActiveSheet()->setCellValue('I'.($i+2),$list[$i]['addtime']);//防伪码
            $objPHPExcel->getActiveSheet()->setCellValue('J'.($i+2),$list[$i]['ip']);//防伪码
        }

        //7.设置保存的Excel表格名称
        $filename = 'user'.date('ymd',time()).'.xls';
        //8.设置当前激活的sheet表格名称；

        $objPHPExcel->getActiveSheet()->setTitle('sheet'); // 设置工作表名

        //8.设置当前激活的sheet表格名称；
        $objPHPExcel->getActiveSheet()->setTitle('防伪码');
        //9.设置浏览器窗口下载表格
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="'.$filename.'"');
        //生成excel文件
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        //下载文件在浏览器窗口
        $objWriter->save('php://output');
        exit;
    }

    /**
     * 实名认证
     * @auth true
     * @menu true
     */
    public function verify_auth()
    {
        $this->title = '实名认证';
        
        $query = $this->_query($this->table)->alias('u');
        $where = [];
        
        if(input('tel/s',''))$where[] = ['u.tel','like','%' . input('tel/s','') . '%'];
        if(input('username/s',''))$where[] = ['u.username','like','%' . input('username/s','') . '%'];
        if(input('status/d','') !== '')$where[] = ['u.id_status','=',input('status/d','')];
        if(input('addtime/s','')){
            $arr = explode(' - ',input('addtime/s',''));
            $where[] = ['u.addtime','between',[strtotime($arr[0]),strtotime($arr[1])]];
        }
        
        $daili = session('daili');   
        if(!empty($daili)){
            //获取直属下级
            $mobile = $daili;
            $uid = db('xy_users')->where('tel', $mobile)->value('id');
            
            $ids1  = db('xy_users')->where('parent_id', $uid)->field('id')->column('id');
            $ids1 ? $ids2  = db('xy_users')->where('parent_id','in', $ids1)->field('id')->column('id') : $ids2 = [];
            $ids2 ? $ids3  = db('xy_users')->where('parent_id','in', $ids2)->field('id')->column('id') : $ids3 = [];
            $ids3 ? $ids4  = db('xy_users')->where('parent_id','in', $ids3)->field('id')->column('id') : $ids4 = [];

            $idsAll = array_merge($ids1,$ids2,$ids3,$ids4);  //所有ids
            if(count($idsAll) > 0){
                $idsAll[0] = intval($idsAll[0]);
            }
            $where[] = ['u.id','in',$idsAll];
        }
        
        $query->field('u.id,u.tel,u.username,u.id_status,u.id_card_num as id_card,u.real_name,u.top_pic as front_pic,u.bot_pic as back_pic,u.addtime,u.auth_time,u1.username as parent_name,le.name as level_name')
            ->leftJoin('xy_users u1','u.parent_id=u1.id')
            ->leftJoin('xy_level le','u.level=le.level')
            ->group('u.id')
            ->where($where)
            ->order('u.id desc')
            ->page();
    }
    
    /**
     * 审核实名认证
     * @auth true
     */
    public function verify_auth_status()
    {
        try {
            // 直接获取参数
            $id = input('id/d', 0);
            $status = input('status/d', 1);
            
            // 简单验证
            if (empty($id)) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }
            
            // 直接更新状态
            $update = [
                'id_status' => $status,
                'auth_time' => time()
            ];
            
            $result = Db::name('xy_users')->where('id', $id)->update($update);
            
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '操作成功']);
            } else {
                return json(['code' => 0, 'msg' => '操作失败']);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
    
    /**
     * 查看身份证正反面
     * @auth true
     */
    public function view_id_card()
    {
        $id = input('get.id', 0);
        $this->userinfo = Db::table($this->table)->where('id', $id)->find();
        // 确保身份证照片字段正确映射
        if ($this->userinfo) {
            $this->userinfo['front_pic'] = $this->userinfo['top_pic'] ?? '';
            $this->userinfo['back_pic'] = $this->userinfo['bot_pic'] ?? '';
            $this->userinfo['id_card'] = $this->userinfo['id_card_num'] ?? '';
        }
        return $this->fetch();
    }

    /**
     * 会员钱包地址管理
     * @auth true
     * @menu true
     */
    public function wallet_address()
    {
        $this->title = '会员钱包地址';
        
        // 构建查询条件
        $where = [];
        if(input('tel/s','')) {
            $where[] = ['u.tel','like','%' . input('tel/s','') . '%'];
        }
        if(input('username/s','')) {
            $where[] = ['u.username','like','%' . input('username/s','') . '%'];
        }
        if(input('wallet_type/s','')) {
            $where[] = ['w.wallet_type','=',input('wallet_type/s','')];
        }
        
        // 使用原生查询确保数据正确获取
        $this->_query('xy_user_wallet')
            ->alias('w')
            ->field('w.id,w.uid,w.wallet_address,w.wallet_type,w.status,w.addtime,u.tel,u.username,IFNULL(le.name,"未设置") as level_name')
            ->leftJoin('xy_users u','w.uid=u.id')
            ->leftJoin('xy_level le','u.level=le.level')
            ->where($where)
            ->order('w.id desc')
            ->page();
    }

    /**
     * 添加钱包地址
     * @auth true
     */
    public function add_wallet_address()
    {
        if(request()->isPost()){
            $uid = input('post.uid/d');
            $wallet_address = input('post.wallet_address/s');
            $wallet_type = input('post.wallet_type/s');
            
            // 验证用户是否存在
            $user = Db::name('xy_users')->where('id', $uid)->find();
            if(!$user){
                return $this->error('用户不存在');
            }
            
            // 检查是否已存在相同的钱包地址
            $exists = Db::name('xy_user_wallet')->where([
                'uid' => $uid,
                'wallet_address' => $wallet_address
            ])->find();
            
            if($exists){
                return $this->error('该用户已存在相同的钱包地址');
            }
            
            // 开启事务，确保两个表同时更新
            Db::startTrans();
            try {
                // 添加到 xy_user_wallet 表
                $wallet_data = [
                    'uid' => $uid,
                    'wallet_address' => $wallet_address,
                    'wallet_type' => $wallet_type,
                    'status' => 1,
                    'addtime' => time()
                ];
                $result1 = Db::name('xy_user_wallet')->insert($wallet_data);
                
                // 同时更新或添加到 xy_bankinfo 表
                $bankinfo_exists = Db::name('xy_bankinfo')->where('uid', $uid)->find();
                $bankinfo_data = [
                    'uid' => $uid,
                    'username' => $user['username'],
                    'cardnum' => $wallet_address,
                    'wallet_type' => $wallet_type,
                    'tel' => $user['tel'],
                    'status' => 1
                ];
                
                if($bankinfo_exists){
                    // 更新现有记录
                    $result2 = Db::name('xy_bankinfo')->where('uid', $uid)->update($bankinfo_data);
                } else {
                    // 插入新记录
                    $result2 = Db::name('xy_bankinfo')->insert($bankinfo_data);
                }
                
                if($result1 && $result2){
                    Db::commit();
                    return $this->success('添加成功');
                } else {
                    Db::rollback();
                    return $this->error('添加失败');
                }
            } catch (\Exception $e) {
                Db::rollback();
                return $this->error('添加失败: ' . $e->getMessage());
            }
        }
        
        // 获取用户列表
        $this->users = Db::name('xy_users')->field('id,tel,username')->select();
        return $this->fetch();
    }

    /**
     * 编辑钱包地址
     * @auth true
     */
    public function edit_wallet_address()
    {
        if(request()->isPost()){
            // 验证管理员密码
            $admin_password = input('post.admin_password/s');
            $admin_info = session('admin_user'); // 修正session键名
            
            if(!$admin_info){
                return $this->error('请先登录管理员账户');
            }
            
            // 获取当前管理员信息
            $admin = Db::name('system_user')->where('id', $admin_info['id'])->find();
            if(!$admin){
                return $this->error('管理员账户不存在');
            }
            
            // 验证密码（使用系统的MD5加密方式）
            $encrypted_password = md5($admin_password);
            if($encrypted_password !== $admin['password']){
                return $this->error('管理员密码错误');
            }
            
            $id = input('post.id/d');
            $wallet_address = input('post.wallet_address/s');
            $wallet_type = input('post.wallet_type/s');
            $status = input('post.status/d', 1);
            
            // 获取当前钱包记录信息
            $wallet_info = Db::name('xy_user_wallet')->where('id', $id)->find();
            if(!$wallet_info){
                return $this->error('钱包地址不存在');
            }
            
            // 开启事务，确保两个表同时更新
            Db::startTrans();
            try {
                // 更新 xy_user_wallet 表
                $wallet_data = [
                    'wallet_address' => $wallet_address,
                    'wallet_type' => $wallet_type,
                    'status' => $status
                ];
                $result1 = Db::name('xy_user_wallet')->where('id', $id)->update($wallet_data);
                
                // 同时更新 xy_bankinfo 表（如果存在该用户的记录）
                $bankinfo_data = [
                    'cardnum' => $wallet_address,
                    'wallet_type' => $wallet_type,
                    'status' => $status
                ];
                $result2 = Db::name('xy_bankinfo')->where('uid', $wallet_info['uid'])->update($bankinfo_data);
                
                // 检查操作结果
                if($result1 !== false){
                    Db::commit();
                    return $this->success('编辑成功');
                } else {
                    Db::rollback();
                    return $this->error('编辑失败');
                }
            } catch (\Exception $e) {
                Db::rollback();
                return $this->error('编辑失败: ' . $e->getMessage());
            }
        }
        
        $id = input('get.id/d');
        $this->wallet = Db::name('xy_user_wallet')->alias('w')
            ->field('w.*,u.tel,u.username,le.name as level_name')
            ->leftJoin('xy_users u','w.uid=u.id')
            ->leftJoin('xy_level le','u.level=le.level')
            ->where('w.id', $id)
            ->find();
            
        if(!$this->wallet){
            return $this->error('钱包地址不存在');
        }
        
        return $this->fetch();
    }

    /**
     * 删除钱包地址
     * @auth true
     */
    public function delete_wallet_address()
    {
        // 验证管理员密码
        $admin_password = input('post.admin_password/s');
        $admin_info = session('admin_user'); // 修正session键名
        
        if(!$admin_info){
            return json(['code' => 0, 'info' => '请先登录管理员账户']);
        }
        
        // 获取当前管理员信息
        $admin = Db::name('system_user')->where('id', $admin_info['id'])->find();
        if(!$admin){
            return json(['code' => 0, 'info' => '管理员账户不存在']);
        }
        
        // 验证密码（使用系统的MD5加密方式）
        $encrypted_password = md5($admin_password);
        if($encrypted_password !== $admin['password']){
            return json(['code' => 0, 'info' => '管理员密码错误']);
        }
        
        $id = input('post.id/d');
        
        // 获取要删除的钱包记录信息
        $wallet_info = Db::name('xy_user_wallet')->where('id', $id)->find();
        if(!$wallet_info){
            return json(['code' => 0, 'info' => '钱包地址不存在']);
        }
        
        // 开启事务，确保两个表同时操作
        Db::startTrans();
        try {
            // 删除 xy_user_wallet 表中的记录
            $result1 = Db::name('xy_user_wallet')->where('id', $id)->delete();
            
            // 清空 xy_bankinfo 表中对应用户的钱包信息
            $result2 = Db::name('xy_bankinfo')->where('uid', $wallet_info['uid'])->update([
                'cardnum' => '',
                'wallet_type' => ''
            ]);
            
            if($result1){
                Db::commit();
                return json(['code' => 1, 'info' => '删除成功']);
            } else {
                Db::rollback();
                return json(['code' => 0, 'info' => '删除失败']);
            }
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'info' => '删除失败: ' . $e->getMessage()]);
        }
    }

}