<?php

namespace app\admin\controller;

use library\Controller;
use think\Db;

/**
 * VIP等级开关管理
 * Class VipLevelSwitch
 * @package app\admin\controller
 */
class VipLevelSwitch extends Controller
{
    /**
     * VIP等级开关列表
     * @auth true
     * @menu true
     */
    public function index()
    {
        $this->title = 'VIP等级开关管理';
        
        // 获取所有VIP等级开关配置
        $list = Db::name('xy_vip_level_switch')
            ->order('vip_level asc')
            ->select();
            
        $this->assign('list', $list);
        return $this->fetch();
    }

    /**
     * 切换VIP等级开关状态
     * @auth true
     */
    public function toggleSwitch()
    {
        $this->applyCsrfToken();
        
        $vip_level = input('post.vip_level/d', 0);
        $status = input('post.status/d', 0);
        
        if ($vip_level < 1 || $vip_level > 6) {
            return $this->error('VIP等级参数错误');
        }
        
        // 更新开关状态
        $result = Db::name('xy_vip_level_switch')
            ->where('vip_level', $vip_level)
            ->update([
                'is_enabled' => $status,
                'updated_time' => time()
            ]);
            
        if ($result !== false) {
            $status_text = $status ? '开启' : '关闭';
            return $this->success("VIP{$vip_level}任务开关已{$status_text}");
        } else {
            return $this->error('操作失败');
        }
    }

    /**
     * 批量操作VIP等级开关
     * @auth true
     */
    public function batchToggle()
    {
        $this->applyCsrfToken();
        
        $vip_levels_str = input('post.vip_levels/s', '');
        $status = input('post.status/d', 0);
        
        if (empty($vip_levels_str)) {
            return $this->error('请选择要操作的VIP等级');
        }
        
        $vip_levels = explode(',', $vip_levels_str);
        $status_text = $status ? '开启' : '关闭';
        
        // 批量更新
        $result = Db::name('xy_vip_level_switch')
            ->where('vip_level', 'in', $vip_levels)
            ->update([
                'is_enabled' => $status,
                'updated_time' => time()
            ]);
            
        if ($result !== false) {
            return $this->success("批量{$status_text}成功");
        } else {
            return $this->error('操作失败');
        }
    }

    /**
     * 获取VIP等级开关状态（API接口）
     * @auth true
     */
    public function getSwitchStatus()
    {
        $vip_level = input('get.vip_level/d', 0);
        
        if ($vip_level > 0) {
            // 获取指定VIP等级的开关状态
            $switch_info = Db::name('xy_vip_level_switch')
                ->where('vip_level', $vip_level)
                ->find();
                
            if ($switch_info) {
                // 格式化时间
                $switch_info['updated_time'] = date('Y-m-d H:i:s', $switch_info['updated_time']);
                return json(['code' => 1, 'data' => $switch_info]);
            } else {
                return json(['code' => 0, 'msg' => 'VIP等级不存在']);
            }
        } else {
            // 获取所有VIP等级的开关状态
            $switch_list = Db::name('xy_vip_level_switch')
                ->order('vip_level asc')
                ->select();
            
            // 格式化时间
            foreach($switch_list as &$item) {
                $item['updated_time'] = date('Y-m-d H:i:s', $item['updated_time']);
            }
                
            return json(['code' => 1, 'data' => $switch_list]);
        }
    }

    /**
     * 重置所有VIP等级开关（全部开启）
     * @auth true
     */
    public function resetAll()
    {
        $this->applyCsrfToken();
        
        $result = Db::name('xy_vip_level_switch')
            ->where('vip_level', 'between', [1, 6])
            ->update([
                'is_enabled' => 1,
                'updated_time' => time()
            ]);
            
        if ($result !== false) {
            return $this->success('所有VIP等级开关已重置为开启状态');
        } else {
            return $this->error('重置失败');
        }
    }

    // 兼容下划线命名的方法
    public function toggle_switch() {
        return $this->toggleSwitch();
    }
    
    public function batch_toggle() {
        return $this->batchToggle();
    }
    
    public function get_switch_status() {
        return $this->getSwitchStatus();
    }
    
    public function reset_all() {
        return $this->resetAll();
    }
} 