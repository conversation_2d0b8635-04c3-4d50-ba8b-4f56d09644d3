{extend name='main'}

{block name="content"}

<!-- 条件搜索 -->
<fieldset>
    <legend>条件搜索</legend>
    <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false" method="get" autocomplete="off">
        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">真实姓名</label>
            <div class="layui-input-inline">
                <input name="username" value="{:app('request')->get('username') ?: ''}" placeholder="请输入真实姓名" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">发起时间</label>
            <div class="layui-input-inline">
                <input data-date-range name="addtime" value="{:app('request')->get('addtime') ?: ''}" placeholder="请选择发起时间" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">类型</label>
            <div class="layui-input-inline">
                <select name="type" id="selectList">
                    <option value="">所有类型</option>
                    <option value="1">利息宝转入</option>
                    <option value="2">利息宝转出</option>
                    <option value="3">每日收益</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-inline">
            <button class="layui-btn layui-btn-primary"><i class="layui-icon">&#xe615;</i> 搜 索</button>
        </div>
    </form>
</fieldset>

<!-- 数据统计 -->
<fieldset>
    <legend>数据统计</legend>
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md3">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                <h3>总转入</h3>
                <div style="font-size: 24px; font-weight: bold;">{$lixibao_ru}</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div style="background: linear-gradient(135deg, #5FB878 0%, #009688 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                <h3>总收益</h3>
                <div style="font-size: 24px; font-weight: bold;">{$lixibao_shouyi}</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div style="background: linear-gradient(135deg, #FFB800 0%, #FF5722 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                <h3>总转出</h3>
                <div style="font-size: 24px; font-weight: bold;">{$lixibao_chu}</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div style="background: linear-gradient(135deg, #1E9FFF 0%, #3F51B5 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                <h3>净资产</h3>
                <div style="font-size: 24px; font-weight: bold;">{$lixibao_sum}</div>
            </div>
        </div>
    </div>
</fieldset>

<script>form.render()</script>

<!-- 数据表格 -->
<table class="layui-table margin-top-15" lay-skin="line">
    {notempty name='list'}
    <thead>
        <tr>
            <th class='list-table-check-td think-checkbox'>
                <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
            </th>
            <th class='text-left nowrap'>编号</th>
            <th class='text-left nowrap'>手机号</th>
            <th class='text-left nowrap'>真实姓名</th>
            <th class='text-left nowrap'>产品名称</th>
            <th class='text-left nowrap'>投资天数</th>
            <th class='text-left nowrap'>金额</th>
            <th class='text-left nowrap'>预计收益</th>
            <th class='text-left nowrap'>实际收益</th>
            <th class='text-left nowrap'>提交时间</th>
            <th class='text-left nowrap'>类型</th>
            <th class='text-left nowrap'>备注</th>
            <th class='text-left nowrap'>提取(手续费)</th>
            <th class='text-left nowrap'>状态</th>
            <th class='text-left nowrap'>操作</th>
        </tr>
    </thead>
    {/notempty}
    <tbody>
        {foreach $list as $key=>$vo}
        <tr>
            <td class='list-table-check-td think-checkbox'>
                <input class="list-check-box" value='{$vo.id}' type='checkbox'>
            </td>
            <td class='text-left nowrap'>{$vo.id}</td>
            <td class='text-left nowrap'>{$vo.tel}</td>
            <td class='text-left nowrap'>{$vo.username}</td>
            <td class='text-left nowrap'>
                <span style="color: #1890ff; font-weight: 500;">
                    {$vo.product_name|default='未知产品'}
                </span>
                {if $vo.product_rate}
                <br><small style="color: #666;">利率: {$vo.product_rate}%</small>
                {/if}
            </td>
            <td class='text-left nowrap'>
                <span style="color: #52c41a; font-weight: 500;">
                    {$vo.product_day|default=$vo.day|default='--'}天
                </span>
            </td>
            <td class='text-left nowrap'>¥{$vo.num}</td>
            <td class='text-left nowrap'>¥{$vo.yuji_num|default='0.00'}</td>
            <td class='text-left nowrap'>¥{$vo.real_num|default='0.00'}</td>
            <td class='text-left nowrap'>{:format_datetime($vo['addtime'])}</td>
            <td class='text-left nowrap'>
                {switch name="vo.type"}
                    {case value="1"}转入利息宝{/case}
                    {case value="2"}利息宝转出{/case}
                    {case value="3"}每日收益{/case}
                {/switch}
            </td>
            <td class='text-left nowrap'>
                {switch name="vo.is_sy"}
                    {case value="1"}正常收益{/case}
                    {case value="0"}理财中{/case}
                    {case value="-1"}提前提取{/case}
                {/switch}
            </td>
            <td class='text-left nowrap'>
                {switch name="vo.is_qu"}
                    {case value="1"}已提取({$vo.shouxu|default='0'}){/case}
                    {case value="0"}{/case}
                {/switch}
            </td>
            <td class='text-left nowrap'>
                {switch name="vo.status"}
                    {case value="1"}已完成{/case}
                    {case value="0"}冻结中{/case}
                {/switch}
            </td>
            <td class='text-left nowrap'>
                <a href="{:url('user_lixibao_detail',['uid'=>$vo.uid])}" class="layui-btn layui-btn-xs layui-btn-normal">
                    <i class="layui-icon layui-icon-chart"></i> 查看详情
                </a>
            </td>
        </tr>
        {/foreach}
    </tbody>
</table>

{if empty($list)}
<span class="notdata">没有记录哦</span>
{else}
{$pagehtml|raw}
{/if}

<script>
    var test = "{:app('request')->get('type') ?: '0'}";
    $("#selectList").find("option[value="+test+"]").prop("selected",true);
    form.render()
</script>

{/block}
