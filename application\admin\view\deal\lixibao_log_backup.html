{extend name='main'}

{block name="content"}
<style>
.lixibao-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}
.stat-card {
    flex: 1;
    min-width: 200px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}
.stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}
.lixibao-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}
</style>

<!-- 页面标题 -->
<div class="lixibao-title">
    <h2 style="margin: 0;">利息宝交易记录管理</h2>
    <p style="margin: 5px 0 0 0; opacity: 0.9;">管理和查看所有利息宝投资、收益和提取记录</p>
</div>

<!-- 统计数据 -->
<div class="lixibao-stats">
    <div class="stat-card">
        <div class="stat-value">¥{$lixibao_ru}</div>
        <div class="stat-label">总转入金额</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">¥{$lixibao_shouyi}</div>
        <div class="stat-label">总收益金额</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">¥{$lixibao_chu}</div>
        <div class="stat-label">总转出金额</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">¥{$lixibao_sum}</div>
        <div class="stat-label">净资产总计</div>
    </div>
</div>

<!-- 搜索表单 -->
<form class="layui-form layui-form-pane" method="get">
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">用户姓名</label>
            <div class="layui-input-inline">
                <input name="username" placeholder="请输入用户姓名" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">交易类型</label>
            <div class="layui-input-inline">
                <select name="type">
                    <option value="">全部类型</option>
                    <option value="1">转入</option>
                    <option value="2">转出</option>
                    <option value="3">收益</option>
                </select>
            </div>
        </div>
        <div class="layui-inline">
            <button class="layui-btn layui-btn-primary">查询</button>
        </div>
    </div>
</form>

<!-- 数据表格 -->
<table class="layui-table" lay-skin="line">
    <thead>
        <tr>
            <th>编号</th>
            <th>用户</th>
            <th>金额</th>
            <th>类型</th>
            <th>时间</th>
            <th>状态</th>
        </tr>
    </thead>
    <tbody>
        {foreach $list as $vo}
        <tr>
            <td>#{$vo.id}</td>
            <td>
                {$vo.username}<br>
                <small>{$vo.tel}</small>
            </td>
            <td>
                {if $vo.type == 2}
                    <span style="color: red;">-¥{$vo.num}</span>
                {else}
                    <span style="color: green;">+¥{$vo.num}</span>
                {/if}
            </td>
            <td>
                {if $vo.type == 1}
                    <span class="layui-badge layui-bg-blue">转入</span>
                {elseif $vo.type == 2}
                    <span class="layui-badge layui-bg-orange">转出</span>
                {else}
                    <span class="layui-badge layui-bg-green">收益</span>
                {/if}
            </td>
            <td>{$vo.addtime|date="Y-m-d H:i",###}</td>
            <td>
                {if $vo.status == 1}
                    <span class="layui-badge layui-bg-green">已完成</span>
                {else}
                    <span class="layui-badge">冻结中</span>
                {/if}
            </td>
        </tr>
        {/foreach}
    </tbody>
</table>

{empty name='list'}
<div style="text-align: center; padding: 40px; color: #999;">
    暂无记录
</div>
{else}
<div style="margin-top: 20px;">
    {$pagehtml|raw}
</div>
{/empty}

{/block} 