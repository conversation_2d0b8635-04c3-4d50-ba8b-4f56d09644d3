{extend name='main'}

{block name="content"}

<style>
.lixibao-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card.income { border-left-color: #28a745; }
.stat-card.in { border-left-color: #007bff; }
.stat-card.out { border-left-color: #ffc107; }
.stat-card.total { border-left-color: #6f42c1; }

.stat-icon {
    font-size: 36px;
    margin-bottom: 15px;
    opacity: 0.8;
}

.stat-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

.rules-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.rules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.rule-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.rule-title {
    font-weight: bold;
    color: #495057;
    margin-bottom: 8px;
}

.rule-desc {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.6;
}

.search-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.enhanced-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.enhanced-table .layui-table {
    margin: 0;
}

.enhanced-table .layui-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.type-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

.type-in { background: #e3f2fd; color: #1976d2; }
.type-out { background: #fff3e0; color: #f57c00; }
.type-income { background: #e8f5e8; color: #388e3c; }

.status-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status-success { background: #d4edda; color: #155724; }
.status-freeze { background: #f8d7da; color: #721c24; }

.amount-positive { color: #28a745; font-weight: 600; }
.amount-negative { color: #dc3545; font-weight: 600; }

.quick-actions {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.action-btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .rules-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- 页面标题区域 -->
<div class="lixibao-header">
    <h2 style="margin: 0; font-size: 24px;">
        <i class="layui-icon layui-icon-dollar" style="font-size: 28px; margin-right: 10px;"></i>
        利息宝交易记录管理
    </h2>
    <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">
        管理和查看所有利息宝投资、收益和提取记录，帮助用户了解投资收益情况
    </p>
</div>

<!-- 数据统计卡片 -->
<div class="stats-grid">
    <div class="stat-card in">
        <div class="stat-icon" style="color: #007bff;">
            <i class="layui-icon layui-icon-upload"></i>
        </div>
        <div class="stat-value" style="color: #007bff;">¥{$lixibao_ru}</div>
        <div class="stat-label">总转入金额</div>
    </div>
    
    <div class="stat-card income">
        <div class="stat-icon" style="color: #28a745;">
            <i class="layui-icon layui-icon-rate"></i>
        </div>
        <div class="stat-value" style="color: #28a745;">¥{$lixibao_shouyi}</div>
        <div class="stat-label">总收益金额</div>
    </div>
    
    <div class="stat-card out">
        <div class="stat-icon" style="color: #ffc107;">
            <i class="layui-icon layui-icon-download"></i>
        </div>
        <div class="stat-value" style="color: #ffc107;">¥{$lixibao_chu}</div>
        <div class="stat-label">总转出金额</div>
    </div>
    
    <div class="stat-card total">
        <div class="stat-icon" style="color: #6f42c1;">
            <i class="layui-icon layui-icon-chart"></i>
        </div>
        <div class="stat-value" style="color: #6f42c1;">¥{$lixibao_sum}</div>
        <div class="stat-label">净资产总计</div>
    </div>
</div>

<!-- 利息宝规则说明 -->
<div class="rules-section">
    <h3 style="margin: 0 0 15px 0; color: #495057;">
        <i class="layui-icon layui-icon-tips" style="color: #007bff; margin-right: 8px;"></i>
        利息宝投资规则说明
    </h3>
    
    <div class="rules-grid">
        <div class="rule-item">
            <div class="rule-title">📈 投资收益</div>
            <div class="rule-desc">
                • 投资后每天产生收益，按产品设定的日利率计算<br>
                • 收益 = 投资金额 × 日利率<br>
                • 投资期间每天定时发放收益到账户余额
            </div>
        </div>
        
        <div class="rule-item">
            <div class="rule-title">⏰ 投资期限</div>
            <div class="rule-desc">
                • 不同产品有不同的投资期限（如15天、30天等）<br>
                • 投资期间资金锁定，不可提前取出<br>
                • 到期后本金自动转回账户余额
            </div>
        </div>
        
        <div class="rule-item">
            <div class="rule-title">💰 资金流转</div>
            <div class="rule-desc">
                • 转入：将余额转入利息宝开始投资<br>
                • 收益：每日产生的投资收益<br>
                • 转出：投资到期或提前取出的资金
            </div>
        </div>
        
        <div class="rule-item">
            <div class="rule-title">📊 收益计算</div>
            <div class="rule-desc">
                • 每日收益在每天固定时间自动计算发放<br>
                • 收益直接加入账户余额，可随时使用<br>
                • 总收益 = 日利率 × 投资金额 × 投资天数
            </div>
        </div>
    </div>
</div>

<!-- 快捷操作 -->
<div class="quick-actions">
    <h4 style="margin: 0 0 15px 0; color: #495057;">
        <i class="layui-icon layui-icon-set" style="color: #28a745; margin-right: 8px;"></i>
        快捷操作
    </h4>
    <a href="/lixibao_debug.php" target="_blank" class="layui-btn layui-btn-normal action-btn">
        <i class="layui-icon layui-icon-search"></i> 诊断工具
    </a>
    <a href="/lixibao_fix_simple.php" target="_blank" class="layui-btn layui-btn-warm action-btn">
        <i class="layui-icon layui-icon-refresh"></i> 补发收益
    </a>
    <a href="/lixibao_daily_auto.php" target="_blank" class="layui-btn layui-btn-primary action-btn">
        <i class="layui-icon layui-icon-play"></i> 手动执行收益
    </a>
    <button onclick="exportData()" class="layui-btn layui-btn-normal action-btn">
        <i class="layui-icon layui-icon-export"></i> 导出数据
    </button>
</div>

<!-- 搜索条件 -->
<div class="search-section">
    <h4 style="margin: 0 0 20px 0; color: #495057;">
        <i class="layui-icon layui-icon-search" style="color: #007bff; margin-right: 8px;"></i>
        筛选条件
    </h4>
    
    <form class="layui-form layui-form-pane form-search" action="" method="get" autocomplete="off">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <div class="layui-form-item">
                    <label class="layui-form-label">用户姓名</label>
                    <div class="layui-input-block">
                        <input name="username" value="" placeholder="请输入用户姓名" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-form-item">
                    <label class="layui-form-label">交易时间</label>
                    <div class="layui-input-block">
                        <input data-date-range name="addtime" value="" placeholder="请选择时间范围" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-form-item">
                    <label class="layui-form-label">交易类型</label>
                    <div class="layui-input-block">
                        <select name="type" id="selectList">
                            <option value="">全部类型</option>
                            <option value="1">转入</option>
                            <option value="2">转出</option>
                            <option value="3">收益</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-form-item">
                    <label class="layui-form-label">&nbsp;</label>
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-primary" style="width: 100%;">
                            <i class="layui-icon layui-icon-search"></i> 筛选查询
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- 数据表格 -->
<div class="enhanced-table">
    <table class="layui-table margin-top-15" lay-skin="line">
        <thead>
        <tr>
            <th class='list-table-check-td think-checkbox'>
                <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
            </th>
            <th class='text-center nowrap'>交易编号</th>
            <th class='text-left nowrap'>用户信息</th>
            <th class='text-center nowrap'>交易金额</th>
            <th class='text-center nowrap'>产品信息</th>
            <th class='text-center nowrap'>交易时间</th>
            <th class='text-center nowrap'>交易类型</th>
            <th class='text-center nowrap'>状态信息</th>
        </tr>
        </thead>
        <tbody>
        {foreach $list as $key=>$vo}
        <tr>
            <td class='list-table-check-td think-checkbox'>
                <input class="list-check-box" value='{$vo.id}' type='checkbox'>
            </td>
            <td class='text-center nowrap'>
                <strong style="color: #495057;">#{$vo.id}</strong>
            </td>
            <td class='text-left nowrap'>
                <div style="line-height: 1.6;">
                    <div style="font-weight: 600; color: #495057;">{$vo.username}</div>
                    <div style="font-size: 12px; color: #6c757d;">{$vo.tel}</div>
                </div>
            </td>
            <td class='text-center nowrap'>
                <div style="font-size: 16px; font-weight: bold;">
                    {eq name="vo.type" value="1"}
                        <span class="amount-positive">+¥{$vo.num}</span>
                    {/eq}
                    {eq name="vo.type" value="2"}
                        <span class="amount-negative">-¥{$vo.num}</span>
                    {/eq}
                    {eq name="vo.type" value="3"}
                        <span class="amount-positive">+¥{$vo.num}</span>
                    {/eq}
                </div>
            </td>
            <td class='text-center nowrap'>
                <div style="line-height: 1.6;">
                    <div style="font-weight: 600; color: #495057;">
                        {$vo.product_name|default='标准产品'}
                    </div>
                </div>
            </td>
            <td class='text-center nowrap'>
                <div style="line-height: 1.6; font-size: 13px;">
                    <div style="color: #495057;">{$vo.addtime|date="Y-m-d",###}</div>
                    <div style="color: #6c757d;">{$vo.addtime|date="H:i:s",###}</div>
                </div>
            </td>
            <td class='text-center nowrap'>
                {eq name="vo.type" value="1"}
                    <span class="type-badge type-in">
                        <i class="layui-icon layui-icon-upload" style="font-size: 12px;"></i> 转入
                    </span>
                {/eq}
                {eq name="vo.type" value="2"}
                    <span class="type-badge type-out">
                        <i class="layui-icon layui-icon-download" style="font-size: 12px;"></i> 转出
                    </span>
                {/eq}
                {eq name="vo.type" value="3"}
                    <span class="type-badge type-income">
                        <i class="layui-icon layui-icon-rate" style="font-size: 12px;"></i> 收益
                    </span>
                {/eq}
            </td>
            <td class='text-center nowrap'>
                <div style="line-height: 1.8;">
                    {eq name="vo.status" value="1"}
                        <span class="status-badge status-success">✓ 已完成</span>
                    {/eq}
                    {eq name="vo.status" value="0"}
                        <span class="status-badge status-freeze">⏸ 冻结中</span>
                    {/eq}
                </div>
            </td>
        </tr>
        {/foreach}
        </tbody>
    </table>
</div>

{empty name='list'}
<div style="text-align: center; padding: 60px 20px; background: white; border-radius: 12px; margin-top: 20px;">
    <i class="layui-icon layui-icon-face-cry" style="font-size: 48px; color: #ccc; margin-bottom: 15px;"></i>
    <p style="color: #999; font-size: 16px; margin: 0;">暂无相关记录</p>
    <p style="color: #ccc; font-size: 14px; margin: 5px 0 0 0;">请调整筛选条件后重试</p>
</div>
{else}
<div style="background: white; border-radius: 12px; padding: 20px; margin-top: 20px;">
    {$pagehtml|raw|default=''}
</div>
{/empty}

<script>
function exportData() {
    alert('导出功能开发中...');
}

$(document).ready(function() {
    $('.amount-positive, .amount-negative').hover(function() {
        $(this).css('transform', 'scale(1.05)');
    }, function() {
        $(this).css('transform', 'scale(1)');
    });
    
    $('.type-badge, .status-badge').hover(function() {
        $(this).css({
            'transform': 'translateY(-1px)',
            'box-shadow': '0 2px 8px rgba(0,0,0,0.15)'
        });
    }, function() {
        $(this).css({
            'transform': 'translateY(0)',
            'box-shadow': 'none'
        });
    });
});
</script>

{/block} 