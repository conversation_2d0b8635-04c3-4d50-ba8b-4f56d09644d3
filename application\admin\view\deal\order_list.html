{extend name='main'}

{block name="content"}

<div class="think-box-shadow order-list-container">
    {include file='deal/order_list_search'}
    
    {notempty name='list'}
    <div class="order-cards">
        {foreach $list as $key=>$vo}
        <div class="order-card">
            <div class="order-header">
                <div class="order-id">
                    <span class="order-id-label">订单号</span>
                    <span class="order-id-value">{$vo.id}</span>
                </div>
                <div class="order-status">
                    {switch $vo.status}
                        {case 0}
                            <span class="status-badge status-pending">等待付款</span>
                        {/case}
                        {case 1}
                            <span class="status-badge status-success">完成付款</span>
                        {/case}
                        {case 2}
                            <span class="status-badge status-cancel">用户取消</span>
                        {/case}
                        {case 3}
                            <span class="status-badge status-force">强制付款</span>
                        {/case}
                        {case 4}
                            <span class="status-badge status-cancel">系统取消</span>
                        {/case}
                        {case 5}
                            <span class="status-badge status-freeze">订单冻结</span>
                        {/case}
                    {/switch}
                </div>
            </div>
            
            <div class="order-body">
                <div class="order-section user-info">
                    <div class="section-title"><i class="layui-icon layui-icon-user"></i> 用户信息</div>
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">用户名称：</span>
                            <span class="info-value">{$vo.username}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">用户余额：</span>
                            <span class="info-value"><?php echo isset(getUserInfo($vo['uid'])['balance']) ? getUserInfo($vo['uid'])['balance'] : '0' ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">今日单数：</span>
                            <span class="info-value"><?=!empty($today_num_list[$vo['id']]) ? $today_num_list[$vo['id']]['date'] . ' 第' . $today_num_list[$vo['id']]['today_num'] . '单' : '-'?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">上级用户：</span>
                            <span class="info-value"><?=!empty($today_num_list[$vo['id']]) ? $today_num_list[$vo['id']]['parent_username'] : '-'?></span>
                        </div>
                    </div>
                </div>
                
                <div class="order-section goods-info">
                    <div class="section-title"><i class="layui-icon layui-icon-cart"></i> 商品信息</div>
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">商品名称：</span>
                            <span class="info-value">{$vo.goods_name}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">商品单价：</span>
                            <span class="info-value">¥ {$vo.num / $vo.goods_count}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">购买数量：</span>
                            <span class="info-value">{$vo.goods_count}</span>
                        </div>
                    </div>
                </div>
                
                <div class="order-section money-info">
                    <div class="section-title"><i class="layui-icon layui-icon-rmb"></i> 交易金额</div>
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">交易金额：</span>
                            <span class="info-value highlight-red">¥ {$vo.num}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">佣金金额：</span>
                            <span class="info-value highlight-blue">¥ {$vo.commission}</span>
                        </div>
                    </div>
                </div>
                
                <div class="order-section time-info">
                    <div class="section-title"><i class="layui-icon layui-icon-time"></i> 交易时间</div>
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">下单时间：</span>
                            <span class="info-value">{$vo.addtime|format_datetime}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">解冻时间：</span>
                            <span class="info-value">{$vo.endtime|format_datetime}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="order-footer">
                {if $vo.status==0}
                <div class="order-actions">
                    <a data-csrf="{:systoken('admin/deal/do_user_order')}" class="action-btn success-btn" data-action="{:url('do_user_order')}" data-value="id#{$vo.id};status#3">
                        <i class="layui-icon layui-icon-ok"></i> 强制付款
                    </a>
                    <a data-csrf="{:systoken('admin/deal/do_user_order')}" class="action-btn danger-btn" data-action="{:url('do_user_order')}" data-value="id#{$vo.id};status#4">
                        <i class="layui-icon layui-icon-close"></i> 取消订单
                    </a>
                </div>
                {/if}

                {if $vo.status==5}
                <div class="order-actions">
                    <a data-csrf="{:systoken('admin/deal/jiedong')}" class="action-btn warning-btn" data-action="{:url('jiedong')}" data-value="id#{$vo.id};status#3">
                        <i class="layui-icon layui-icon-release"></i> 手动解冻
                    </a>
                </div>
                {/if}
            </div>
        </div>
        {/foreach}
    </div>
    {else}
    <div class="no-data">
        <i class="layui-icon layui-icon-face-surprised"></i>
        <p>没有相关订单记录</p>
    </div>
    {/notempty}
    
    <div class="pagination-container">
        {$pagehtml|raw|default=''}
    </div>
</div>

<style>
    .order-list-container {
        padding: 20px;
        background-color: #f8f8f8;
    }
    
    .highlight-red {
        color: #ff6b6b;
    }
    
    .highlight-blue {
        color: #1e9fff;
    }
    
    .order-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .order-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }
    
    .order-card:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: #f9f9f9;
        border-bottom: 1px solid #eee;
    }
    
    .order-id-label {
        font-size: 13px;
        color: #999;
        display: block;
        margin-bottom: 3px;
    }
    
    .order-id-value {
        font-size: 16px;
        font-weight: bold;
        color: #444;
    }
    
    .status-badge {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 13px;
        font-weight: bold;
        color: #fff;
    }
    
    .status-pending {
        background-color: #ff9800;
    }
    
    .status-success {
        background-color: #4caf50;
    }
    
    .status-cancel {
        background-color: #9e9e9e;
    }
    
    .status-force {
        background-color: #2196f3;
    }
    
    .status-freeze {
        background-color: #f44336;
    }
    
    .order-body {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 20px;
    }
    
    .order-section {
        margin-bottom: 15px;
    }
    
    .section-title {
        font-size: 15px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }
    
    .section-title .layui-icon {
        margin-right: 5px;
        color: #1e9fff;
    }
    
    .section-content {
        padding-left: 5px;
    }
    
    .info-item {
        margin-bottom: 8px;
        font-size: 14px;
        display: flex;
    }
    
    .info-label {
        color: #666;
        min-width: 90px;
    }
    
    .info-value {
        color: #333;
        flex: 1;
        word-break: break-all;
    }
    
    .order-footer {
        border-top: 1px solid #eee;
        padding: 15px 20px;
        display: flex;
        justify-content: flex-end;
    }
    
    .order-actions {
        display: flex;
        gap: 10px;
    }
    
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: bold;
        color: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
    }
    
    .action-btn .layui-icon {
        margin-right: 5px;
        font-size: 14px;
    }
    
    .success-btn {
        background-color: #4caf50;
    }
    
    .success-btn:hover {
        background-color: #3d8b40;
    }
    
    .danger-btn {
        background-color: #f44336;
    }
    
    .danger-btn:hover {
        background-color: #d32f2f;
    }
    
    .warning-btn {
        background-color: #ff9800;
    }
    
    .warning-btn:hover {
        background-color: #f57c00;
    }
    
    .no-data {
        text-align: center;
        padding: 50px 0;
        color: #999;
    }
    
    .no-data .layui-icon {
        font-size: 60px;
        margin-bottom: 10px;
    }
    
    .pagination-container {
        margin-top: 20px;
        text-align: center;
    }
    
    @media screen and (max-width: 768px) {
        .order-body {
            grid-template-columns: 1fr;
        }
        
        .order-cards {
            grid-template-columns: 1fr;
        }
    }
</style>
{/block}
