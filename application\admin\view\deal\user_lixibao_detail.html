{extend name='main'}

{block name="content"}

<style>
.user-detail-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.stats-card {
    background: white;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    border-left: 3px solid #1890ff;
    margin-bottom: 10px;
}

.stats-card.income { border-left-color: #52c41a; }
.stats-card.expected { border-left-color: #722ed1; }
.stats-card.count { border-left-color: #fa8c16; }

.stats-value {
    font-size: 20px;
    font-weight: bold;
    margin: 5px 0;
}

.stats-label {
    color: #666;
    font-size: 12px;
}

.investment-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin-bottom: 15px;
    overflow: hidden;
}

.investment-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 12px 15px;
    position: relative;
}

.status-badge {
    position: absolute;
    top: 8px;
    right: 10px;
    padding: 2px 8px;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    font-size: 11px;
}

.investment-body {
    padding: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid #f5f5f5;
    font-size: 13px;
}

.info-label {
    font-weight: 500;
    color: #333;
}

.info-value {
    color: #666;
}

.info-value.highlight {
    color: #52c41a;
    font-weight: 500;
}

.income-table {
    margin-top: 15px;
}

.income-header {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px 6px 0 0;
    border-bottom: 2px solid #52c41a;
}

.table-container {
    max-height: 250px;
    overflow-y: auto;
    border-radius: 0 0 6px 6px;
}

.section-title {
    color: #333;
    margin-bottom: 10px;
    border-bottom: 2px solid #4facfe;
    padding-bottom: 3px;
    font-size: 14px;
}

.section-title.income-section {
    border-bottom-color: #52c41a;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}
</style>

<!-- 用户信息头部 -->
<div class="user-detail-header">
    <h3 style="margin: 0; font-size: 18px;">
        <i class="layui-icon layui-icon-user" style="margin-right: 8px;"></i>
        {$user.username} - 利息宝投资详情
    </h3>
    <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 13px;">
        <i class="layui-icon layui-icon-cellphone" style="margin-right: 5px;"></i>
        手机号：{$user.tel}
    </p>
</div>

<!-- 统计数据卡片 -->
<div class="layui-row layui-col-space10">
    <div class="layui-col-md3">
        <div class="stats-card">
            <div class="stats-value" style="color: #1890ff;">¥{$total_investment}</div>
            <div class="stats-label">总投资金额</div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="stats-card income">
            <div class="stats-value" style="color: #52c41a;">¥{$total_income}</div>
            <div class="stats-label">累计收益</div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="stats-card expected">
            <div class="stats-value" style="color: #722ed1;">¥{$total_expected_income}</div>
            <div class="stats-label">预期总收益</div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="stats-card count">
            <div class="stats-value" style="color: #fa8c16;">{$investment_details|count}</div>
            <div class="stats-label">投资记录数</div>
        </div>
    </div>
</div>

<!-- 投资详情列表 -->
{foreach $investment_details as $detail}
<div class="investment-card">
    <div class="investment-header">
        <div class="status-badge">{$detail.status}</div>
        <h4 style="margin: 0; font-size: 15px;">
            <i class="layui-icon layui-icon-diamond" style="margin-right: 6px;"></i>
            {$detail.investment.product_name}
        </h4>
        <p style="margin: 6px 0 0 0; opacity: 0.9; font-size: 12px;">
            投资编号：{$detail.investment.id} | 产品类型：理财产品
        </p>
    </div>
    
    <div class="investment-body">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <h5 class="section-title">
                    <i class="layui-icon layui-icon-file" style="margin-right: 4px;"></i>
                    投资信息
                </h5>
                <div class="info-item">
                    <span class="info-label">投资金额</span>
                    <span class="info-value highlight">¥{$detail.investment.num}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">日利率</span>
                    <span class="info-value">{$detail.investment.product_rate}%</span>
                </div>
                <div class="info-item">
                    <span class="info-label">投资周期</span>
                    <span class="info-value">{$detail.total_days} 天</span>
                </div>
                <div class="info-item">
                    <span class="info-label">已进行</span>
                    <span class="info-value">{$detail.days_passed} 天</span>
                </div>
            </div>
            <div class="layui-col-md6">
                <h5 class="section-title income-section">
                    <i class="layui-icon layui-icon-chart" style="margin-right: 4px;"></i>
                    收益信息
                </h5>
                <div class="info-item">
                    <span class="info-label">累计收益</span>
                    <span class="info-value highlight">¥{$detail.total_income}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">预期总收益</span>
                    <span class="info-value">¥{$detail.expected_total_income}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">当前状态</span>
                    <span class="info-value">{$detail.status}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">完成进度</span>
                    <span class="info-value">{$detail.progress}%</span>
                </div>
            </div>
        </div>
        
        {if $detail.income_records}
        <div class="income-table">
            <div class="income-header">
                <h5 style="margin: 0; color: #333; font-size: 13px;">
                    <i class="layui-icon layui-icon-log" style="margin-right: 6px;"></i>
                    每日收益明细 ({$detail.income_records|count} 条记录)
                </h5>
            </div>
            <div class="table-container">
                <table class="layui-table" lay-skin="line" style="margin: 0;">
                    <thead>
                        <tr style="background: #fafafa;">
                            <th style="text-align: center; padding: 8px; font-size: 12px;">日期</th>
                            <th style="text-align: center; padding: 8px; font-size: 12px;">收益金额</th>
                            <th style="text-align: center; padding: 8px; font-size: 12px;">备注说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach $detail.income_records as $income}
                        <tr>
                            <td style="text-align: center; padding: 6px; font-size: 12px;">{$income.addtime|date='Y-m-d'}</td>
                            <td style="text-align: center; color: #52c41a; font-weight: 500; padding: 6px; font-size: 12px;">
                                +¥{$income.num}
                            </td>
                            <td style="text-align: center; color: #666; padding: 6px; font-size: 12px;">每日收益</td>
                        </tr>
                        {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
        {else}
        <div style="margin-top: 15px; padding: 20px; background: #f8f9fa; border-radius: 6px; text-align: center;">
            <i class="layui-icon layui-icon-chart-screen" style="font-size: 32px; color: #ddd; display: block; margin-bottom: 8px;"></i>
            <p style="color: #999; margin: 0; font-size: 12px;">暂无收益记录</p>
        </div>
        {/if}
    </div>
</div>
{/foreach}

{if empty($investment_details)}
<div class="empty-state">
    <div style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;">💰</div>
    <h4 style="color: #666; margin-bottom: 8px;">该用户暂无利息宝投资记录</h4>
    <p style="color: #999; font-size: 13px;">用户还没有进行任何利息宝投资</p>
</div>
{/if}

{/block}