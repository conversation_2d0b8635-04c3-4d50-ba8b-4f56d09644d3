{extend name='main'}

{block name="content"}
<div class="think-box-shadow">
    <form class="layui-form layui-card" action="{:request()->url()}" data-auto="true" method="post" autocomplete="off">
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label label-required">选择用户</label>
                <div class="layui-input-block">
                    <select name="uid" lay-verify="required" lay-search>
                        <option value="">请选择用户</option>
                        {foreach $users as $user}
                        <option value="{$user.id}">{$user.tel} - {$user.username}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label label-required">钱包地址</label>
                <div class="layui-input-block">
                    <input name="wallet_address" lay-verify="required" placeholder="请输入钱包地址" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label label-required">钱包类型</label>
                <div class="layui-input-block">
                    <select name="wallet_type" lay-verify="required">
                        <option value="">请选择钱包类型</option>
                        <option value="USDT-TRC20">USDT-TRC20</option>
                        <option value="USDT-ERC20">USDT-ERC20</option>
                        <option value="BTC">BTC</option>
                        <option value="ETH">ETH</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="layui-form-item text-center">
            <button class="layui-btn" type='submit'>保存数据</button>
            <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
        </div>
    </form>
</div>
{/block} 