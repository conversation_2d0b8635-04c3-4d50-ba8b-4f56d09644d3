{extend name='main'}

{block name="button"}

{if auth("add_users")}
{if $isjia == 1}
<button data-modal='{:url("set_betch_jia_password")}' data-title="批量添加假人" class='layui-btn layui-btn-normal'>批量添加假人</button>
{/if}
<button data-modal='{:url("add_users")}' data-title="添加会员" class='layui-btn'>添加会员</button>
{/if}

{/block}

{block name="content"}

<div class="think-box-shadow">
    {include file='users/index_search'}
    <table class="layui-table margin-top-15" lay-skin="line">
        {notempty name='list'}
        <thead>
        <tr>
            <th class='text-center'>会员等级</th>
            <th class='text-center'>账号</th>
            <th class='text-center'>用户名</th>
            <th class='text-center'>账户余额</th>
            <th class='text-center'>提现总额</th>
            <th class='text-center'>盈利</th>
            <th class='text-center'>冻结金额</th>
            <th class='text-center'>利息宝金额</th>
            <th class='text-center'>上级用户</th>
            <th class='text-center'>匹配区间</th>
            <th class='text-center'>邀请码</th>
            <th class='text-center'>注册时间</th>
            <th class='text-center'>最后登录IP</th>
            <th class='text-center'>信用分</th>
            <th class='text-center'>状态</th>
            <th class='text-center'>操作</th>
        </tr>
        </thead>
        {/notempty}
        <tbody>
        {foreach $list as $key=>$vo}
        <tr>
            <td class='text-center'>{$vo.level_name}</td>
            <td class='text-center'>{$vo.tel}</td>
            <td class='text-center'>{$vo.username}</td>
            <td class='text-center'>{$vo.balance}</td>
            <td class='text-center'>{$vo.deposit_sum|default='0.00'}</td>
            <td class='text-center'>{$vo.recharge_sum - $vo.deposit_sum}</td>
            <td class='text-center'>{$vo.freeze_balance}</td>
            <td class='text-center'>{$vo.lixibao_balance}</td>
            <td class='text-center'>{$vo.parent_name}</td>
            <td class='text-center'>{$vo.pipei_min}% - {$vo.pipei_max}%</td>
            <td class='text-center'>{$vo.invite_code}</td>
            <td class='text-center'>{$vo.addtime|date='Y-m-d H:i:s'}</td>
            <td class='text-center'>
                <div class="ip-address">{$vo.ip|default='-'}</div>
                <div class="ip-location">{$vo.ip_location|default='-'}</div>
            </td>
            <td class='text-center'>{$vo.credit_score}</td>
            <td class='text-center'>
                {if $vo.is_jia == 0}
                <span class="layui-badge layui-bg-orange status-btn">假人</span>
                {else}
                <span class="layui-badge layui-bg-blue status-btn">真人</span>
                {/if}
            </td>
            <td class='text-center'>
                <div class="layui-btn-group">
                    {if auth("edit_users")}
                    <a class="layui-btn layui-btn-xs layui-btn-danger" data-title="派单" data-reload="true" data-modal='{:url("admin/users/edit_users_order_setting")}?id={$vo.id}'>派单</a>
                    <a class="layui-btn layui-btn-xs" data-title="编辑" data-modal='{:url("admin/users/edit_users")}?id={$vo.id}'>编辑</a>
                    <a class="layui-btn layui-btn-xs layui-btn-warm" data-action="{:url('edit_users_ewm',['status'=>2,'id'=>$vo.id])}" data-value="id#{$vo.id};status#{$vo.invite_code}">刷新</a>
                    {/if}
                </div>
                <div class="layui-btn-group mt-5">
                {if auth("edit_users")}
                {if(!$vo.agents)}
                 <a class="layui-btn layui-btn-xs layui-btn-warm" data-action="{:url('edit_agents',['status'=>1,'id'=>$vo.id])}" data-value="id#{$vo.id};status#1">设为代理</a>
                {else}
                    <a class="layui-btn layui-btn-xs layui-btn-danger" data-action="{:url('edit_agents',['status'=>0,'id'=>$vo.id])}" data-value="id#{$vo.id};status#0">取消代理</a>
                    {/if}
                {/if}

                {if ($vo.status == 1) and auth("edit_users_status")}
                    <a class="layui-btn layui-btn-xs layui-btn-danger" data-action="{:url('edit_users_status',['status'=>2,'id'=>$vo.id])}" data-value="id#{$vo.id};status#2">禁用</a>
                {elseif ($vo.status == 2) and auth("edit_users_status")}
                    <a class="layui-btn layui-btn-xs layui-btn-normal" data-action="{:url('edit_users_status',['status'=>1,'id'=>$vo.id])}" data-value="id#{$vo.id};status#1">启用</a>
                {/if}
                </div>

                <div class="layui-btn-group mt-5">
                    {if auth("edit_users")}
                    <a class="layui-btn layui-btn-xs layui-btn-danger" onclick="del_user({$vo.id})">删除</a>
                    <a class="layui-btn layui-btn-xs layui-btn-normal" data-title="暗扣设置" data-modal='{:url("admin/users/edit_users_ankou")}?id={$vo.id}'>暗扣设置</a>
                {/if}
                </div>

                <div class="layui-btn-group mt-5">
                {if auth("tuandui")}
                    <a class="layui-btn layui-btn-xs layui-btn-danger" data-title="查看团队" data-reload="true" data-open='{:url("admin/users/tuandui")}?id={$vo.id}'>查看团队</a>
                    <a class="layui-btn layui-btn-xs layui-btn-normal" data-title="查看账变" data-reload="true" data-open='{:url("admin/users/caiwu")}?id={$vo.id}'>账变</a>
                {/if}

                {if ($vo.is_jia == 0) and auth("edit_users_status")}
                    <a class="layui-btn layui-btn-xs" data-action="{:url('edit_users_status2',['status'=>1,'id'=>$vo.id])}" data-value="id#{$vo.id};status#1">设为真人</a>
                {else}
                    <a class="layui-btn layui-btn-xs" data-action="{:url('edit_users_status2',['status'=>0,'id'=>$vo.id])}" data-value="id#{$vo.id};status#0">设为假人</a>
                {/if}
                </div>
            </td>
        </tr>
        {/foreach}
        </tbody>
    </table>
    <script>
        function del_user(id){
            layer.confirm("确认要删除吗，删除后不能恢复",{ title: "删除确认" },function(index){
                $.ajax({
                    type: 'POST',
                    url: "{:url('delete_user')}",
                    data: {
                        'id': id,
                        '_csrf_': "{:systoken('admin/users/delete_user')}"
                    },
                    success:function (res) {
                        layer.msg(res.info,{time:2500});
                        location.reload();
                    }
                });
            },function(){});
        }
    </script>

    {empty name='list'}<span class="notdata">没有记录哦</span>{else}{$pagehtml|raw|default=''}{/empty}
</div>

<style>
.layui-table th {
    font-weight: bold;
    background-color: #f2f2f2;
}
.layui-table td {
    vertical-align: middle;
}
.mt-5 {
    margin-top: 5px;
}
.layui-btn-group .layui-btn {
    margin-right: 2px;
}
.layui-btn-xs {
    padding: 0 6px;
    font-size: 12px;
    line-height: 22px;
}
/* IP地理位置显示样式 */
.ip-location {
    font-size: 11px;
    color: #666;
    margin-top: 3px;
    padding: 2px 6px;
    background-color: #f5f5f5;
    border-radius: 3px;
    display: inline-block;
    border: 1px solid #e6e6e6;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.ip-address {
    font-weight: bold;
    color: #333;
}
</style>
{/block}
