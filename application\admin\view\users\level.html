{extend name='main'}

{block name="button"}

{if auth("add_users")}
<!--<button data-modal='{:url("add_users")}' data-title="添加等级" class='layui-btn'>添加等级</button>-->
{/if}

<button data-modal='{:url("add_level")}' data-title="添加等级" class='layui-btn layui-btn-sm layui-btn-normal'>添加等级</button>

{/block}

{block name="content"}

<div class="think-box-shadow">

    <table class="layui-table margin-top-15" lay-filter="tab" lay-skin="line">
        {notempty name='list'}
        <thead>
        <tr>
            <th lay-data="{field:'id',width:60}" class='text-center'>ID</th>
            <th lay-data="{field:'name',width:100}" class='text-center'>名称</th>
            <th lay-data="{field:'pic',width:80}" class='text-center'>图标</th>
            <th lay-data="{field:'num',width:110}" class='text-center'>充值升级金额</th>
            <th lay-data="{field:'bili',width:100}" class='text-center'>佣金比例</th>
            <th lay-data="{field:'num_min',width:100}" class='text-center'>最小余额</th>
            <th lay-data="{field:'order_num',width:120}" class='text-center'>每日接单限制 <i class="layui-icon layui-icon-about daily-limit-tip" title="用户每天可接单的次数上限"></i></th>
            <th lay-data="{field:'task_enabled',width:100}" class='text-center'>任务开关</th>
            <th lay-data="{field:'tixian_ci',width:100}" class='text-center'>提现次数</th>
            <th lay-data="{field:'tixian_shouxu',width:100}" class='text-center'>提现手续费</th>
            <th lay-data="{field:'tixian_min',width:110}" class='text-center'>提现最小金额</th>
            <th lay-data="{field:'tixian_max',width:110}" class='text-center'>提现最大金额</th>
            <th lay-data="{field:'edit',width:180,fixed: 'right'}" class='text-center'>操作</th>
        </tr>
        </thead>
        {/notempty}
        <tbody>
        {foreach $list as $key=>$vo}
        <tr>
            <td class='text-center'>{$vo.id}</td>
            <td class='text-center'>{$vo.name}</td>
            <td class='text-center'><img src="{$vo.pic}" alt="" style="max-width: 50px; max-height: 50px;"></td>
            <td class='text-center'>{$vo.num}</td>
            <td class='text-center'>{$vo.bili}</td>
            <td class='text-center'>{$vo.num_min}</td>
            <td class='text-center'>{$vo.order_num} <small class="daily-text">每日</small></td>
            <td class='text-center'>
                {if $vo.task_enabled == 1}
                <button class="layui-btn layui-btn-xs layui-btn-normal task-switch-btn" data-id="{$vo.id}" data-status="1" onclick="toggleTaskSwitch('{$vo.id}', 0)">
                    <i class="layui-icon layui-icon-ok"></i> 已开启
                </button>
                {else}
                <button class="layui-btn layui-btn-xs layui-btn-danger task-switch-btn" data-id="{$vo.id}" data-status="0" onclick="toggleTaskSwitch('{$vo.id}', 1)">
                    <i class="layui-icon layui-icon-close"></i> 已关闭
                </button>
                {/if}
            </td>
            <td class='text-center'>{$vo.tixian_ci}</td>
            <td class='text-center'>{$vo.tixian_shouxu}</td>
            <td class='text-center'>{$vo.tixian_min}</td>
            <td class='text-center'>{$vo.tixian_max}</td>
            <td class='text-center'>
                {if auth("admin/users/edit_users_level")}
                <a data-dbclick class="layui-btn layui-btn-xs layui-btn-normal" data-title="编辑会员等级" data-modal='{:url("admin/users/edit_users_level")}?id={$vo.id}'>编辑</a>
                {/if}
                {if auth("admin/users/delete_level")}
                <a class="layui-btn layui-btn-xs layui-btn-danger" onClick="del_level('{$vo.id}')">删除</a>
                {/if}
            </td>
        </tr>
        {/foreach}
        </tbody>
    </table>
    <script>
        function del_level(id){
            layer.confirm("确认要删除吗，删除后不能恢复",{ title: "删除确认" },function(index){
                $.ajax({
                    type: 'POST',
                    url: "{:url('delete_level')}",
                    data: {
                        'id': id,
                        '_csrf_': "{:systoken('admin/users/delete_level')}"
                    },
                    success:function (res) {
                        layer.msg(res.info,{time:2500});
                        location.reload();
                    }
                });
            },function(){});
        }
        
        // 切换任务开关
        function toggleTaskSwitch(id, newStatus) {
            var actionText = newStatus == 1 ? '开启' : '关闭';
            layer.confirm('确定要' + actionText + '该等级的任务接单功能吗？', {
                title: '任务开关确认',
                btn: ['确定', '取消']
            }, function(index) {
                $.ajax({
                    type: 'POST',
                    url: "{:url('toggle_task_switch')}",
                    data: {
                        'id': id,
                        'status': newStatus
                    },
                    success: function(res) {
                        if(res.code == 1) {
                            layer.msg(res.info, {icon: 1, time: 2000});
                            // 更新按钮状态
                            var btn = $('button[data-id="' + id + '"]');
                            if(newStatus == 1) {
                                btn.removeClass('layui-btn-danger').addClass('layui-btn-normal');
                                btn.attr('data-status', '1');
                                btn.attr('onclick', 'toggleTaskSwitch(' + id + ', 0)');
                                btn.html('<i class="layui-icon layui-icon-ok"></i> 已开启');
                            } else {
                                btn.removeClass('layui-btn-normal').addClass('layui-btn-danger');
                                btn.attr('data-status', '0');
                                btn.attr('onclick', 'toggleTaskSwitch(' + id + ', 1)');
                                btn.html('<i class="layui-icon layui-icon-close"></i> 已关闭');
                            }
                        } else {
                            layer.msg(res.info || '操作失败', {icon: 2, time: 3000});
                        }
                    },
                    error: function() {
                        layer.msg('网络错误，请重试', {icon: 2, time: 3000});
                    }
                });
                layer.close(index);
            });
        }
        
        // 初始化提示
        $(function(){
            $('.daily-limit-tip').hover(function(){
                layer.tips('此数值表示会员每天可接单的最大次数，次日重置', this, {
                    tips: [1, '#3595CC'],
                    time: 4000
                });
            });
        });
    </script>
    <script>
        var table = layui.table;
        //转换静态表格
        var limit = Number('{$Think.get.limit}');
        if(limit==0) limit=20;
        table.init('tab', {
            cellMinWidth:80,
            skin: 'line,row',
            size: 'lg',
            limit: limit
        });
    </script>
    {empty name='list'}<span class="notdata">没有记录哦</span>{else}{$pagehtml|raw|default=''}{/empty}

</div>

<style>
.daily-limit-tip {
    cursor: pointer;
    color: #1E9FFF;
    font-size: 16px;
}
.daily-text {
    color: #FF5722;
    font-size: 12px;
    background-color: #fff4f1;
    padding: 2px 4px;
    border-radius: 2px;
}
.task-switch-btn {
    min-width: 80px;
    transition: all 0.3s ease;
}
.task-switch-btn:hover {
    transform: scale(1.05);
}
</style>
{/block}
