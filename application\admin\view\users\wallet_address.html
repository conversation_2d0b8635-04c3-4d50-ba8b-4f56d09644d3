{extend name='main'}

{block name="button"}
{if auth("add_wallet_address")}
<button data-modal='{:url("add_wallet_address")}' data-title="添加钱包地址" class='layui-btn'>添加钱包地址</button>
{/if}
{/block}

{block name="content"}
<div class="think-box-shadow">
    {include file='users/wallet_address_search'}
    <table class="layui-table margin-top-15" lay-skin="line">
        {notempty name='list'}
        <thead>
        <tr>
            <th class='text-center'>ID</th>
            <th class='text-center'>会员等级</th>
            <th class='text-center'>用户名</th>
            <th class='text-center'>账号</th>
            <th class='text-center'>钱包地址</th>
            <th class='text-center'>钱包类型</th>
            <th class='text-center'>状态</th>
            <th class='text-center'>添加时间</th>
            <th class='text-center'>操作</th>
        </tr>
        </thead>
        {/notempty}
        <tbody>
        {foreach $list as $key=>$vo}
        <tr>
            <td class='text-center'>{$vo.id}</td>
            <td class='text-center'>{$vo.level_name|default='未设置'}</td>
            <td class='text-center'>{$vo.username}</td>
            <td class='text-center'>{$vo.tel}</td>
            <td class='text-center'>
                <span class="wallet-address" title="{$vo.wallet_address}" onclick="copyToClipboard('{$vo.wallet_address}')">
                    {$vo.wallet_address|substr=0,16}...
                </span>
            </td>
            <td class='text-center'>{$vo.wallet_type}</td>
            <td class='text-center'>
                {if $vo.status == 1}
                <span class="status-badge status-active">
                    <i class="layui-icon layui-icon-ok-circle"></i> 正常
                </span>
                {else}
                <span class="status-badge status-disabled">
                    <i class="layui-icon layui-icon-close-fill"></i> 禁用
                </span>
                {/if}
            </td>
            <td class='text-center'>{$vo.addtime|date='Y-m-d H:i:s'}</td>
            <td class='text-center'>
                <div class="layui-btn-group">
                    {if auth("edit_wallet_address")}
                    <a class="layui-btn layui-btn-xs" data-title="编辑钱包地址" data-modal='{:url("edit_wallet_address")}?id={$vo.id}'>编辑</a>
                    {/if}
                    {if auth("delete_wallet_address")}
                    <a class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteWallet({$vo.id})">删除</a>
                    {/if}
                </div>
            </td>
        </tr>
        {/foreach}
        </tbody>
    </table>

    <script>
        function deleteWallet(id) {
            layer.confirm('确定要删除这个钱包地址吗？', {
                title: '删除确认',
                btn: ['确定删除', '取消']
            }, function(index) {
                layer.close(index);
                layer.prompt({
                    title: '请输入管理员登录密码',
                    formType: 1,
                    value: '',
                    area: ['350px', '120px']
                }, function(password, promptIndex) {
                    if (!password) {
                        layer.msg('请输入管理员登录密码');
                        return false;
                    }
                    
                    $.ajax({
                        type: 'POST',
                        url: "{:url('delete_wallet_address')}",
                        data: {
                            'id': id,
                            'admin_password': password,
                            '_csrf_': "{:systoken('admin/users/delete_wallet_address')}"
                        },
                        success: function(res) {
                            layer.close(promptIndex);
                            layer.msg(res.info, {time: 2500});
                            if (res.code === 1) {
                                location.reload();
                            }
                        }
                    });
                });
            });
        }

        // 复制钱包地址到剪贴板
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    layer.msg('钱包地址已复制到剪贴板', {icon: 1, time: 1500});
                }).catch(function() {
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                fallbackCopyTextToClipboard(text);
            }
        }

        // 兼容性复制方法
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.top = "-1000px";
            textArea.style.left = "-1000px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    layer.msg('钱包地址已复制到剪贴板', {icon: 1, time: 1500});
                } else {
                    layer.msg('复制失败，请手动复制', {icon: 2, time: 2000});
                }
            } catch (err) {
                layer.msg('复制失败，请手动复制', {icon: 2, time: 2000});
            }
            
            document.body.removeChild(textArea);
        }
    </script>

    {empty name='list'}<span class="notdata">没有记录哦</span>{else}{$pagehtml|raw|default=''}{/empty}
</div>

<style>
.layui-table th {
    font-weight: bold;
    background-color: #f2f2f2;
}
.layui-table td {
    vertical-align: middle;
}
.layui-btn-xs {
    padding: 0 6px;
    font-size: 12px;
    line-height: 22px;
}

/* 状态徽章样式 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.status-badge i {
    margin-right: 4px;
    font-size: 14px;
}

.status-active {
    background: linear-gradient(135deg, #52c41a, #73d13d);
    color: #fff;
    box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
}

.status-disabled {
    background: linear-gradient(135deg, #d9d9d9, #bfbfbf);
    color: #666;
    box-shadow: 0 2px 4px rgba(217, 217, 217, 0.3);
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 钱包地址样式优化 */
.wallet-address {
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wallet-address:hover {
    background: #e6f7ff;
    color: #1890ff;
}
</style>
{/block} 