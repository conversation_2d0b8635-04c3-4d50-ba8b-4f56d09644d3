{extend name="main" /}
{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <h3>VIP等级开关管理</h3>
    </div>
    <div class="layui-card-body">
        <!-- 操作按钮 -->
        <div class="layui-btn-group" style="margin-bottom: 15px;">
            <button class="layui-btn layui-btn-normal" id="batchEnable">批量开启</button>
            <button class="layui-btn layui-btn-warm" id="batchDisable">批量关闭</button>
            <button class="layui-btn layui-btn-danger" id="resetAll">全部重置</button>
        </div>
        
        <!-- VIP等级开关列表 -->
        <table class="layui-table" lay-filter="vipSwitchTable">
            <thead>
                <tr>
                    <th lay-data="{type:'checkbox', fixed: 'left'}"></th>
                    <th lay-data="{field:'vip_level', width:120, align:'center'}">VIP等级</th>
                    <th lay-data="{field:'switch_name', width:150, align:'center'}">开关名称</th>
                    <th lay-data="{field:'description', width:200}">描述</th>
                    <th lay-data="{field:'is_enabled', width:120, align:'center', templet:'#statusTpl'}">状态</th>
                    <th lay-data="{field:'updated_time', width:180, align:'center'}">更新时间</th>
                    <th lay-data="{fixed: 'right', width:150, align:'center', toolbar: '#barTpl'}">操作</th>
                </tr>
            </thead>
            <tbody id="vipSwitchList">
                <!-- 数据将通过AJAX加载 -->
            </tbody>
        </table>
    </div>
</div>

<!-- 状态模板 -->
<script type="text/html" id="statusTpl">
    {{# if(d.is_enabled == 1){ }}
        <span class="layui-badge layui-bg-green">已开启</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">已关闭</span>
    {{# } }}
</script>

<!-- 操作按钮模板 -->
<script type="text/html" id="barTpl">
    {{# if(d.is_enabled == 1){ }}
        <button class="layui-btn layui-btn-xs layui-btn-warm" lay-event="disable">关闭</button>
    {{# } else { }}
        <button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="enable">开启</button>
    {{# } }}
</script>

<script>
layui.use(['table', 'layer', 'jquery'], function(){
    var table = layui.table;
    var layer = layui.layer;
    var $ = layui.jquery;
    
    // 加载VIP开关数据
    function loadVipSwitches() {
        $.ajax({
            url: '{:url("admin/VipLevelSwitch/getSwitchStatus")}',
            type: 'GET',
            dataType: 'json',
            success: function(res) {
                if(res.code === 1) {
                    renderTable(res.data);
                } else {
                    layer.msg(res.msg || '获取数据失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    }
    
    // 渲染表格
    function renderTable(data) {
        var html = '';
        for(var i = 0; i < data.length; i++) {
            var item = data[i];
            var statusClass = item.is_enabled == 1 ? 'layui-bg-green' : 'layui-bg-gray';
            var statusText = item.is_enabled == 1 ? '已开启' : '已关闭';
            var actionBtn = item.is_enabled == 1 ? 
                '<button class="layui-btn layui-btn-xs layui-btn-warm" onclick="toggleSwitch(' + item.vip_level + ', 0)">关闭</button>' :
                '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="toggleSwitch(' + item.vip_level + ', 1)">开启</button>';
            
            html += '<tr>';
            html += '<td><input type="checkbox" name="vip_levels" value="' + item.vip_level + '" lay-skin="primary"></td>';
            html += '<td style="text-align:center;">VIP' + item.vip_level + '</td>';
            html += '<td style="text-align:center;">' + item.switch_name + '</td>';
            html += '<td>' + item.description + '</td>';
            html += '<td style="text-align:center;"><span class="layui-badge ' + statusClass + '">' + statusText + '</span></td>';
            html += '<td style="text-align:center;">' + item.updated_time + '</td>';
            html += '<td style="text-align:center;">' + actionBtn + '</td>';
            html += '</tr>';
        }
        $('#vipSwitchList').html(html);
        layui.form.render('checkbox');
    }
    
    // 切换单个开关
    window.toggleSwitch = function(vipLevel, status) {
        var actionText = status == 1 ? '开启' : '关闭';
        layer.confirm('确定要' + actionText + 'VIP' + vipLevel + '等级开关吗？', {
            btn: ['确定', '取消']
        }, function(index) {
            $.ajax({
                url: '{:url("admin/VipLevelSwitch/toggleSwitch")}',
                type: 'POST',
                data: {
                    vip_level: vipLevel,
                    status: status
                },
                dataType: 'json',
                success: function(res) {
                    if(res.code === 1) {
                        layer.msg(res.msg, {icon: 1});
                        loadVipSwitches(); // 重新加载数据
                    } else {
                        layer.msg(res.msg || '操作失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    };
    
    // 批量开启
    $('#batchEnable').click(function() {
        var checkedLevels = [];
        $('input[name="vip_levels"]:checked').each(function() {
            checkedLevels.push($(this).val());
        });
        
        if(checkedLevels.length === 0) {
            layer.msg('请选择要操作的VIP等级', {icon: 3});
            return;
        }
        
        layer.confirm('确定要批量开启选中的VIP等级开关吗？', {
            btn: ['确定', '取消']
        }, function(index) {
            $.ajax({
                url: '{:url("admin/VipLevelSwitch/batchToggle")}',
                type: 'POST',
                data: {
                    vip_levels: checkedLevels.join(','),
                    status: 1
                },
                dataType: 'json',
                success: function(res) {
                    if(res.code === 1) {
                        layer.msg(res.msg, {icon: 1});
                        loadVipSwitches();
                    } else {
                        layer.msg(res.msg || '操作失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });
    
    // 批量关闭
    $('#batchDisable').click(function() {
        var checkedLevels = [];
        $('input[name="vip_levels"]:checked').each(function() {
            checkedLevels.push($(this).val());
        });
        
        if(checkedLevels.length === 0) {
            layer.msg('请选择要操作的VIP等级', {icon: 3});
            return;
        }
        
        layer.confirm('确定要批量关闭选中的VIP等级开关吗？', {
            btn: ['确定', '取消']
        }, function(index) {
            $.ajax({
                url: '{:url("admin/VipLevelSwitch/batchToggle")}',
                type: 'POST',
                data: {
                    vip_levels: checkedLevels.join(','),
                    status: 0
                },
                dataType: 'json',
                success: function(res) {
                    if(res.code === 1) {
                        layer.msg(res.msg, {icon: 1});
                        loadVipSwitches();
                    } else {
                        layer.msg(res.msg || '操作失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });
    
    // 全部重置
    $('#resetAll').click(function() {
        layer.confirm('确定要重置所有VIP等级开关为开启状态吗？', {
            btn: ['确定', '取消']
        }, function(index) {
            $.ajax({
                url: '{:url("admin/VipLevelSwitch/resetAll")}',
                type: 'POST',
                dataType: 'json',
                success: function(res) {
                    if(res.code === 1) {
                        layer.msg(res.msg, {icon: 1});
                        loadVipSwitches();
                    } else {
                        layer.msg(res.msg || '操作失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });
    
    // 页面加载时获取数据
    loadVipSwitches();
});
</script>
{/block}