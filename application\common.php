<?php

use think\Db;
use think\facade\Lang;
use think\facade\Cookie;

// 语言自动加载函数，确保正确处理多语言环境
function load_language() {
    // 获取当前语言设置
    $lang = Cookie::get('lang', 'en-us');
    
    // 确保语言值有效
    $validLangs = ['en-us', 'mi', 'thai', 'baxi', 'moxige', 'tuerqi', 'arabic', 'zh'];
    if(!in_array($lang, $validLangs)) {
        $lang = 'en-us'; // 默认为英语
    }
    
    // 设置ThinkPHP语言
    Lang::range($lang);
    
    // 加载全局语言包
    $globalLangFile = APP_PATH . 'lang/' . $lang . '.php';
    if(file_exists($globalLangFile)) {
        Lang::load($globalLangFile);
    }
    
    // 加载模块特定语言包
    $moduleNames = ['index', 'admin', 'common']; // 所有模块名称
    foreach ($moduleNames as $module) {
        $moduleLangFile = APP_PATH . $module . '/lang/' . $lang . '.php';
        if(file_exists($moduleLangFile)) {
            Lang::load($moduleLangFile);
        }
    }
    
    // 特殊处理阿拉伯语
    if($lang == 'arabic') {
        // 设置RTL方向
        Cookie::set('direction', 'rtl', ['expire' => time()+3600*24*30]);
        Cookie::set('isArabic', 'true', ['expire' => time()+3600*24*30]);
    } else {
        Cookie::set('direction', 'ltr', ['expire' => time()+3600*24*30]);
        Cookie::set('isArabic', 'false', ['expire' => time()+3600*24*30]);
    }
    
    return $lang;
}

// 在common.php加载时自动执行语言加载
load_language();

function result($msg = 'ok', $url = false)
{
    $result = "<script>alert('$msg');";
    $url && ($result .= "    location.href='$url'");
    $result .= ' </script>';
    exit($result);
}

function getUserInfo($uid){
    return Db::name('xy_users')->where('id', $uid)->find();
}

function replace_text_lang($string){

    $item = explode(',', $string);

    foreach ($item as &$v){
        preg_replace_callback("/[\x{4e00}-\x{9fa5}]+/u", function($v) use (&$string){
            // dump($v[0]);
                    $string = str_replace($v[0], lang($v[0]), $string);
            }, $string);
            // die;
    }
    //  dump($string);die;
    return $string;
}
//支付接口
function pay_signs($data, $token){
    ksort($data);
    $str = '';
    foreach ($data as $key => $value) {
    $str .= $key . '=' . $value . '&';
    }
    return strtolower(md5($str . 'key=' . $token));
}

/*
 *  订单规则
 */
function check_order_rule($uid){
        //查询用户是否在规则内
        $user_group_rule = Db::name('xy_user_group_rule')->where('user_id', $uid)->find();
        //用户信息
        $users = Db::name('xy_users')->where('id', $uid)->find();
        //用户不在规则内 或 用户不存在
        if(is_null($user_group_rule) || is_null($users)) return false;
        //获取规则
        $order_rule = Db::name('xy_order_rule')->where('group_id', $user_group_rule['group_id'])->select();
        //用户当前接单数量
        $user_convey_num = Db::name('xy_convey')->where('uid',$uid)->count();
        //用户的等级
        // 安全处理level值，确保为数字
        $levelValue = isset($users['level']) && is_numeric($users['level']) ? (int)$users['level'] : 0;
        $user_level = $levelValue + 1;

        $return = false;

        foreach($order_rule as $k => $v){
            //达到触发等级并且单数
            if($v['level'] == $user_level && $v['order_num'] == $user_convey_num){
                $return = $v;
            }
        }
        return $return;
}

	/**
 * [ ASCII 编码 ]
 * @param array  编码数组
 * @param string 签名键名   => sign
 * @param string 密钥键名   => key
 * @param bool   签名大小写 => false(大写)
 * @param string 签名是否包含密钥 => false(不包含)
 * @return array 编码好的数组
 */
function ASCII2($asciiData, $asciiSign = 'sign', $asciiKey = 'key', $asciiSize = true, $asciiKeyBool = false)
{
    //编码数组从小到大排序
    ksort($asciiData);
    //拼接源文->签名是否包含密钥->密钥最后拼接
    $MD5str = "";
    foreach ($asciiData as $key => $val) {
        if (!$asciiKeyBool && $asciiKey == $key) continue;
        $MD5str .= $key . "=" . $val . "&";
    }
    $sign = $MD5str . $asciiKey . "=" . $asciiData[$asciiKey];
    //大小写->md5
    $asciiData[$asciiSign]  = $asciiSize ? strtoupper(md5($sign)) : strtolower(md5($sign));
    return $asciiData;
}

function request_post($url = '', $data=[]) {
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HEADER, FALSE);//不抓取头部信息。只返回数据
    curl_setopt($curl, CURLOPT_TIMEOUT,1000);//超时设置
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);//1表示不返回bool值
    curl_setopt($curl, CURLOPT_POST, TRUE);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/x-www-form-urlencoded'));//重点
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    $response = curl_exec($curl);
    if (curl_errno($curl)) {
        return curl_error($curl);
    }
    curl_close($curl);
    return $response;
}

function msectime(){
    list($msec, $sec) = explode(' ', microtime());
    $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
    return $msectime;
}

function ASCII($data){
    if (empty($data))  return false;

    ksort($data);

    $str = '';
    foreach ($data as $k => $val) {
        if(empty($val) || $k=='signType'|| $k=='sign') continue;
       $str .= $k . '=' . $val . '&';
    }
    $strs = rtrim($str, '&');

    return $strs;
}

function is_mobile($tel){
    if(preg_match("/^1[3457896]{1}\d{9}$/",$tel)){
        return true;
    }else{
        return false;
    }
}

/**
 * 字符串替换函数，用于ThinkPHP模板引擎管道操作
 * @param string $content 原内容
 * @param string $search 查找内容
 * @param string $replace 替换内容
 * @return string 替换后的内容
 */
function replace($content, $search, $replace = '')
{
    if(empty($content)) return '';
    return str_replace($search, $replace, $content);
}

/*
 * 检查图片是不是bases64编码的
 */
function is_image_base64($base64) {
    if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $base64, $result)){
        return true;
    }else{
        return false;
    }
}

function check_pic($dir,$type_img){
    $new_files = $dir.date("YmdHis"). '-' . rand(0,9999999) . "{$type_img}";
    if(!file_exists($new_files))
        return $new_files;
    else
        return check_pic($dir,$type_img);
}

/**
 * 获取数组中的某一列
 * @param array $arr 数组
 * @param string $key_name  列名
 * @return array  返回那一列的数组
 */
function get_arr_column($arr, $key_name)
{
	$arr2 = array();
	foreach($arr as $key => $val){
		$arr2[] = $val[$key_name];
	}
	return $arr2;
}

//保留两位小数
function tow_float($number){
    return (floor($number * 100) / 100);
}

//生成订单号
function getSn($head='')
{
    @date_default_timezone_set("PRC");
    $order_id_main = date('YmdHis') . mt_rand(1000, 9999);
    //唯一订单号码（YYMMDDHHIISSNNN）
    $osn = $head.substr($order_id_main,2); //生成订单号
    return $osn;
}

/**
 * 修改本地配置文件
 *
 * @param array $name   ['配置名']
 * @param array $value  ['参数']
 * @return void
 */
function setconfig($name, $value)
{
    if (is_array($name) and is_array($value)) {
        for ($i = 0; $i < count($name); $i++) {
            $names[$i] = '/\'' . $name[$i] . '\'(.*?),/';
            $values[$i] = "'". $name[$i]. "'". "=>" . "'".$value[$i] ."',";
        }
        $fileurl = APP_PATH . "../config/app.php";
        $string = file_get_contents($fileurl); //加载配置文件
        $string = preg_replace($names, $values, $string); // 正则查找然后替换
        file_put_contents($fileurl, $string); // 写入配置文件
        return true;
    } else {
        return false;
    }
}



//生成随机用户名
function get_username()
{
    $chars1 = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    $chars2 = "abcdefghijklmnopqrstuvwxyz0123456789";
    $username = "";
    for ( $i = 0; $i < mt_rand(2,3); $i++ )
    {
        $username .= $chars1[mt_rand(0,25)];
    }
    $username .='_';

    for ( $i = 0; $i < mt_rand(4,6); $i++ )
    {
        $username .= $chars2[mt_rand(0,35)];
    }
    return $username;
}

/**
 * 判断当前时间是否在指定时间段之内
 * @param integer $a 起始时间
 * @param integer $b 结束时间
 * @return boolean
 */
function check_time( $a, $b)
{
    $nowtime = time();
    $start = strtotime($a.':00:00');
    $end = strtotime($b.':00:00');

    if ($nowtime >= $end || $nowtime <= $start){
        return true;
    }else{
        return false;
    }
}

/**
*post 请求
*
*/
    function post2($url, $data = null)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($curl,CURLOPT_HTTPHEADER,array(
        'Content-Type: application/json; charset=utf-8',
    ));
    if (!empty($data)){
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
    }
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
    $output = curl_exec($curl);
    curl_close($curl);
    return $output;
}
  function get_http_type()
    {
        $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        return $http_type;
    }




