<?php

namespace app\common\service;

use think\Db;

/**
 * VIP等级服务类
 * Class VipLevelService
 * @package app\common\service
 */
class VipLevelService
{
    /**
     * 获取用户可接任务的VIP等级列表
     * @param int $user_level 用户VIP等级
     * @return array 可接任务的VIP等级数组
     */
    public static function getAvailableVipLevels($user_level)
    {
        // 获取所有开启的VIP等级开关
        $enabled_switches = Db::name('xy_vip_level_switch')
            ->where('is_enabled', 1)
            ->column('vip_level');

        // 用户可接的等级范围（从VIP1到用户当前等级）
        $user_available_levels = range(1, $user_level);

        // 取交集，只返回既在用户等级范围内又开启了开关的等级
        $available_levels = array_intersect($user_available_levels, $enabled_switches);

        return array_values($available_levels);
    }

    /**
     * 获取用户可接的任务分类
     * @param int $user_level 用户VIP等级
     * @return array 可接任务的分类ID数组
     */
    public static function getAvailableCategories($user_level)
    {
        // 获取用户可接的VIP等级列表
        $available_levels = self::getAvailableVipLevels($user_level);

        if (empty($available_levels)) {
            return [];
        }

        // 获取这些VIP等级对应的任务分类
        $query = Db::name('xy_goods_cate');

        // 检查vip_level字段是否存在
        try {
            $categories = $query->where(function($query) use ($available_levels) {
                $query->where('vip_level', 'in', $available_levels)
                      ->whereOr('vip_level', 'null')
                      ->whereOr('vip_level', 0)
                      ->whereOr('vip_level', '');
            })->column('id');
        } catch (\Exception $e) {
            // 如果vip_level字段不存在，返回所有分类
            $categories = $query->column('id');
        }

        return $categories;
    }

    /**
     * 检查用户是否可以接指定分类的任务
     * @param int $user_level 用户VIP等级
     * @param int $category_vip_level 分类要求的VIP等级
     * @return bool 是否可以接单
     */
    public static function canUserTakeCategory($user_level, $category_vip_level)
    {
        // 如果分类没有VIP等级要求，默认为VIP1
        if (!$category_vip_level) {
            $category_vip_level = 1;
        }

        // 检查用户等级是否足够
        if ($user_level < $category_vip_level) {
            return false;
        }

        // 检查该VIP等级的开关是否开启
        return self::isVipLevelEnabled($category_vip_level);
    }

    /**
     * 检查指定VIP等级是否开启
     * @param int $vip_level VIP等级
     * @return bool 是否开启
     */
    public static function isVipLevelEnabled($vip_level)
    {
        $switch_info = Db::name('xy_vip_level_switch')
            ->where('vip_level', $vip_level)
            ->find();

        return $switch_info && $switch_info['is_enabled'] == 1;
    }

    /**
     * 检查用户是否可以接指定VIP等级的任务
     * @param int $user_level 用户VIP等级
     * @param int $task_vip_level 任务VIP等级
     * @return bool 是否可以接单
     */
    public static function canUserTakeTask($user_level, $task_vip_level)
    {
        // 检查用户等级是否足够
        if ($user_level < $task_vip_level) {
            return false;
        }

        // 检查该VIP等级的开关是否开启
        return self::isVipLevelEnabled($task_vip_level);
    }

    /**
     * 获取所有VIP等级开关状态
     * @return array VIP等级开关状态数组
     */
    public static function getAllVipSwitchStatus()
    {
        $switches = Db::name('xy_vip_level_switch')
            ->order('vip_level asc')
            ->select();

        $result = [];
        foreach ($switches as $switch) {
            $result[$switch['vip_level']] = $switch['is_enabled'];
        }

        return $result;
    }

    /**
     * 过滤任务分类列表（根据VIP等级开关）
     * @param array $cate_list 任务分类列表
     * @param int $user_level 用户VIP等级
     * @return array 过滤后的任务分类列表
     */
    public static function filterTaskCategories($cate_list, $user_level)
    {
        if (empty($cate_list)) {
            return [];
        }

        $available_levels = self::getAvailableVipLevels($user_level);

        $filtered_list = [];
        foreach ($cate_list as $cate) {
            // 假设任务分类中有level字段表示VIP等级要求
            $required_level = isset($cate['level']) ? $cate['level'] : 1;

            // 检查该等级是否在可用等级列表中
            if (in_array($required_level, $available_levels)) {
                $filtered_list[] = $cate;
            }
        }

        return $filtered_list;
    }

    /**
     * 获取VIP等级开关缓存键
     * @param int $vip_level VIP等级
     * @return string 缓存键
     */
    private static function getCacheKey($vip_level = null)
    {
        if ($vip_level) {
            return "vip_switch_level_{$vip_level}";
        }
        return "vip_switch_all";
    }

    /**
     * 清除VIP等级开关缓存
     * @param int $vip_level 指定VIP等级，为空则清除所有
     */
    public static function clearCache($vip_level = null)
    {
        if ($vip_level) {
            cache(self::getCacheKey($vip_level), null);
        } else {
            // 清除所有VIP等级的缓存
            for ($i = 1; $i <= 6; $i++) {
                cache(self::getCacheKey($i), null);
            }
            cache(self::getCacheKey(), null);
        }
    }
}