<?php

namespace app\index\controller;

use think\Controller;
use think\Request;
use think\Db;

use payment\Payment;

class Ctrl extends Base
{
      //钱包页面
    public function wallet()
    {
        $balance = db('xy_users')->where('id',cookie('user_id'))->value('balance');
        $this->assign('balance',$balance);
        $balanceT = db('xy_convey')->where('uid',cookie('user_id'))->where('status',1)->sum('commission');
        $this->assign('balance_shouru',$balanceT);

        //收益
        $startDay = strtotime( date('Y-m-d 00:00:00', time()) );
        $shouyi = db('xy_convey')->where('uid',cookie('user_id'))->where('addtime','>',$startDay)->where('status',1)->select();

        //充值
        $chongzhi = db('xy_recharge')->where('uid',cookie('user_id'))->where('addtime','>',$startDay)->where('status',2)->select();

        //提现
        $tixian = db('xy_deposit')->where('uid',cookie('user_id'))->where('addtime','>',$startDay)->where('status',1)->select();

        $this->assign('shouyi',$shouyi);
        $this->assign('chongzhi',$chongzhi);
        $this->assign('tixian',$tixian);
        return $this->fetch();
    }

    // public function dd()
    // {
    //     $count = DB::table('xy_convey')->where('status','in',[1,3])->where('uid',cookie('user_id'))->count('*');
    //     $zp_count = [120,300,480];
    //     if(in_array($count,$zp_count)){
    //         Db::name('xy_users')->where('id', cookie('user_id'))->setInc('zp_num',1);
    //     }
    // }

    public function dial()
    {
        if(config('zp') == 2){
            result(lang('功能关闭'),'/index/my/index.html');
        }


        $awrad = [18.8,38.8,108.8,188,288,588];
        $pros   = [0   ,0   ,0    ,0  ,0  ,0  ];
        // $awrad = array_map(function($v){
        //     return $v.lang('元');
        // },$awrads);
        //
        $num = DB::table('xy_award')->where(['uid' => cookie('user_id')])->count('*');
        $data = [
            1,
            2,
            3,
            4,
            ];
        if(@isset($pros[$data[$num]])){
            $pros[$data[$num]] = 100;
        }else{
            $pros   = [25   ,15   ,15    ,15  ,15  ,15  ];
        }



        $pro = array_map(function($v){
            return $v.'%';
        },$pros);

        $zp_num = DB::table('xy_users')->where('id',cookie('user_id'))->value('zp_num');

        $this->assign('zp_num', $zp_num);
        $this->assign('pro', $pro);
        $this->assign('awrad',$awrad);

        return $this->fetch();
    }


    public function awrad()
    {

        $uid = cookie('user_id');
        $zp_num = DB::table('xy_users')->where('id',$uid)->value('zp_num');
        if($zp_num <= 0){
            echo json_encode(['msg' =>lang('抽奖次数不足') , 'code'=> 500]);
            exit;
        }

        $awrads = [18.8,38.8,108.8,188,288,588];
        $i = input('i');

        $num = $awrads[$i];
        // 抽奖记录
        DB::table('xy_award')->insert([
            'uid'=>$uid,
            'time'=>time(),
            'award'=>$num,
            ]);
        // 用户余额记录
        DB::table('xy_balance_log')->insert([
            'uid'=>$uid,
            'oid'=> getSn('ZP'),
            'num'=>$num,
            'type'=>87,
            'status'=>1,
            'addtime'=>time(),
            ]);
        DB::table('xy_users')->where(['id' => $uid])->setInc('balance',$num);
        DB::table('xy_users')->where(['id' => $uid])->setDec('zp_num',1);
        echo json_encode(['msg' =>lang('恭喜获得').$num , 'code'=> 200]);
        exit;
    }

// public function edit_deposit_pwd(){
//     $this->pwd2 =$r= db('xy_users')->where('id',cookie('user_id'))->value('pwd2');
//     dump($r);
//     return $this->fetch();
// }
    public function recharge_before()
    {
        $pay = db('xy_pay')->where('status',1)->select();

        $this->assign('pay',$pay);
        return $this->fetch();
    }


    public function vip()
    {
        error_reporting(0);
        $pay = db('xy_pay')->where('status',1)->select();
        $this->member_level = db('xy_level')->order('level asc')->select();;
        $this->info = db('xy_users')->where('id', cookie('user_id'))->find();
        $this->member = $this->info;

        //var_dump($this->info['level']);die;

        $level_name = $this->member_level[0]['name'];
        $order_num = $this->member_level[0]['order_num'];
        if (!empty($this->info['level'])){
            $level_name = db('xy_level')->where('level',$this->info['level'])->value('name');;
        }
        if (!empty($this->info['level'])){
            $order_num = db('xy_level')->where('level',$this->info['level'])->value('order_num');;
        }

        $this->level_name = $level_name;
        $this->order_num = $order_num;
        $this->list = $pay;
        return $this->fetch();
    }

    public function qubao()
    {
        $uinfo = db('xy_users')->field('username,tel,level,id,headpic,balance,freeze_balance,lixibao_balance,lixibao_dj_balance')->find(cookie('user_id'));
        $data = [
          "balance" => ($uinfo['balance'] + $uinfo['lixibao_balance']),
          "freeze_balance" => "0.00",
          "lixibao_balance" => ($uinfo['lixibao_balance'] - $uinfo['lixibao_balance']),
          "lixibao_dj_balance" => "0.0000"
        ];
        db('xy_lixibao')->where('uid', cookie('user_id'))->delete();
        $res = db('xy_users')->where('id', cookie('user_id'))->update($data);
        if($res){
           return 1;
        }else {
           return 2;
        }
    }

    /**
     * @地址      recharge_dovip
     * @说明      利息宝
     * @参数       @参数 @参数
     * @返回      \think\response\Json
     */
    public function lixibao()
    {
        $this->assign('title',lang('利息宝'));
        $uinfo = db('xy_users')->field('username,tel,level,id,headpic,balance,freeze_balance,lixibao_balance,lixibao_dj_balance')->find(cookie('user_id'));
        // dump($uinfo);exit;
        $this->assign('ubalance',$uinfo['balance']);
        $this->assign('balance',$uinfo['lixibao_balance']);
        $this->assign('balance_total',$uinfo['lixibao_balance'] + $uinfo['lixibao_dj_balance']);
        // $balanceT = db('xy_lixibao')->where('uid',cookie('user_id'))->where('status',1)->where('type',3)->sum('num');

        $balanceT = db('xy_balance_log')->where('uid',cookie('user_id'))->where('status',1)->where('type',23)->sum('num');

        $yes1 = strtotime( date("Y-m-d 00:00:00",strtotime("-1 day")) );
        $yes2 = strtotime( date("Y-m-d 23:59:59",strtotime("-1 day")) );
        $this->yes_shouyi = db('xy_balance_log')->where('uid',cookie('user_id'))->where('status',1)->where('type',23)->where('addtime','between',[$yes1,$yes2])->sum('num');

        $this->assign('balance_shouru',$balanceT);


        //收益
        $startDay = strtotime( date('Y-m-d 00:00:00', time()) );
        $shouyi = db('xy_lixibao')->where('uid',cookie('user_id'))->where('is_qu',0)->select();

        foreach ($shouyi as &$item) {
            $type = '';
            if ($item['type'] == 1) {
                $type = '<font color="green">'.lang('转入利息宝').'</font>';
            }elseif ($item['type'] == 2) {
                $n = $item['status'] ? lang('已到账') : lang('未到账');
                $type = '<font color="red" >'.lang('利息宝转出').'('.$n.')</font>';
            }elseif ($item['type'] == 3) {
                $type = '<font color="orange" >'.lang('每日收益').'</font>';
            }else{

            }

            $lixbao = Db::name('xy_lixibao_list')->find($item['sid']);

            $name = $lixbao['name'].'('.$lixbao['day'].lang('天)').$lixbao['bili']*100 .'% ';

            $item['num'] = number_format($item['num'],2);
            $item['name'] = $type.'　　'.$name;
            $item['shouxu'] = $lixbao['shouxu']*100 .'%';
            $item['addtime'] = date('Y/m/d H:i', $item['addtime']);

            if ($item['is_sy'] == 1) {
                $notice = lang('正常收益,实际收益').$item['real_num'];
            }else if ($item['is_sy'] == -1) {
                $notice = lang('未到期提前提取,未收益,手续费为:').$item['shouxu'];
            }else{
                $notice = lang('理财中...');
            }
            $item['notice'] =$notice;
        }

        $this->rililv = config('lxb_bili')*100 .'%' ;
        $this->shouyi=$shouyi;
        if(request()->isPost()) {
            return json(['code'=>0,'info'=>lang('操作'),'data'=>$shouyi]);
        }

        $lixibao = Db::name('xy_lixibao_list')->field('id,name,bili,day,min_num')->order('day asc')->select();
        $this->lixibao = $lixibao;
        return $this->fetch();
    }

    public function lixibao_ru()
    {

        $uid = cookie('user_id');
        $uinfo = Db::name('xy_users')->field('recharge_num,deal_time,balance,level')->find($uid);//获取用户今日已充值金额

        if(request()->isPost()){
            $count = db('xy_lixibao')->where('uid', cookie('user_id'))->find();
            $price = input('post.price/d',0);
            $id = input('post.cid/d',0);
            $yuji=0;
            if ($id) {
                $lixibao = Db::name('xy_lixibao_list')->find($id);
                if ($price < $lixibao['min_num']) {
                    return json(['code'=>1,'info'=>lang('该产品最低起投金额').$lixibao['min_num']]);
                }
                if ($price > $lixibao['max_num']) {
                    return json(['code'=>1,'info'=>lang('该产品最高可投金额').$lixibao['max_num']]);
                }
                $yuji = $price * $lixibao['bili'] * $lixibao['day'];
            }else{
                return json(['code'=>1,'info'=>lang('数据异常')]);
            }


            if ( $price <= 0 ) {
                return json(['code'=>1,'info'=>'you are sb']); //直接充值漏洞
            }
            if ($uinfo['balance'] < $price ) {
                return json(['code'=>1,'info'=>lang('可用余额不足，请充值')]);
            }
            Db::name('xy_users')->where('id',$uid)->setInc('lixibao_balance',$price);  //利息宝月 +
            Db::name('xy_users')->where('id',$uid)->setDec('balance',$price);  //余额 -

            $endtime = time() + $lixibao['day'] * 24 * 60 * 60;
            // if(!$count){
                $res = Db::name('xy_lixibao')->insert([
                    'uid'         => $uid,
                    'num'         => $price,
                    'addtime'     => time(),
                    'endtime'     => $endtime,
                    'sid'         => $id,
                    'yuji_num'         => $yuji,
                    'type'        => 1,
                    'status'      => 0,
                    'day'         => $lixibao['day'],
                    'shouxu'         => $lixibao['shouxu'],
                     'bili'         => $lixibao['bili'],
                ]);
            // }else{
            //     $res = Db::name('xy_lixibao')->where('uid', cookie('user_id'))->update([
            //         'uid'         => $uid,
            //         'num'         => ($count['num'] + $price),
            //         'addtime'     => time(),
            //         'endtime'     => $endtime,
            //         'sid'         => $id,
            //         'yuji_num'         => ($count['yuji_num'] + $yuji),
            //         'type'        => 1,
            //         'status'      => 0,
            //         'day'         => $lixibao['day'],
            //     ]);
            // }
            $oid = Db::name('xy_lixibao')->getLastInsID();
            $res1 = Db::name('xy_balance_log')->insert([
                //记录返佣信息
                'uid'       => $uid,
                'oid'       => $oid,
                'num'       => $price,
                'type'      => 21,
                'addtime'   => time()
            ]);
            if($res) {
                return json(['code'=>0,'info'=>lang('操作成功')]);
            }else{
                return json(['code'=>1,'info'=>lang('操作失败!请检查账号余额')]);
            }
        }

        $this->rililv = config('lxb_bili')*100 .'%' ;
        $this->yue = $uinfo['balance'];
        $isajax = input('get.isajax/d',0);

        if ($isajax) {
            $lixibao = Db::name('xy_lixibao_list')->field('id,name,bili,day,min_num')->select();
            $data2=[];
            $str = $lixibao[0]['name'].'('.$lixibao[0]['day'].lang('天)').$lixibao[0]['bili']*100 .'% ('.$lixibao[0]['min_num'].lang('起投)');
            foreach ($lixibao as $item) {
                $data2[] = array(
                    'id'=>$item['id'],
                    'value'=>$item['name'].'('.$item['day'].lang('天)').$item['bili']*100 .'% ('.$item['min_num'].lang('起投)'),
                );
            }
            return json(['code'=>0,'info'=>lang('操作'),'data'=>$data2,'data0'=>$str]);
        }

        $this->libi =1;

        $this->assign('title',lang('利息宝余额转入'));
        return $this->fetch();
    }


    public function deposityj()
    {
        $num = input('post.price/f',0);
        $id = input('post.cid/d',0);
        if ($id) {
            $lixibao = Db::name('xy_lixibao_list')->find($id);

            $res = $num * $lixibao['day'] * $lixibao['bili'];
            return json(['code'=>0,'info'=>lang('操作'),'data'=>$res]);
        }
    }

    public function lixibao_chu()
    {
        $uid = cookie('user_id');
        $uinfo = Db::name('xy_users')->field('recharge_num,deal_time,balance,level,lixibao_balance')->find($uid);//获取用户今日已充值金额

        if(request()->isPost()){
            // $id = input('post.id/d',0);
            $lixibao = Db::name('xy_lixibao')->where('uid',$uid)->where('is_qu',0)->select();
            if (empty($lixibao)) {
                return json(['code'=>1,'info'=>lang('没有订单')]);
            }
            foreach($lixibao as $v){
                 Db::name('xy_users')->where('id',$uid)->setDec('lixibao_balance',$v['num']);  //lixibao_balance余额 -

                //  应扣除的手续费
                  $shouxu = $v['shouxu'];
                  $price=0;
                    if ($shouxu) {
                        $price = $v['num'] - $v['num']*$shouxu;
                        $price += $v['sum_shouyi'];//加上这些天的收益
                    }

                    $res = Db::name('xy_lixibao')->where('id',$v['id'])->update([
                        'is_qu'      => 1,
                    ]);

                       Db::name('xy_users')->where('id',$uid)->setInc('balance',$price);  //余额 +
                    $res1 = Db::name('xy_balance_log')->insert([
                        //记录返佣信息
                        'uid'       => $uid,
                        'oid'       => getSn('LXBFY'),
                        'num'       => $price,
                        'type'      => 22,
                        'addtime'   => time()
                    ]);

            }








            //利息宝记录转出


            if($res) {
                return json(['code'=>0,'info'=>lang('操作成功')]);
            }else{
                return json(['code'=>1,'info'=>lang('操作失败!请检查账号余额')]);
            }

        }

        $this->assign('title',lang('利息宝余额转出'));
        $this->rililv = config('lxb_bili')*100 .'%' ;
        $this->yue = $uinfo['lixibao_balance'] ;


        $query = $this->_query('xy_lixibao')->where('uid',cookie('user_id'))->order('addtime desc');

        $start_time = input('get.start_time/s','');
        $end_time =input('get.end_time/s','');
        $is_qu = input('get.is_qu/d',2);
        $where = [];
        if( !empty($start_time) && !empty($end_time)){
        	$start_time = strtotime("{$start_time} 0:0:0" );
        	$end_time = strtotime("{$end_time} 23:59:59");

        	 $query->where("addtime >={$start_time} and addtime <= {$end_time}");
        }

        if( $is_qu !=2){
        	$query->where(['is_qu'=>$is_qu]);
        }



		$query->page();

        //return $this->fetch();
    }



    //升级vip
    public function recharge_dovip()
    {

        if(request()->isPost()){
            $level = input('post.level/d',1);
            $type = input('post.type/s','');

            $uid = cookie('user_id');
            $uinfo = db('xy_users')->field('pwd,salt,tel,username,balance')->find($uid);
            if(!$level ) return json(['code'=>1,'info'=>lang('参数错误')]);

            //
            $pay = db('xy_pay')->where('id',$type)->find();
            $level_info = db('xy_level')->where('level',$level)->find();
            $num = $level_info['num'];
            // db('xy_level')->where('level',$level)->value('num');;

            if ($num > $uinfo['balance']) {
                return json(['code'=>1,'info'=>lang('可用余额不足，请充值')]);
            }



            $id = getSn('SY');
            $res = db('xy_recharge')
                ->insert([
                    'id'        => $id,
                    'uid'       => $uid,
                    'tel'       => $uinfo['tel'],
                    'real_name' => $uinfo['username'],
                    'pic'       => '',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => $type,
                    'is_vip'    => 1,
                    'level'     =>$level
                ]);
            if($res){
                if ($type == 999) {
                    $level_validity = $level_info['validity'] > 0 ? date('Y-m-d H:i:s', time() + $level_info['validity'] * 24 * 3600) : 99;
                    $res1 = Db::name('xy_users')->where('id',$uid)->update(['level'=>$level, 'level_validity' => $level_validity]);
                    $res1 = Db::name('xy_users')->where('id',$uid)->setDec('balance',$num);
                    $res = Db::name('xy_recharge')->where('id',$id)->update(['endtime'=>time(),'status'=>2]);


                    $res2 = Db::name('xy_balance_log')
                        ->insert([
                            'uid'=>$uid,
                            'oid'=>$id,
                            'num'=>$num,
                            'type'=>30,
                            'status'=>1,
                            'addtime'=>time(),
                        ]);
                    return json(['code'=>0,'info'=>lang('升级成功')]);
                }



                $pay['id'] = $id;
                $pay['num'] =$num;
                if ($pay['name2'] == 'bipay' ) {
                    $pay['redirect'] = url('/index/Api/bipay').'?oid='.$id;
                }
                if ($pay['name2'] == 'paysapi' ) {
                    $pay['redirect'] = url('/index/Api/pay').'?oid='.$id;
                }

                if ($pay['name2'] == 'card' ) {
                    $pay['master_cardnum']= config('master_cardnum');
                    $pay['master_name']= config('master_name');
                    $pay['master_bank']= config('master_bank');
                }

                return json(['code'=>0,'info'=>$pay]);
            }

            else
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
        }
        return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
    }


    public function charge(){
        $uid = cookie('user_id');
        if(!$uid){
            return $this->redirect('User/login');
        }

        if($_POST){


            $pic = input('post.pic/s','');
            $money = input('post.money','');

            $uid = cookie('user_id');

            if (is_image_base64($pic))
                $pic = '/' . $this->upload_base64('xy',$pic);  //调用图片上传的方法
            else
                return json(['code'=>1,'info'=>lang('图片格式错误')]);


           if($money <= 0.000001){
               return json(['code'=>1,'info'=>lang('请输入数量')]);

           }
           $userData = Db::table('xy_users')->find($uid);

           $input['uid'] = $uid;
           $input['id'] = $orderId = getSn('SY');
           $input['real_name'] = $userData['username'];
           $input['tel'] = $userData['tel'];
           $input['num'] = $money;
           $input['pic'] = $pic;
           $input['addtime'] = time();

           $res = Db::table('xy_recharge')->insert($input);

            if($res){
                return json(['code'=>2,'info'=>lang('充值成功等待审核')]);
            }
            return json(['code'=>1,'info'=>lang('参数错误')]);





        }

       // $tel = Db::name('xy_users')->where('id',$uid)->value('tel');//获取用户今日已充值金额


        return $this->fetch();
    }


    public function recharge(){
        $uid = cookie('user_id');
        if(!$uid){
            return $this->redirect('User/login');
        }

        try {
            $tel = Db::name('xy_users')->where('id',$uid)->value('tel');//获取用户今日已充值金额
            $this->tel = substr_replace($tel,'****',3,4);
            $this->pay = db('xy_pay')->where('status',1)->select();

            // 记录用户访问充值页面
            $this->logUserAction($uid, 'visit_recharge_page', [
                'ip' => request()->ip(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            ]);

            return $this->fetch();
        } catch(\Exception $e) {
            // 记录错误日志
            $this->logError('recharge', $e->getMessage(), [
                'uid' => $uid,
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // 显示友好的错误页面
            return $this->error(lang('系统繁忙，请稍后再试'), 'my/index');
        }
    }

    // 记录用户行为日志
    protected function logUserAction($uid, $action, $data = []) {
        $logDir = "../runtime/user_logs/";
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = $logDir . date('Y-m-d') . '.log';
        $logData = [
            'time' => date('Y-m-d H:i:s'),
            'uid' => $uid,
            'action' => $action,
            'data' => $data
        ];

        error_log(json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", 3, $logFile);
    }

    // 记录错误日志
    protected function logError($action, $message, $data = []) {
        $logDir = "../runtime/error_logs/";
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = $logDir . date('Y-m-d') . '.log';
        $logData = [
            'time' => date('Y-m-d H:i:s'),
            'action' => $action,
            'message' => $message,
            'data' => $data,
            'request' => [
                'uri' => $_SERVER['REQUEST_URI'],
                'method' => $_SERVER['REQUEST_METHOD'],
                'ip' => request()->ip()
            ]
        ];

        error_log(json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", 3, $logFile);
    }

    public function paytm(){
        $num = input('num/f',0);

        $pay = db('xy_pay')->where('name2','paytm')->find();
         if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

        $xy_user = db('xy_users')->find($uid);
        $orderId = getSn('SY');
        $slhttp = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        $start_url="https://interface.sskking.com/pay/web";
        $merchant_key ="08ac3953933b4fc8a0ce425c424b8934";

        $mch_id = '700222101';
    	$page_url = $slhttp. $_SERVER['SERVER_NAME'];
    	$notify_url = $page_url.'/index/api/paytm_hui';
    	$mch_order_no = $orderId;
    	$pay_type ='772';
    	$trade_amount = $num;
    	$order_date = date('Y-m-d H:i:s');
    	$goods_name = 'recharge';
    	$sign_type = 'MD5';
    	$signStr = "";
    	if($goods_name != ""){
    		$signStr = $signStr."goods_name=".$goods_name."&";
    	}
    	$signStr = $signStr."mch_id=".$mch_id."&";
    	$signStr = $signStr."mch_order_no=".$mch_order_no."&";
    // 	$signStr = $signStr."mch_return_msg=".$mch_return_msg."&";
    	$signStr = $signStr."notify_url=".$notify_url."&";
    	$signStr = $signStr."order_date=".$order_date."&";


    	$signStr = $signStr."page_url=".$page_url."&";
    	$signStr = $signStr."pay_type=".$pay_type."&";
    	$signStr = $signStr."trade_amount=".$trade_amount;
    	$signStr = $signStr."&version=1.0";
        include('SignApi.php');

        $signAPI = new \SignApi;
        $sign = $signAPI->sign($signStr,$merchant_key);
        $signStr .=  "&sign_type=".$sign_type;
        $signStr .= '&sign='.$sign;
        $result = $signAPI->http_post_res($start_url, $signStr);
        $repones = json_decode($result, true);
        if(!$result){
            $url = '/index/ctrl/recharge';
        }else{
            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'paytm'
                ]);
            $url = $repones['payInfo'];
        }
        return $this->redirect($url);
    }

    public function kbpay(){
        try {
            $num = sprintf('%.2f', input('num/f',0));

            // 参数验证
            if (!$num || $num <= 0) {
                return json(['code'=>1,'info'=>lang('充值金额必须大于0')]);
            }

            // 费率验证
            $pay = db('xy_pay')->where('name2','kbpay')->find();
            if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
            if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

            $uid = cookie('user_id');
            if (!$uid) {
                return json(['code'=>1,'info'=>lang('请先登录')]);
            }

            $xy_user = db('xy_users')->find($uid);
            if (!$xy_user) {
                return json(['code'=>1,'info'=>lang('用户信息不存在')]);
            }

            // 生成充值订单号
            $orderId = getSn('SY');

            $start_url = 'https://pay.3kbpay.com/india/recharge';
            $mch_id = '12135';
            $merchant_key ="c629e8609ce7ca20c91c2f2d52cd91bd";

            // 构建支付参数
            $page_url = 'https://'. $_SERVER['SERVER_NAME'];
            $notify_url = $page_url.'/index/api/kbpay_hui';

            $data['merchant_id'] = $mch_id;
            $data['pay_type'] = '101';
            $data['order_id'] = $orderId;
            $data['amount'] = $num;
            $data['notify_url'] = $notify_url;
            $data['redirect_url'] = $page_url;

            // 记录请求日志
            $this->logApiRequest('kbpay', $orderId, $data);

            include('SignApi.php');
            $signAPI = new \SignApi;
            $sign = $signAPI->sign(ASCII($data), $merchant_key);

            $data['sign'] = $sign;

            // 发送请求到支付网关
            $result = post2($start_url, json_encode($data));
            if (!$result) {
                $this->logApiError('kbpay', $orderId, '支付网关返回空响应');
                return json(['code'=>1,'info'=>lang('支付网关连接失败，请稍后再试')]);
            }

            $repones = json_decode($result, true);
            if (!$repones) {
                $this->logApiError('kbpay', $orderId, '支付网关返回格式错误', $result);
                return json(['code'=>1,'info'=>lang('支付网关返回格式错误，请联系客服')]);
            }

            if($repones['status'] != '0'){
                $this->logApiError('kbpay', $orderId, $repones['message'], $repones);
                return json(['code'=>1,'info'=>$repones['message'] ?: lang('创建支付订单失败')]);
            }

            // 数据库新增订单记录
            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'kbpay'
                ]);

            if (!$res) {
                $this->logApiError('kbpay', $orderId, '创建本地订单失败');
                return json(['code'=>1,'info'=>lang('创建订单失败，请稍后再试')]);
            }

            // 记录成功日志
            $this->logApiSuccess('kbpay', $orderId, [
                'redirect_url' => $repones['data']['pay_url'],
                'amount' => $num
            ]);

            return $this->redirect($repones['data']['pay_url']);

        } catch (\Exception $e) {
            // 记录异常
            $this->logApiError('kbpay', isset($orderId) ? $orderId : 'ERROR', '支付处理异常: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // 返回友好错误信息
            return json(['code'=>1,'info'=>lang('支付处理异常，请稍后再试')]);
        }
    }

    // API请求日志
    protected function logApiRequest($api, $orderId, $data = []) {
        $logDir = "../runtime/api_logs/";
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = $logDir . date('Y-m-d') . '_requests.log';
        $logData = [
            'time' => date('Y-m-d H:i:s'),
            'api' => $api,
            'order_id' => $orderId,
            'data' => is_array($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : $data,
            'ip' => request()->ip(),
            'ua' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''
        ];

        error_log(json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", 3, $logFile);
    }

    // API错误日志
    protected function logApiError($api, $orderId, $message, $data = null) {
        $logDir = "../runtime/api_logs/";
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = $logDir . date('Y-m-d') . '_errors.log';
        $logData = [
            'time' => date('Y-m-d H:i:s'),
            'api' => $api,
            'order_id' => $orderId,
            'message' => $message,
            'data' => $data ? (is_array($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : $data) : null,
            'ip' => request()->ip(),
            'ua' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''
        ];

        error_log(json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", 3, $logFile);
    }

    // API成功日志
    protected function logApiSuccess($api, $orderId, $data = []) {
        $logDir = "../runtime/api_logs/";
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = $logDir . date('Y-m-d') . '_success.log';
        $logData = [
            'time' => date('Y-m-d H:i:s'),
            'api' => $api,
            'order_id' => $orderId,
            'data' => is_array($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : $data
        ];

        error_log(json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", 3, $logFile);
    }

    public function stepay(){
                $num = sprintf('%.2f', input('num/f',0));


        //
        $pay = db('xy_pay')->where('name2','stepay')->find();
         if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

        $xy_user = db('xy_users')->find($uid);
            $orderId = getSn('SY');

            $start_url = 'https://pay.stepay.xyz/generate/payment';
            $mch_id = 'aa112233';
            $merchant_key ="d2b474887c9e14f0de71fbf5adf08994";


        //     $mch_id = '12002';
        // 	$merchant_key = 'a5938ded6843fc86568deb452a5da697';


// 参数名	必选	类型	说明
// mch_id	是	string	商户号
// notify_url	是	string	后台通知地址
// page_url	是	string	前台通知地址
// mch_order_no	是	string	商家订单号
// pay_type	是	string	支付类型 查阅商户后台通道编码
// currency	是	string	货币代码 查阅商户后台首页币种
// trade_amount	是	float	交易金额 保留2位小数
// order_date	是	string	订单日期 可以固定一个值 格式 Y-m-d H:i:s 2020-12-12 15:15:15
// goods_name	是	string	商品名称 可以固定一个值
// payer_ip	是	string	客户ip 可以默认 127.0.0.1
// sign	是	string	签名值


        	$page_url = get_http_type(). $_SERVER['SERVER_NAME'];
        	$notify_url = $page_url.'/index/api/stepay_hui';

            $data['mch_id'] = $mch_id;
            $data['pay_type'] = '28';
            $data['mch_order_no'] = $orderId;
            $data['trade_amount'] = $num;
            $data['notify_url'] = $notify_url;
            $data['page_url'] = $page_url;
            $data['currency'] = 'BRL';
            $data['order_date'] = date('Y-m-d H:i:s');
            $data['goods_name'] = 'recharge';
            $data['payer_ip'] = '127.0.0.1';
            // $signStr = ASCII($data);

            include('SignApi.php');
            // $signStr = 'merchantId='.$mch_id.'&merchantOrderId='.$orderId.'&amount='.$num.'&'.$merchant_key;
            $signAPI = new \SignApi;
	        $sign = $signAPI->sign(ASCII($data), $merchant_key);

	        $data['sign'] = $sign;

            $result = post2($start_url, json_encode($data));
            $repones = json_decode($result, true);

            if($repones['code'] != '0'){
                return json(['code'=>1,'info'=>$repones['message']]);
            }

            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'stepay'
                ]);

            return $this->redirect($repones['data']['pay_url']);
    }

    public function rpay(){
        $num = sprintf('%.2f', input('num/f',0));


        //
        $pay = db('xy_pay')->where('name2','rpay')->find();
         if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

        $xy_user = db('xy_users')->find($uid);
            $orderId = getSn('SY');

            $start_url = 'https://rpay.cash/rpay-api/order/submit';
            $mch_id = '20119';
            $merchant_key ="T4ftvcj1OyBomqqA";
        //     $mch_id = '999';
        // 	$merchant_key = 'abc#123!';


        	$page_url = 'https://'. $_SERVER['SERVER_NAME'];
        	$notify_url = $page_url.'/index/api/rpay_hui';

            $data['merchantId'] = $mch_id;
            $data['merchantOrderId'] = $orderId;
            $data['amount'] = $num;
            $data['timestamp'] = msectime();
            $data['payType'] = 1;
            $data['notifyUrl'] = $notify_url;
            $data['callbackUrl'] = $page_url;
            $data['remark'] = 'recharge';

            // $signStr = ASCII($data);

        //     include('SignApi.php');
            $signStr = 'merchantId='.$mch_id.'&merchantOrderId='.$orderId.'&amount='.$num.'&'.$merchant_key;
        //     $signAPI = new \SignApi;
	       // $sign = $signAPI->sign($signStr, $merchant_key);

	        $data['sign'] = md5($signStr);

            $result = post2($start_url, json_encode($data));
            $repones = json_decode($result, true);
// dump($repones);die;
            if($repones['code'] != '0'){
                return json(['code'=>1,'info'=>'error']);
            }

            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'rpay'
                ]);

            return $this->redirect($repones['data']['h5Url']);
    }

    public function kingpay(){
        $num = input('num/f',0);


        //
        $pay = db('xy_pay')->where('name2','kingpay')->find();
         if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

        $xy_user = db('xy_users')->find($uid);
            $orderId = getSn('SY');

            $start_url = 'https://interface.sskking.com/pay/web';

            $merchant_key ="TGMVFUGCQ1EZ2PRCZXIBTSMXWNV4HTOJ";

        	$mch_id = '*********';
        	$page_url = 'https://'. $_SERVER['SERVER_NAME'];
        	$notify_url = $page_url.'/index/api/sskking_hui';
        	$mch_order_no = $orderId;
        	$pay_type ='102';
        	$trade_amount = $num;
        	$order_date = date('Y-m-d H:i:s');
        // 	$bank_code = $_POST["bank_code"];
        	$goods_name = 'recharge';
        	$sign_type = 'MD5';
        // 	$mch_return_msg = $_POST["mch_return_msg"];
        	$signStr = "";
        	if($goods_name != ""){
        		$signStr = $signStr."goods_name=".$goods_name."&";
        	}
        	$signStr = $signStr."mch_id=".$mch_id."&";
        	$signStr = $signStr."mch_order_no=".$mch_order_no."&";
        // 	$signStr = $signStr."mch_return_msg=".$mch_return_msg."&";
        	$signStr = $signStr."notify_url=".$notify_url."&";
        	$signStr = $signStr."order_date=".$order_date."&";


        	$signStr = $signStr."page_url=".$page_url."&";
        	$signStr = $signStr."pay_type=".$pay_type."&";
        	$signStr = $signStr."trade_amount=".$trade_amount;
        	$signStr = $signStr."&version=1.0";
            include('SignApi.php');

            $signAPI = new \SignApi;
	        $sign = $signAPI->sign($signStr,$merchant_key);
	        $signStr .=  "&sign_type=".$sign_type;
	        $signStr .= '&sign='.$sign;
	       // goods_name=recharge&mch_id=111222166&mch_order_no=SY2108182055385864&notify_url=https://indeed-2.com/index/user/sskking_hui&order_date=2021-08-18 20:55:38&page_url=https://indeed-1.com&pay_type=102&trade_amount=500&goods_name=recharge&mch_id=111222166&mch_order_no=SY2108182055385864&notify_url=https://indeed-2.com/index/user/sskking_hui&order_date=2021-08-18 20:55:38&page_url=https://indeed-1.com&pay_type=102&trade_amount=500&sign=09af421eabc4d88a754b671338485940

            $result = $signAPI->http_post_res($start_url, $signStr);
            $repones = json_decode($result, true);
            // dump($repones);die;
            if($repones['respCode'] != 'SUCCESS'){
                return json(['code'=>1,'info'=>'error']);
            }

            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'kingpay'
                ]);
            return $this->redirect($repones['payInfo']);
    }

    public function recharge_do_before()
    {
        $num = input('post.price/f',0);
        $type = input('post.type/s','card');

        $uid = cookie('user_id');
        if(!$num ) return json(['code'=>1,'info'=>lang('参数错误')]);

        //时间限制 //TODO
        $res = check_time(config('chongzhi_time_1'),config('chongzhi_time_2'));
        $str = config('chongzhi_time_1').":00  - ".config('chongzhi_time_2').":00";
        if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!')]);


        //
        $pay = db('xy_pay')->where('name2',$type)->find();
        if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

           $uid = cookie('user_id');
           $dbs = Db::table('xy_users')->where('id',$uid)->find();
            $ids = getSn('SY');
           $chongzhi_type=config('chongzhi_type');
           if($chongzhi_type==1){
                $uinfo = db('xy_users')->field('pwd,salt,tel,username')->find($uid);

                $res = db('xy_recharge')
                    ->insert([
                        'id'        => $ids,
                        'uid'       => $uid,
                        'tel'       => $uinfo['tel'],
                        'real_name' => $uinfo['username'],
                        'num'       => $num,
                        'status' => 1,
                        'addtime'   => time(),

                    ]);
               $arr=[
                    'mch_id'=>*********, //商户id
                    'mch_order_no'=>$ids, //订单编号
                    'trade_amount'=>$num,//金额
                    'order_date'=>date('Y-m-d H:i:s',time()),//时间
                    'bank_code'=>'IDPT0001',//收款银行代码
                    'pay_type'=>122,
                    "goods_name"=>"充值",
                    'notify_url'=>get_http_type().$_SERVER['SERVER_NAME'].'/index/user/hui2',//回调地址
                    'key'=>'c11a7f310a464d698f9f2ba378b9df4f',
                ];
                $data=$this->ASCII($arr,'sign','key',false,false);
                $data['sign_type']="MD5";
                unset($data['key']);
                $formItemString='';
                $formItemString.="<form style='display:none' name='submit_form' id='submit_form' action='https://pay.sepropay.com/sepro/pay/web' method='post'>";
foreach($data as $key=>$value){
    $formItemString.="<input name='{$key}' type='text' value='{$value}'/>";
}
$formItemString.="</form>";

return json(['code'=>0,'info'=>'Jumping to third party payment','data'=>$formItemString]);
           }else{
               $arr=[
                    "userid"=>"amazon",
                    "orderid"=>$ids,
                    "type"=>"razorpay",
                    "amount"=>$num,
                    "notifyurl"=>get_http_type().$_SERVER['SERVER_NAME'].'/index/user/hui',
                    "returnurl"=>get_http_type().$_SERVER['SERVER_NAME'].'/index/my/index',
                ];
                $arr['sign']=md5("33133a3d-a2e4-43c6-8332-1287219c04cf".$ids.$num);
                $arr=json_encode($arr);
                $data=post2('https://api.zf77777.org/api/create',$arr);
                $data=json_decode($data,true);
                if($data['success']==1){
                    $uinfo = db('xy_users')->field('pwd,salt,tel,username')->find($uid);
                     $res = db('xy_recharge')
                     ->insert([
                         'id'        => $ids,
                         'uid'       => $uid,
                         'tel'       => $uinfo['tel'],
                         'real_name' => $uinfo['username'],
                         'num'       => $num,
                         'status' => 1,
                         'addtime'   => time()
                         ]);
                    return json(['code'=>2,'info'=>'Jumping to third party payment','url'=>$data['pageurl']]);
                }else{
                     return json(['code'=>1,'info'=>'Please contact customer service if payment fails']);
                }
        //     $res =new Payment();
        //     $datas = $res->ds_pay($channelName="UPI",$orderNo= $ids ,$payMoney="$num",$productDetail="1234",$name="$dbs[username]",$email="<EMAIL>"
        //     ,$phone="$dbs[tel]",$redirectUrl=get_http_type().$_SERVER['SERVER_NAME'].'/index/user/hui',$errorReturnUrl="www.baidu.com",$notifyUrl=get_http_type().$_SERVER['SERVER_NAME'].'/index/user/hui');
        //   $jiekuo = post2('http://11128.in:18088/api/international/pay',$datas['post']);
        //   $jieguo = json_decode($jiekuo);

        //   if($jieguo->code == '00'){


        //         $uid = cookie('user_id');
        //         $uinfo = db('xy_users')->field('pwd,salt,tel,username')->find($uid);

        //         $res = db('xy_recharge')
        //             ->insert([
        //                 'id'        => $ids,
        //                 'uid'       => $uid,
        //                 'tel'       => $uinfo['tel'],
        //                 'real_name' => $uinfo['username'],
        //                 'num'       => $num,
        //                 'status' => 1,
        //                 'addtime'   => time(),

        //             ]);
        //      // header("Location: $jieguo->backUrl");
        //       return json(['code'=>2,'info'=>'Jumping to third party payment','url'=>$jieguo->backUrl]);
        //       // die;
        //   }else{
        //          return json(['code'=>1,'info'=>'Please contact customer service if payment fails']);
        //   }
        }

    }



    public function recharge5_do_before()
    {
        $num = input('post.price/f',0);
        $type = input('post.type/s','card');

        $uid = cookie('user_id');
        if(!$num ) return json(['code'=>1,'info'=>lang('参数错误')]);

        //时间限制 //TODO
        $res = check_time(config('chongzhi_time_1'),config('chongzhi_time_2'));
        $str = config('chongzhi_time_1').":00  - ".config('chongzhi_time_2').":00";
        if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!')]);


        //
        $pay = db('xy_pay')->where('name2',$type)->find();
        if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $info = [];
        $info['num'] = $num;
        return json(['code'=>0,'info'=>$info]);
    }
    public function recharge6_do_before()
    {
        $num = input('post.price/f',0);
        $type = input('post.type/s','card');

        $uid = cookie('user_id');
        if(!$num ) return json(['code'=>1,'info'=>lang('参数错误')]);

        //时间限制 //TODO
        $res = check_time(config('chongzhi_time_1'),config('chongzhi_time_2'));
        $str = config('chongzhi_time_1').":00  - ".config('chongzhi_time_2').":00";
        if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!')]);


        //
        $pay = db('xy_pay')->where('name2',$type)->find();
        if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $info = [];
        $info['num'] = $num;
        return json(['code'=>0,'info'=>$info]);
    }

    public function recharge2()
    {
        $oid = input('get.oid/s','');
        $num = input('get.num/s','');
        $type = input('get.type/s','');
        $this->pay = db('xy_pay')->where('status',1)->where('name2',$type)->find();
        if(request()->isPost()) {
            $id = input('post.id/s', '');
            $pic = input('post.pic/s', '');

            if (is_image_base64($pic)) {
                $pic = '/' . $this->upload_base64('xy', $pic);  //调用图片上传的方法
            }else{
                return json(['code'=>1,'info'=>lang('图片格式错误')]);
            }

            $res = db('xy_recharge')->where('id',$id)->update(['pic'=>$pic]);
            if (!$res) {
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
            }else{
                return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
            }
        }


        $info = [];//db('xy_recharge')->find($oid);
        $info['num'] = $num;//db('xy_recharge')->find($oid);

        $this->bank = Db::name('xy_bank')->where('status', 1)->find();

        /*

        */

         $info['master_bank'] = config('master_bank');//银行名称
        $info['master_name'] = config('master_name');//收款人
        $info['master_cardnum'] = config('master_cardnum');//银行卡号
        $info['master_bk_address'] = config('master_bk_address');//银行地址
        $this->info = $info;

        return $this->fetch();
    }
    public function recharge55()
    {
        $oid = input('get.oid/s','');
        $num = input('get.num/s','');
        $type = input('get.type/s','');
        $this->pay = db('xy_pay')->where('status',1)->where('name2',$type)->find();
        if(request()->isPost()) {
            $id = input('post.id/s', '');
            $pic = input('post.pic/s', '');

            if (is_image_base64($pic)) {
                $pic = '/' . $this->upload_base64('xy', $pic);  //调用图片上传的方法
            }else{
                return json(['code'=>1,'info'=>lang('图片格式错误')]);
            }

            $res = db('xy_recharge')->where('id',$id)->update(['pic'=>$pic]);
            if (!$res) {
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
            }else{

                return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
            }
        }

        $num = $num.'.'.rand(10,99); //随机金额
        $info = [];//db('xy_recharge')->find($oid);
        $info['num'] = $num;//db('xy_recharge')->find($oid);

        $this->bank = Db::name('xy_bank')->where('status', 1)->select();

        /*
        $info['master_bank'] = config('master_bank');//银行名称
        $info['master_name'] = config('master_name');//收款人
        $info['master_cardnum'] = config('master_cardnum');//银行卡号
        $info['master_bk_address'] = config('master_bk_address');//银行地址
        */


        $this->info = $info;

        return $this->fetch();
    }
    public function recharge66()
    {
        $oid = input('get.oid/s','');
        $num = input('get.num/s','');
        $type = input('get.type/s','');
        $this->pay = db('xy_pay')->where('status',1)->where('name2',$type)->find();
        if(request()->isPost()) {
            $id = input('post.id/s', '');
            $pic = input('post.pic/s', '');

            if (is_image_base64($pic)) {
                $pic = '/' . $this->upload_base64('xy', $pic);  //调用图片上传的方法
            }else{
                return json(['code'=>1,'info'=>lang('图片格式错误')]);
            }

            $res = db('xy_recharge')->where('id',$id)->update(['pic'=>$pic]);
            if (!$res) {
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
            }else{

                return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
            }
        }

        $num = $num.'.'.rand(10,99); //随机金额
        $info = [];//db('xy_recharge')->find($oid);
        $info['num'] = $num;//db('xy_recharge')->find($oid);

        $this->bank = Db::name('xy_bank')->where('status', 1)->select();

        /*
        $info['master_bank'] = config('master_bank');//银行名称
        $info['master_name'] = config('master_name');//收款人
        $info['master_cardnum'] = config('master_cardnum');//银行卡号
        $info['master_bk_address'] = config('master_bk_address');//银行地址
        */


        $this->info = $info;

        return $this->fetch();
    }
    //三方支付
    public function recharge3()
    {

        $type = isset($_REQUEST['type']) ? $_REQUEST['type']: 'wx';
        $pay = db('xy_pay')->where('status',1)->select();
        $this->assign('title',$type=='wx' ? lang('微信支付'): lang('支付宝支付'));
        $this->assign('pay',$pay);
        $this->assign('type',$type);
        return $this->fetch();
    }
    public function recharge5(){
        $uid = cookie('user_id');
        $tel = Db::name('xy_users')->where('id',$uid)->value('tel');//获取用户今日已充值金额
        $this->tel = substr_replace($tel,'****',3,4);
        $this->pay = db('xy_pay')->where('status',1)->select();

        return $this->fetch();
    }
    public function recharge6(){
        $uid = cookie('user_id');
        $tel = Db::name('xy_users')->where('id',$uid)->value('tel');//获取用户今日已充值金额
        $this->tel = substr_replace($tel,'****',3,4);
        $this->pay = db('xy_pay')->where('status',1)->select();

        return $this->fetch();
    }
    //钱包页面
    public function bank()
    {
        $balance = db('xy_users')->where('id', cookie('user_id'))->value('balance');
        $this->assign('balance', $balance);
        $balanceT = db('xy_convey')->where('uid', cookie('user_id'))->where('status', 2)->sum('commission');
        $this->assign('balance_shouru', $balanceT);
        return $this->fetch();
    }

    //获取提现订单接口
    public function get_deposit()
    {
        $info = db('xy_deposit')->where('uid',cookie('user_id'))->select();
        if($info) return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$info]);
        return json(['code'=>1,'info'=>lang('暂无数据')]);
    }

    public function my_data()
    {
        $uinfo = db('xy_users')->where('id', cookie('user_id'))->find();
        if ($uinfo['tel']) {
            $uinfo['tel'] = substr_replace($uinfo['tel'], '****', 3, 4);
        }
        $bank = db('xy_bankinfo')->where(['uid'=>cookie('user_id')])->find();
        $uinfo['cardnum'] = substr_replace($bank['cardnum'],'****',7,7);
        if(request()->isPost()) {
            $username = input('post.username/s', '');
            //$pic = input('post.qq/s', '');

            $res = db('xy_users')->where('id',cookie('user_id'))->update(['username'=>$username]);
            if (!$res) {
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
            }else{
                return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
            }
        }

        $this->assign('info', $uinfo);

        return $this->fetch();
    }



    public function recharge_do()
    {
        if(request()->isPost()){
            $num = input('post.price/f',0);
            $type = input('post.type/s','card');
            $pic = input('post.pic/s','');

            $uid = cookie('user_id');
            $uinfo = db('xy_users')->field('pwd,salt,tel,username')->find($uid);
            if(!$num ) return json(['code'=>1,'info'=>lang('参数错误')]);

            if (is_image_base64($pic))
                $pic = '/' . $this->upload_base64('xy',$pic);  //调用图片上传的方法
            else
                return json(['code'=>1,'info'=>lang('图片格式错误')]);

            //
            $pay = db('xy_pay')->where('name2',$type)->find();
            if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
            if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

            $id = getSn('SY');
            $dd = DB::name('xy_recharge')->where('uid',$uid)->count('*');
            $res = db('xy_recharge')
                ->insert([
                    'id'        => $id,
                    'uid'       => $uid,
                    'tel'       => $uinfo['tel'],
                    'real_name' => $uinfo['username'],
                    'pic'       => $pic,
                    'num'       => $num,
                     'nums'       => $dd + 1,
                    'addtime'   => time(),
                    'pay_name'  => $type
                ]);
            if($res){
                $pay['id'] = $id;
                $pay['num'] =$num;
                if ($pay['name2'] == 'bipay' ) {
                    $pay['redirect'] = url('/index/Api/bipay').'?oid='.$id;
                }
                if ($pay['name2'] == 'paysapi' ) {
                    $pay['redirect'] = url('/index/Api/pay').'?oid='.$id;
                }
                return json(['code'=>0,'info'=>$pay]);
            }

            else
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
        }
        return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
    }

    function deposit_wx(){

        $user = db('xy_users')->where('id', cookie('user_id'))->find();
        $this->assign('title',lang('微信提现'));

        $this->assign('type','wx');
        $this->assign('user',$user);
        return $this->fetch();
    }

    //提现首页
    function deposit(){
//echo DB::name('xy_deposit')->where('uid',cookie('user_id'))->count('*');
        $user = db('xy_users')->where('id', cookie('user_id'))->find();
        $user['tel'] = substr_replace($user['tel'],'****',3,4);
        $bank = db('xy_bankinfo')->where(['uid'=>cookie('user_id')])->find();

        $bank['cardnum'] = !empty($bank['cardnum']) ? substr_replace($bank['cardnum'],'****',7,7) : '';
        $bank['zhifunum'] = !empty($bank['zhifunum']) ? substr_replace($bank['zhifunum'],'****',2,5) : '';
        $this->assign('info',$bank);
        $this->assign('user',$user);

        //提现限制
        $level = $user['level'];
        !$user['level'] ? $level = 0 : '';
        $ulevel = Db::name('xy_level')->where('level',$level)->find();
        $this->shouxu = $ulevel['tixian_shouxu'];

        return $this->fetch();
    }



    function deposit_zfb(){

        $user = db('xy_users')->where('id', cookie('user_id'))->find();
        $this->assign('title',lang('支付宝提现'));

        $this->assign('type','zfb');
        $this->assign('user',$user);
        return $this->fetch('deposit_zfb');
    }


public function withdraw()
    {
        $uid = cookie('user_id');
        $userData = Db::table("xy_users")->find($uid);
        $this->userData = $userData;

        if($_POST){
            $res = check_time(config('tixian_time_1'),config('tixian_time_2'));
        $str = config('tixian_time_1').":00  - ".config('tixian_time_2').":00";
        if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!'),'a'=>1]);
        }

        return $this->fetch();
    }

    //提现接口
    public function do_deposit()
    {
        $res = check_time(config('tixian_time_1'),config('tixian_time_2'));
        $str = config('tixian_time_1').":00  - ".config('tixian_time_2').":00";
        if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!'),'a'=>1]);  //'a'=>0 表示刷新提现当前页，1表示跳转回个人页


        $bankinfo = Db::name('xy_bankinfo')->where('uid',cookie('user_id'))->where('status',1)->find();
        //var_dump($bankinfo);die;
        $type = config('tixian_type');

        if(!$bankinfo) return json(['code'=>1,'info'=>lang('还没添加收款信息!'),'a'=>1]);


        if(request()->isPost()){
            $uid = cookie('user_id');
            $Day = date('Y-m-d ',time());
            $timeBegin = strtotime($Day.config('tixian_time_1').":00");
            $timeEnd = strtotime($Day.config('tixian_time_2').":00");
            $curr_time = time();
            if($curr_time >= $timeBegin && $curr_time <= $timeEnd){
                //交易密码
                $pwd2 = input('post.paypassword/s','');
                $info = db('xy_users')->field('pwd2,salt2')->find(cookie('user_id'));
                if($info['pwd2']=='') return json(['code'=>1,'info'=>lang('未设置交易密码')]);
                if($info['pwd2']!=sha1($pwd2.$info['salt2'].config('pwd_str'))) return json(['code'=>1,'info'=>lang('密码错误') ,'a'=>0]);


                $num = input('post.num/d',0);
                $bkid = input('post.bk_id/d',$bankinfo['id']);
                // $type = input('post.type/s','');
                $token = input('post.token','');
                $data = ['__token__' => $token];
                $validate   = \Validate::make($this->rule,$this->msg);
                if(!$validate->check($data)) return json(['code'=>1,'info'=>$validate->getError(),'a'=>0]);//'a'=>0 表示刷新提现当前页，1表示跳转回个人页
                // dump($num);die;
                if ($num <= 0)return json(['code'=>1,'info'=>lang('参数错误'),'status'=>0]);

                $uinfo = Db::name('xy_users')->field('recharge_num,deal_time,balance,level')->find($uid);//获取用户今日已充值金额

                //提现限制
                $level = $uinfo['level'];
                !$uinfo['level'] ? $level = 0 : '';
                $ulevel = Db::name('xy_level')->where('level',$level)->find();
                if ($num < $ulevel['tixian_min']) {
                    return ['code'=>1,'info'=>lang('会员等级提现额度为').$ulevel['tixian_min'].'-'.$ulevel['tixian_max'].'!' , 'a'=>0];
                }
                if ($num >= $ulevel['tixian_max']) {
                    return ['code'=>1,'info'=>lang('会员等级提现额度为').$ulevel['tixian_min'].'-'.$ulevel['tixian_max'].'!' ,'a'=>0];//'a'=>0 表示刷新提现当前页，1表示跳转回个人页
                }

                $onum =  db('xy_convey')->where('uid',$uid)->where('addtime','between',[strtotime(date('Y-m-d')),time()])->count('id');
                if ($onum < $ulevel['tixian_nim_order']) {
                    return ['code'=>1,'info'=>lang('当前等级提现需完成').$ulevel['tixian_nim_order'].lang('笔订单!') ,'a'=>1];
                }

                // //有未完成订单不允许提现
                $order_status =  db('xy_convey')->where('uid',$uid)->where('status',0)->count('status');  //查询未完成订单

                if( $order_status != 0 ){
                    return ['code'=>1,'info'=>lang('您还有未完成的订单！！'),'a'=>1];
                }


                if ($num > $uinfo['balance']) return json(['code'=>1,'info'=>lang('余额不足'),'a'=>0]);


                if($uinfo['deal_time']==strtotime(date('Y-m-d'))){
                    // if($num > 20000-$uinfo['recharge_num']) return ['code'=>1,'info'=>'今日剩余提现额度为'.( 20000 - $uinfo['recharge_num'])];
                    //提现次数限制
                    $tixianCi = db('xy_deposit')->where('uid',$uid)->where('addtime','between',[strtotime(date('Y-m-d 00:00:00')),time()])->count();
                    if ($tixianCi+1 > $ulevel['tixian_ci'] ) {
                        return ['code'=>1,'info'=>lang('会员等级 今日提现次数不足!') ,'a'=>0];
                    }

                }else{
                    //重置最后交易时间
                    Db::name('xy_users')->where('id',$uid)->update(['deal_time'=>strtotime(date('Y-m-d')),'deal_count'=>0,'recharge_num'=>0,'deposit_num'=>0]);
                }
                $id = getSn('CO');
                $dd = DB::name('xy_deposit')->where('uid',$uid)->count('*');
                // echo $dd;
                try {
                    Db::startTrans();
                    $res = Db::name('xy_deposit')->insert([
                        'id'          => $id,
                        'uid'         => $uid,
                        'bk_id'       => $bkid,
                        'num'         => $num,
                        'nums'         => $dd + 1,
                        'addtime'     => time(),
                        'type'        => $type,
                        'shouxu'      => $ulevel['tixian_shouxu'],
                        'real_num'    => $num - ($num*$ulevel['tixian_shouxu'])
                    ]);

                    //提现日志
                    $res2 = Db::name('xy_balance_log')
                        ->insert([
                            'uid' => $uid,
                            'oid' => $id,
                            'num' => $num,
                            'type' => 7, //TODO 7提现
                            'status' => 2,
                            'addtime' => time(),
                        ]);


                    $res1 = Db::name('xy_users')->where('id',cookie('user_id'))->setDec('balance',$num);
                    if($res && $res1){
                        Db::commit();
                        return json(['code'=>0,'info'=>lang('操作成功'),'a'=>0]);
                    }else{
                        Db::rollback();
                        return json(['code'=>1,'info'=>lang('操作失败'),'a'=>0]);
                    }
                } catch (\Exception $e){
                    Db::rollback();
                    return json(['code'=>1,'info'=>lang('操作失败!请检查账号余额'),'a'=>0]);
                }
            } else {
                return json(['code'=>1,'info'=>lang('不在提现时间范围内') ,'a'=>0]);
            }

        }
        return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$bankinfo,'a'=>1]);
    }

    //////get请求获取参数，post请求写入数据，post请求传人bkid则更新数据//////////
    public function do_bankinfo()
    {
        if(request()->isPost()){
            $token = input('post.token','');
            $data = ['__token__' => $token];
            $validate   = \Validate::make($this->rule,$this->msg);
            if(!$validate->check($data)) return json(['code'=>1,'info'=>$validate->getError()]);

            $username = input('post.username/s','');
            $bankname = input('post.bankname/s','');
            $cardnum = input('post.cardnum/s','');
            $site = input('post.site/s','');
            $tel = input('post.tel/s','');
            $status = input('post.default/d',0);
            $bkid = input('post.bkid/d',0); //是否为更新数据

            if(!$username)return json(['code'=>1,'info'=>lang('开户人名称为必填项')]);
            if(mb_strlen($username) > 30)return json(['code'=>1,'info'=>lang('开户人名称长度最大为30个字符')]);
            if(!$bankname)return json(['code'=>1,'info'=>lang('银行名称为必填项')]);
            if(!$cardnum)return json(['code'=>1,'info'=>lang('银行卡号为必填项')]);
            if(!$tel)return json(['code'=>1,'info'=>lang('手机号为必填项')]);

            if($bkid)
                $cardn = Db::table('xy_bankinfo')->where('id','<>',$bkid)->where('cardnum',$cardnum)->count();
            else
                $cardn = Db::table('xy_bankinfo')->where('cardnum',$cardnum)->count();

            if($cardn)return json(['code'=>1,'info'=>lang('银行卡号已存在')]);

            $data = ['uid'=>cookie('user_id'),'bankname'=>$bankname,'cardnum'=>$cardnum,'tel'=>$tel,'site'=>$site,'username'=>$username];
            if($status){
                Db::table('xy_bankinfo')->where(['uid'=>cookie('user_id')])->update(['status'=>0]);
                $data['status'] = 1;
            }

            if($bkid)
                $res = Db::table('xy_bankinfo')->where('id',$bkid)->where('uid',cookie('user_id'))->update($data);
            else
                $res = Db::table('xy_bankinfo')->insert($data);

            if($res)
                return json(['code'=>0,'info'=>lang('操作成功')]);
            else
                return json(['code'=>1,'info'=>lang('操作失败')]);
        }
    }

    /**
     * 测试连接
     */
    public function test_connection()
    {
        $uid = cookie('user_id');
        $session_id = session_id();
        $all_cookies = $_COOKIE;
        
        return json([
            'code' => 0, 
            'info' => '连接成功', 
            'data' => [
                'time' => date('Y-m-d H:i:s'),
                'user_id' => $uid,
                'session_id' => $session_id,
                'has_user_id_cookie' => isset($_COOKIE['user_id']),
                'post_method' => request()->isPost() ? 'YES' : 'NO',
                'all_cookies' => array_keys($all_cookies)
            ]
        ]);
    }

    /**
     * 获取资金明细
     */
    public function get_funds_detail()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'info' => lang('请求方式错误')]);
        }
        
        $uid = cookie('user_id');
        if (!$uid) {
            return json(['code' => 1, 'info' => '用户未登录']);
        }
        
        $month = input('post.month', '');
        
        try {
            // 先简化查询，只获取基本的转入转出记录
            $records = Db::name('xy_balance_log')
                ->where('uid', $uid)
                ->where('type', 'in', [21, 22]) // 21转入，22转出
                ->order('addtime desc')
                ->limit(20)
                ->select();
            
            $data = [];
            if ($records) {
                foreach ($records as $record) {
                    $data[] = [
                        'id' => $record['id'],
                        'num' => number_format($record['num'], 2),
                        'type' => $record['type'],
                        'type_prefix' => $record['type'] == 21 ? '+' : '-',
                        'addtime_fmt' => date('Y-m-d H:i:s', $record['addtime']),
                        'month' => date('Y年n月', $record['addtime']),
                        'product_name' => $record['type'] == 21 ? '利息宝转入' : '利息宝转出'
                    ];
                }
            }
            
            return json(['code' => 0, 'info' => '获取成功', 'data' => $data, 'debug' => ['uid' => $uid, 'record_count' => count($records)]]);
            
        } catch (\Exception $e) {
            return json(['code' => 1, 'info' => '获取失败: ' . $e->getMessage(), 'debug' => ['uid' => $uid]]);
        }
    }
    
    /**
     * 获取收益报表
     */
    public function get_report_detail()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'info' => lang('请求方式错误')]);
        }
        
        $uid = cookie('user_id');
        
        try {
            // 获取收益记录
            $income_records = Db::name('xy_balance_log')
                ->where('uid', $uid)
                ->where('type', 23) // 23为利息宝收益
                ->order('addtime desc')
                ->limit(50)
                ->select();
            
            // 获取投资记录
            $investment_records = Db::name('xy_lixibao')
                ->alias('l')
                ->leftJoin('xy_lixibao_list p', 'l.sid = p.id')
                ->where('l.uid', $uid)
                ->where('l.type', 1)
                ->field('l.*, p.name as product_name')
                ->order('l.addtime desc')
                ->select();
            
            $records = [];
            
            // 处理收益记录
            foreach ($income_records as $record) {
                $records[] = [
                    'date' => date('Y-m-d', $record['addtime']),
                    'product_name' => lang('利息宝收益'),
                    'amount' => '0.00',
                    'income' => '+' . $record['num'],
                    'status_text' => lang('已到账')
                ];
            }
            
            // 处理投资记录
            foreach ($investment_records as $record) {
                $records[] = [
                    'date' => date('Y-m-d', $record['addtime']),
                    'product_name' => $record['product_name'] ?: lang('利息宝产品'),
                    'amount' => $record['num'],
                    'income' => '0.00',
                    'status_text' => $record['is_qu'] == 0 ? lang('投资中') : lang('已结束')
                ];
            }
            
            // 按日期排序
            usort($records, function($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });
            
            // 计算统计信息
            $total_investment = Db::name('xy_lixibao')->where('uid', $uid)->sum('num') ?: 0;
            $total_income = Db::name('xy_balance_log')->where('uid', $uid)->where('type', 23)->sum('num') ?: 0;
            
            // 获取投资开始时间
            $start_date = Db::name('xy_lixibao')->where('uid', $uid)->min('addtime');
            $start_date_fmt = $start_date ? date('Y-m-d', $start_date) : '';
            
            // 计算投资天数
            $investment_days = $start_date ? ceil((time() - $start_date) / 86400) : 0;
            
            // 计算平均日收益
            $avg_daily_income = $investment_days > 0 ? round($total_income / $investment_days, 4) : 0;
            
            $data = [
                'records' => array_slice($records, 0, 20), // 限制返回20条记录
                'start_date' => $start_date_fmt,
                'total_investment' => number_format($total_investment, 2),
                'total_income' => number_format($total_income, 2),
                'avg_daily_income' => number_format($avg_daily_income, 4),
                'investment_days' => $investment_days
            ];
            
            return json(['code' => 0, 'info' => lang('获取成功'), 'data' => $data]);
            
        } catch (\Exception $e) {
            return json(['code' => 1, 'info' => lang('获取失败') . ': ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取利息宝规则
     */
    public function get_rules_detail()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'info' => lang('请求方式错误')]);
        }
        
        try {
            // 获取所有活跃的利息宝产品
            $products = Db::name('xy_lixibao_list')
                ->where('status', 1)
                ->field('id, name, bili, day, min_num, max_num')
                ->order('day asc')
                ->select();
            
            $data = [
                'products' => $products
            ];
            
            return json(['code' => 0, 'info' => lang('获取成功'), 'data' => $data]);
            
        } catch (\Exception $e) {
            return json(['code' => 1, 'info' => lang('获取失败') . ': ' . $e->getMessage()]);
        }
    }
}