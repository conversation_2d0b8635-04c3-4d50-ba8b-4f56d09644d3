<?php

// +----------------------------------------------------------------------
// | ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2019 
// +----------------------------------------------------------------------

// +----------------------------------------------------------------------

// +----------------------------------------------------------------------
// | 

// +----------------------------------------------------------------------

namespace app\index\controller;

use library\Controller;
use think\Db;

/**
 * 支付控制器
 */
class Api extends Controller
{

    public $BASE_URL = "https://bapi.app";
    public $appKey = '';
    public $appSecret = '';

    const POST_URL = "https://pay.bbbapi.com/";


    public function __construct()
    {
        $this->appKey = config('app.bipay.appKey');
        $this->appSecret = config('app.bipay.appSecret');
    }
    
    public function sskking_t_hui(){
        if(request()->isPost()){
            // $data = file_get_contents('php://input');
            //  $data = json_decode(file_get_contents('php://input'),true);
             $data = $_POST;
            
            if($data['respCode'] != 'SUCCESS' || $data['tradeResult'] != '1'){
                exit('error');
            }
            
            $deposit_list = Db::name('xy_deposit')->where([ 'id' => $data['merTransferId'], 'status' => 4])->find();
            if(is_null($deposit_list)){
                exit('error');
            }
            $res = Db::name('xy_deposit')->where([ 'id' => $deposit_list['id']])->update(['status' => 5]);
            if(!$res) exit('error');
            exit('suceess');
        }
    }
    public function rpay_h_hui(){
        // file_put_contents('test.txt',file_get_contents('php://input'));
        if(request()->isPost()){
            $data = json_decode(file_get_contents('php://input'), true);
            // 
             $deposit_list = Db::name('xy_deposit')->where([ 'id' => $data['merchantOrderId'], 'status' => 4])->find();
            if(is_null($deposit_list)){
                exit('error');
            }
            
            if($data['status'] != '1'){
                $res = Db::name('xy_deposit')->where([ 'id' => $deposit_list['id']])->update(['status' => 3]);
                exit('error');
            }
            
           
            $res = Db::name('xy_deposit')->where([ 'id' => $deposit_list['id']])->update(['status' => 5]);
            if(!$res) exit('error');
            exit('suceess');
        }
    }
    
        public function stepay_hui(){
        if(request()->isPost()){

// tradeResult	int	否	订单状态 0:初始化状态 1成功的订单 2失败的订单
// orderNo	string	否	平台订单号
// mchOrderNo	string	否	商户订单号
// mchId	string	否	商户号
// amount	float	否	订单金额
// sign	float	否	签名

        // $list = json_decode(file_get_contents('php://input'), true);
        $list = $_POST;
// file_put_contents('./test.txt', $list);die;
// {"amount":"10000.00","mchId":"testsh","mchOrderNo":"SY2203240042451146","orderNo":"202203231342468874","tradeResult":"1","sign":"12e1b69bb5e78a63e78cd6171624e09a"}
        $data['mchOrderNo'] = $list['mchOrderNo'];
    		 $charge_list = Db::name('xy_recharge')->where([ 'id' => $data['mchOrderNo'], 'status' => 1])->find();
            //订单不存在
            if(is_null($charge_list)) exit('error');
// dump($data);die;
            if($list['tradeResult'] != 1){
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '3']);
                exit('success');
            }
            if($charge_list['status'] == 2){
                exit('success');
            }
            $exec_result = false;
            // 启动事务
            Db::startTrans();
            try {
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '2']);
                $res2 = Db::name('xy_balance_log')
                                ->insert([
                                    'uid' => $charge_list['uid'],
                                    'oid' => $data['mchOrderNo'],
                                    'num' => $charge_list['num'],
                                    'type' => 1, //TODO 7提现
                                    'status' => 1,
                                    'addtime' => time(),
                                ]);
                $user_result = Db::name('xy_users')->where(['id' => $charge_list['uid']])->setInc('balance', $charge_list['num']);  
                if(!$res || !$res2 || !$user_result){
                    throw new \Exception('error');
                }
                $exec_result = true;
                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
            }

            if(!$exec_result)  exit('error');

            exit('success');					
    	}else{
    		echo "error";  
    	}
}
    
    
        
    public function stepay_t_hui(){
        if(request()->isPost()){
            // $data = file_get_contents('php://input');
            //  $data = json_decode(file_get_contents('php://input'),true);
             $data = $_POST;

            if($data['tradeResult'] != '1'){
                exit('error');
            }
            
            $deposit_list = Db::name('xy_deposit')->where([ 'id' => $data['mchOrderNo'], 'status' => 4])->find();
            // file_put_contents('./test.txt', json_encode($deposit_list));die;
            if(is_null($deposit_list)){
                exit('error');
            }
            $res = Db::name('xy_deposit')->where([ 'id' => $deposit_list['id']])->update(['status' => 2]);
            if(!$res) exit('error');
            exit('success');
        }
    }
    
    public function kbpay_hui(){
        if(request()->isPost()){

        $list = json_decode(file_get_contents('php://input'), true);

        $data['mchOrderNo'] = $list['order_id'];
    		 $charge_list = Db::name('xy_recharge')->where([ 'id' => $data['mchOrderNo'], 'status' => 1])->find();
            //订单不存在
            if(is_null($charge_list)) exit('error');
// dump($data);die;
            if($list['order_status'] != 1){
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '3']);
                exit('SUCCESS');
            }
            if($charge_list['status'] == 2){
                exit('SUCCESS');
            }
            $exec_result = false;
            // 启动事务
            Db::startTrans();
            try {
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '2']);
                $res2 = Db::name('xy_balance_log')
                                ->insert([
                                    'uid' => $charge_list['uid'],
                                    'oid' => $data['mchOrderNo'],
                                    'num' => $charge_list['num'],
                                    'type' => 1, //TODO 7提现
                                    'status' => 1,
                                    'addtime' => time(),
                                ]);
                $user_result = Db::name('xy_users')->where(['id' => $charge_list['uid']])->setInc('balance', $charge_list['num']);  
                if(!$res || !$res2 || !$user_result){
                    throw new \Exception('error');
                }
                $exec_result = true;
                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
            }

            if(!$exec_result)  exit('error');

            exit('SUCCESS');					
    	}else{
    		echo "error";  
    	}
}
    
    public function kbpay_t_hui(){
        if(request()->isPost()){
            // $data = file_get_contents('php://input');
             $data = json_decode(file_get_contents('php://input'),true);
            //  $data = $_POST;
            
            if($data['status'] != '0'){
                exit('error');
            }
            
            $deposit_list = Db::name('xy_deposit')->where([ 'id' => $data['data']['order_id'], 'status' => 4])->find();
            if(is_null($deposit_list)){
                exit('error');
            }
            $res = Db::name('xy_deposit')->where([ 'id' => $deposit_list['id']])->update(['status' => 5]);
            if(!$res) exit('error');
            exit('SUCCESS');
        }
    }
    
    public function kbpay_t_hui22(){
        if(request()->isPost()){
            // $data = file_get_contents('php://input');
             $data = json_decode(file_get_contents('php://input'),true);
            //  $data = $_POST;
            
            if($data['status'] != 'success'){
                exit('error');
            }
            
            $deposit_list = Db::name('xy_deposit')->where([ 'id' => $data['order_no'], 'status' => 4])->find();
            if(is_null($deposit_list)){
                exit('error');
            }
            $res = Db::name('xy_deposit')->where([ 'id' => $deposit_list['id']])->update(['status' => 5]);
            if(!$res) exit('error');
            exit('SUCCESS');
        }
    }
    
    public function rpay_hui(){
        if(request()->isPost()){
// merchantId	int32	商户id	false	-
// merchantOrderId	string	商户订单id	false	-
// amount	string	订单金额（带小数）	false	-
// timestamp	int64	时间戳	false	-
// orderId	int64	running pay 订单id	false	-
// status	int32	订单状态 0 未支付(待提现) 1 已支付（已提现） 2 失败 3 （已失效）	false	-
// sign	string	签名

// 签名固定字符串格式
// 'merchantId=1001&merchantOrderId=1000&amount=100.00&secretKey'
// 进行MD5加密
// 注意金额保留小数位	false	-
// msg	string	错误消息	false
        $list = json_decode(file_get_contents('php://input'), true);

        $data['mchOrderNo'] = $list['merchantOrderId'];
    		 $charge_list = Db::name('xy_recharge')->where([ 'id' => $data['mchOrderNo'], 'status' => 1])->find();
            //订单不存在
            if(is_null($charge_list)) exit('error');
// dump($data);die;
            if($list['status'] != 1){
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '3']);
                exit('success');
            }
            if($charge_list['status'] == 2){
                exit('success');
            }
            $exec_result = false;
            // 启动事务
            Db::startTrans();
            try {
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '2']);
                $res2 = Db::name('xy_balance_log')
                                ->insert([
                                    'uid' => $charge_list['uid'],
                                    'oid' => $data['mchOrderNo'],
                                    'num' => $charge_list['num'],
                                    'type' => 1, //TODO 7提现
                                    'status' => 1,
                                    'addtime' => time(),
                                ]);
                $user_result = Db::name('xy_users')->where(['id' => $charge_list['uid']])->setInc('balance', $charge_list['num']);  
                if(!$res || !$res2 || !$user_result){
                    throw new \Exception('error');
                }
                $exec_result = true;
                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
            }

            if(!$exec_result)  exit('error');

            exit('OK');					
    	}else{
    		echo "Signature error";  
    	}
}



 public function rpay_hui22(){
        if(request()->isPost()){

        $list = json_decode(file_get_contents('php://input'), true);

        $data['mchOrderNo'] = $list['order_no'];
    		 $charge_list = Db::name('xy_recharge')->where([ 'id' => $data['mchOrderNo'], 'status' => 1])->find();
            //订单不存在
            if(is_null($charge_list)) exit('error');
// dump($data);die;
            if($list['status'] != 'success'){
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '3']);
                exit('success');
            }
            if($charge_list['status'] == 2){
                exit('success');
            }
            $exec_result = false;
            // 启动事务
            Db::startTrans();
            try {
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '2']);
                $res2 = Db::name('xy_balance_log')
                                ->insert([
                                    'uid' => $charge_list['uid'],
                                    'oid' => $data['mchOrderNo'],
                                    'num' => $charge_list['num'],
                                    'type' => 1, //TODO 7提现
                                    'status' => 1,
                                    'addtime' => time(),
                                ]);
                $user_result = Db::name('xy_users')->where(['id' => $charge_list['uid']])->setInc('balance', $charge_list['num']);  
                if(!$res || !$res2 || !$user_result){
                    throw new \Exception('error');
                }
                $exec_result = true;
                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
            }

            if(!$exec_result)  exit('error');

            exit('OK');					
    	}else{
    		echo "Signature error";  
    	}
}

    
    public function paytm_t_hui(){
        if(request()->isPost()){
            // $data = file_get_contents('php://input');
            //  $data = json_decode(file_get_contents('php://input'),true);
             $data = $_POST;
            
            if($data['respCode'] != 'SUCCESS'){
                exit('error');
            }
            
            $deposit_list = Db::name('xy_deposit')->where([ 'id' => $data['merTransferId'], 'status' => 4])->find();
            if(is_null($deposit_list)){
                exit('error');
            }
            if($data['tradeResult']==1){
                $res = Db::name('xy_deposit')->where([ 'id' => $deposit_list['id']])->update(['status' => 2]);
            }else{
                $res = Db::name('xy_deposit')->where([ 'id' => $deposit_list['id']])->update(['status' => 3]);
                Db::name("xy_users")->where('id',$deposit_list['uid'])->setInc("balance",$deposit_list['num']);
            }
            return 'suceess';
        }
    }
    
    public function paytm_hui(){
        if(request()->isPost()){
            $data = input('post.');
    		$charge_list = Db::name('xy_recharge')->where([ 'id' => $data['mchOrderNo'], 'status' => 1])->find();
            //订单不存在
            if(is_null($charge_list)) exit('error');

            if($data['tradeResult'] != 1){
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '3']);
                exit('success');
            }
            if($charge_list['status'] == 2){
                exit('success');
            }
            $exec_result = false;
            // 启动事务
            Db::startTrans();
            try {
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '2']);
                $res2 = Db::name('xy_balance_log')
                                ->insert([
                                    'uid' => $charge_list['uid'],
                                    'oid' => $data['mchOrderNo'],
                                    'num' => $charge_list['num'],
                                    'type' => 1, //TODO 7提现
                                    'status' => 1,
                                    'addtime' => time(),
                                ]);
                $user_result = Db::name('xy_users')->where(['id' => $charge_list['uid']])->setInc('balance', $charge_list['num']);  
                if(!$res || !$res2 || !$user_result){
                    throw new \Exception('error');
                }
                $exec_result = true;
                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
            }

            if(!$exec_result)  exit('error');

            exit('success');					
    	}else{
    		exit('error');
    	}
    }
    
    public function sskking_hui(){
         //  $data = json_decode(file_get_contents('php://input'),true);
        
        // file_put_contents('./test12.txt', file_get_contents('php://input'));
        $data = $_POST;
        //  file_put_contents('./test123.txt', $data);
            //         $data = explode('&',$data);
            // $list = [];
            // foreach($data as $k =>$v){
            //     $result = explode('=', $v);
            //     $list[$result[0]] = $result[1];
            // }
            // $data = $list;
        $merchant_key = 'TGMVFUGCQ1EZ2PRCZXIBTSMXWNV4HTOJ';

        $signStr = ASCII($data);
// dump($data);die;
    	include('SignApi.php');
            // dump($signStr);
            // dump($merchant_key);
            // dump($data);die;
        $signAPI = new \SignApi;
    	$flag = $signAPI->validateSignByKey($signStr,$merchant_key,$data['sign']);

    	if($flag){						
    		 $charge_list = Db::name('xy_recharge')->where([ 'id' => $data['mchOrderNo'], 'status' => 1])->find();
            //订单不存在
            if(is_null($charge_list)) exit('error');
            // if($data['tradeResult'] == 1) exit('success');
            if($data['tradeResult'] != 1){
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '3']);
                exit('success');
            }
            if($charge_list['status'] == 2){
                exit('success');
            }
            $exec_result = false;
            // 启动事务
            Db::startTrans();
            try {
                $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '2']);
                $res2 = Db::name('xy_balance_log')
                                ->insert([
                                    'uid' => $charge_list['uid'],
                                    'oid' => $data['mchOrderNo'],
                                    'num' => $charge_list['num'],
                                    'type' => 1, //TODO 7提现
                                    'status' => 1,
                                    'addtime' => time(),
                                ]);
                $user_result = Db::name('xy_users')->where(['id' => $charge_list['uid']])->setInc('balance', $charge_list['num']);  
                if(!$res || !$res2 || !$user_result){
                    throw new \Exception('error');
                }
                $exec_result = true;
                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
            }
//             $res = Db::name('xy_recharge')->where([ 'id' => $charge_list['id']])->update(['status' => '2']);
//             if(!$res) exit('error');

//             $res2 = Db::name('xy_balance_log')
//                     ->insert([
//                         'uid' => $charge_list['uid'],
//                         'oid' => $order_no,
//                         'num' => $charge_list['num'],
//                         'type' => 1, //TODO 7提现
//                         'status' => 1,
//                         'addtime' => time(),
//                     ]);
//             //增加余额失败
//             if(!$res2) exit('error2'); 
            if(!$exec_result)  exit('error');
             
//             //增加余额失败
//             if(!$user_result) exit('error3');
            exit('success');					
    	}else{
    		echo "Signature error";  
    	}
    }
    
    public function bipay()
    {

        $oid = isset($_REQUEST['oid']) ? $_REQUEST['oid']: '';
        if ($oid) {
            $r = db('xy_recharge')->where('id',$oid)->find();
            if ($r) {
                $server_url = $_SERVER['SERVER_NAME']?"http://".$_SERVER['SERVER_NAME']:"http://".$_SERVER['HTTP_HOST'];
                $notifyUrl = $server_url.url('/index/api/bipay_notify');
                $returnUrl = $server_url.url('/index/api/bipay_return');
                $price = $r['num'] * 100;
                $res = $this->create_order($oid,$price,lang('用户充值'),$notifyUrl, $returnUrl);

                if ($res && $res['code']==200) {
                    $url = $res['data']['pay_url'];
                    $this->redirect($url);
                }
            }
        }
    }

    public function bipay_return()
    {
        return $this->fetch();
    }


    public function bipay_notify()
    {

        $content = file_get_contents('php://input');
        $post    = (array)json_decode($content, true);
        file_put_contents("bipay_notify.log",$content."\r\n",FILE_APPEND);

        if (!$post['order_id']) {
            die('fail');
        }
        $oid = $post['order_id'];
        $r = db('xy_recharge')->where('id',$oid)->find();
        if (!$r) {
            die('fail');
        }
        if ($post['order_state']!=1) {
            die('fail');
        }

        if ($r['status'] == 2){
            die('SUCCESS');
        }

        if ($post['order_state']) {
            $res = Db::name('xy_recharge')->where('id',$oid)->update(['endtime'=>time(),'status'=>2]);
            $oinfo = $r;
            $res1 = Db::name('xy_users')->where('id',$oinfo['uid'])->setInc('balance',$oinfo['num']);
            $res2 = Db::name('xy_balance_log')
                ->insert([
                    'uid'=>$oinfo['uid'],
                    'oid'=>$oid,
                    'num'=>$oinfo['num'],
                    'type'=>1,
                    'status'=>1,
                    'addtime'=>time(),
                ]);
            /************* 发放推广奖励 *********/
            $uinfo = Db::name('xy_users')->field('id,active')->find($oinfo['uid']);
            if($uinfo['active']===0){
                Db::name('xy_users')->where('id',$uinfo['id'])->update(['active'=>1]);
                //将账号状态改为已发放推广奖励
                $userList = model('Users')->parent_user($uinfo['id'],3);
                if($userList){
                    foreach($userList as $v){
                        if($v['status']===1 && ($oinfo['num'] * config($v['lv'].'_reward') != 0)){
                            Db::name('xy_reward_log')
                                ->insert([
                                    'uid'=>$v['id'],
                                    'sid'=>$uinfo['id'],
                                    'oid'=>$oid,
                                    'num'=>$oinfo['num'] * config($v['lv'].'_reward'),
                                    'lv'=>$v['lv'],
                                    'type'=>1,
                                    'status'=>1,
                                    'addtime'=>time(),
                                ]);
                        }
                    }
                }
            }
            /************* 发放推广奖励 *********/
            die('SUCCESS');
        }
    }


    public function create_order(
        $orderId, $amount, $body, $notifyUrl, $returnUrl, $extra = '', $orderIp = '', $amountType = 'CNY', $lang = 'zh_CN')
    {
        $reqParam = [
            'order_id' => $orderId,
            'amount' => $amount,
            'body' => $body,
            'notify_url' => $notifyUrl,
            'return_url' => $returnUrl,
            'extra' => $extra,
            'order_ip' => $orderIp,
            'amount_type' => $amountType,
            'time' => time() * 1000,
            'app_key' => $this->appKey,
            'lang' => $lang
        ];
        $reqParam['sign'] = $this->create_sign($reqParam, $this->appSecret);
        $url = $this->BASE_URL . '/api/v2/pay';

        return $this->http_request($url, 'POST', $reqParam);
    }

    /**
     * @return {
     * bapp_id: "2019081308272299266f",
     * order_id: "1565684838",
     * order_state: 0,
     * body: "php-sdk sample",
     * notify_url: "https://sdk.b.app/api/test/notify/test",
     * order_ip: "",
     * amount: 1,
     * amount_type: "CNY",
     * amount_btc: 0,
     * pay_time: 0,
     * create_time: 1565684842076,
     * order_type: 2,
     * app_key: "your_app_key",
     * extra: ""
     * }
     */
    public function get_order($orderId)
    {
        $reqParam = [
            'order_id' => $orderId,
            'time' => time() * 1000,
            'app_key' => $this->appKey
        ];
        $reqParam['sign'] = $this->create_sign($reqParam, $this->appSecret);
        $url = $this->BASE_URL . '/api/v2/order';
        return $this->http_request($url, 'GET', $reqParam);
    }

    public function is_sign_ok($params)
    {
        $sign = $this->create_sign($params, $this->appSecret);
        return $params['sign'] == $sign;
    }

    public function create_sign($params, $appSecret)
    {
        $signOriginStr = '';
        ksort($params);
        foreach ($params as $key => $value) {
            if (empty($key) || $key == 'sign') {
                continue;
            }
            $signOriginStr = "$signOriginStr$key=$value&";
        }
        return strtolower(md5($signOriginStr . "app_secret=$appSecret"));
    }

    private function http_request($url, $method = 'GET', $params = [])
    {
        $curl = curl_init();

        if ($method == 'POST') {
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_HEADER, false);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array("Content-type: application/json"));
            $jsonStr = json_encode($params);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $jsonStr);
        } else if ($method == 'GET') {
            $url = $url . "?" . http_build_query($params, '', '&');
        }
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);

        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);


        $output = curl_exec($curl);

        if (curl_errno($curl) > 0) {
            return [];
        }
        curl_close($curl);
        $json = json_decode($output, true);

        //var_dump($output,curl_errno($curl));die;

        return $json;
    }


    //----------------------------------------------------------------
    //  paysapi
    //----------------------------------------------------------------

    public function pay(){

        $oid = isset($_REQUEST['oid']) ? $_REQUEST['oid']: '';
        if ($oid) {
            $r = db('xy_recharge')->where('id',$oid)->find();
            if ($r) {

                //var_dump($r);die;

                $server_url = $_SERVER['SERVER_NAME']?"http://".$_SERVER['SERVER_NAME']:"http://".$_SERVER['HTTP_HOST'];
                $notify_url = $server_url.url('/index/api/pay_notify');
                $return_url = $server_url.url('/index/api/bipay_return');
                $price = $r['num'] * 100;


                $uid   = config('app.paysapi.uid');    //"此处填写Yipay的uid";
                $token = config('app.paysapi.token');;     //"此处填写Yipay的Token";

                $orderid = $r['id'];
                $goodsname= '用户充值';
                $istype =  config('app.paysapi.istype');
                $orderuid = cookie('user_id');

                $key = md5($goodsname. $istype . $notify_url . $orderid . $orderuid . $price . $return_url . $token. $uid);

                $data = array(
                    'goodsname'=>$goodsname,
                    'istype'=>$istype,
                    'key'=>$key,
                    'notify_url'=>$notify_url,
                    'orderid'=>$orderid,
                    'orderuid'=>$orderuid,
                    'price'=>$price,
                    'return_url'=>$return_url,
                    'uid'=>$uid
                );
                $this->assign('data',$data);
                $this->assign('post_url',self::POST_URL);
                return $this->fetch();
            }
        }

    }


    /**
     * notify_url接收页面
     */
    public function pay_notify(){

        $paysapi_id = $_POST["paysapi_id"];
        $orderid = $_POST["orderid"];
        $price = $_POST["price"];
        $realprice = $_POST["realprice"];
        $orderuid = $_POST["orderuid"];
        $key = $_POST["key"];

        file_put_contents(RUNTIME_PATH.'/paysapi_notify.log', json_encode($_REQUEST)."\r\n",FILE_APPEND);


        //校验传入的参数是否格式正确，略
        $d = $payType = array();
        if ($orderid) {
            $out_trade_no = $orderid;
            //$res = Db::name('xy_recharge')->where('id',$oid)->update(['endtime'=>time(),'status'=>2]);

            //$d = M('recharge')->where(array('order_no'=>$out_trade_no))->find();
            //$payType = M('pay_type')->find($d['payment_type']);

        }
        $token = config('app.paysapi.token');;
        $temps = md5($orderid . $orderuid . $paysapi_id . $price . $realprice . $token);

        if ($temps != $key){
            return exit(lang("key值不匹配"));
        }else{
            //校验key成功
            $oid = $orderid;
            $r = db('xy_recharge')->where('id',$oid)->find();
            $res = Db::name('xy_recharge')->where('id',$oid)->update(['endtime'=>time(),'status'=>2]);
            $oinfo = $r;
            $res1 = Db::name('xy_users')->where('id',$oinfo['uid'])->setInc('balance',$oinfo['num']);
            $res2 = Db::name('xy_balance_log')
                ->insert([
                    'uid'=>$oinfo['uid'],
                    'oid'=>$oid,
                    'num'=>$oinfo['num'],
                    'type'=>1,
                    'status'=>1,
                    'addtime'=>time(),
                ]);
            /************* 发放推广奖励 *********/
            $uinfo = Db::name('xy_users')->field('id,active')->find($oinfo['uid']);
            if($uinfo['active']===0){
                Db::name('xy_users')->where('id',$uinfo['id'])->update(['active'=>1]);
                //将账号状态改为已发放推广奖励
                $userList = model('Users')->parent_user($uinfo['id'],3);
                if($userList){
                    foreach($userList as $v){
                        if($v['status']===1 && ($oinfo['num'] * config($v['lv'].'_reward') != 0)){
                            Db::name('xy_reward_log')
                                ->insert([
                                    'uid'=>$v['id'],
                                    'sid'=>$uinfo['id'],
                                    'oid'=>$oid,
                                    'num'=>$oinfo['num'] * config($v['lv'].'_reward'),
                                    'lv'=>$v['lv'],
                                    'type'=>1,
                                    'status'=>1,
                                    'addtime'=>time(),
                                ]);
                        }
                    }
                }
            }
            /************* 发放推广奖励 *********/
            die('SUCCESS');

        }
    }

    /**
     * 检查用户登录状态
     * 安全修复：只检查session，不从cookie恢复登录状态
     */
    public function check_login()
    {
        try {
            // 从会话中获取用户ID
            $uid = session('user_id');
            
            if ($uid) {
                // 验证用户是否存在且状态正常
                $user = db('xy_users')->where('id', $uid)->where('status', 1)->find();
                if ($user) {
                    return json(['code' => 0, 'info' => lang('已登录'), 'data' => ['uid' => $uid, 'username' => $user['username']]]);
                } else {
                    // 用户不存在或被禁用，清除session
                    session('user_id', null);
                    return json(['code' => 1, 'info' => lang('用户状态异常，请重新登录')]);
                }
            }
            
            return json(['code' => 1, 'info' => lang('未登录')]);
        } catch (\Exception $e) {
            // 出现异常时，记录日志但不返回错误，避免影响用户体验
            \think\facade\Log::error('登录检查异常: ' . $e->getMessage());
            
            // 如果有session，认为是已登录状态
            $uid = session('user_id');
            if ($uid) {
                return json(['code' => 0, 'info' => lang('已登录'), 'data' => ['uid' => $uid]]);
            }
            
            return json(['code' => 1, 'info' => lang('登录状态检查失败')]);
        }
    }

    /**
     * 根据IP地址获取国家信息
     */
    public function get_country_by_ip()
    {
        try {
            // 获取用户真实IP
            $ip = $this->getRealIpAddr();
            
            // 如果是本地IP，返回默认值
            if ($this->isLocalIp($ip)) {
                return json([
                    'code' => 0,
                    'data' => [
                        'country_code' => '86',
                        'country_name' => 'China',
                        'flag_color' => '#de2910',
                        'ip' => $ip
                    ]
                ]);
            }

            // 使用免费的IP地理位置API
            $geoData = $this->getGeoLocationData($ip);
            
            if ($geoData) {
                return json([
                    'code' => 0,
                    'data' => $geoData
                ]);
            } else {
                // 如果API失败，返回默认值
                return json([
                    'code' => 0,
                    'data' => [
                        'country_code' => '86',
                        'country_name' => 'China',
                        'flag_color' => '#de2910',
                        'ip' => $ip
                    ]
                ]);
            }
        } catch (\Exception $e) {
            // 出错时返回默认值
            return json([
                'code' => 0,
                'data' => [
                    'country_code' => '86',
                    'country_name' => 'China',
                    'flag_color' => '#de2910',
                    'ip' => 'unknown'
                ]
            ]);
        }
    }

    /**
     * 获取用户真实IP地址
     */
    private function getRealIpAddr()
    {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (!empty($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (!empty($_SERVER['HTTP_FORWARDED'])) {
            $ip = $_SERVER['HTTP_FORWARDED'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        // 如果有多个IP，取第一个
        if (strpos($ip, ',') !== false) {
            $ip = trim(explode(',', $ip)[0]);
        }
        
        return $ip;
    }

    /**
     * 检查是否为本地IP
     */
    private function isLocalIp($ip)
    {
        return in_array($ip, ['127.0.0.1', '::1', 'localhost']) || 
               filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false;
    }

    /**
     * 获取地理位置数据
     */
    private function getGeoLocationData($ip)
    {
        // 国家代码映射表
        $countryMapping = [
            'CN' => ['code' => '86', 'name' => 'China', 'color' => '#de2910'],
            'US' => ['code' => '1', 'name' => 'United States', 'color' => '#3c3b6e'],
            'PH' => ['code' => '63', 'name' => 'Philippines', 'color' => '#0038a8'],
            'IN' => ['code' => '91', 'name' => 'India', 'color' => '#ff9933'],
            'ID' => ['code' => '62', 'name' => 'Indonesia', 'color' => '#ff0000'],
            'MY' => ['code' => '60', 'name' => 'Malaysia', 'color' => '#010066'],
            'TH' => ['code' => '66', 'name' => 'Thailand', 'color' => '#ed1c24'],
            'VN' => ['code' => '84', 'name' => 'Vietnam', 'color' => '#da020e'],
            'SG' => ['code' => '65', 'name' => 'Singapore', 'color' => '#ed2939'],
            'JP' => ['code' => '81', 'name' => 'Japan', 'color' => '#bc002d'],
            'KR' => ['code' => '82', 'name' => 'South Korea', 'color' => '#003478'],
            'AU' => ['code' => '61', 'name' => 'Australia', 'color' => '#012169'],
            'GB' => ['code' => '44', 'name' => 'United Kingdom', 'color' => '#012169'],
            'CA' => ['code' => '1', 'name' => 'Canada', 'color' => '#ff0000'],
            'BR' => ['code' => '55', 'name' => 'Brazil', 'color' => '#009739'],
            'MX' => ['code' => '52', 'name' => 'Mexico', 'color' => '#006847'],
            'TR' => ['code' => '90', 'name' => 'Turkey', 'color' => '#e30a17'],
            'SA' => ['code' => '966', 'name' => 'Saudi Arabia', 'color' => '#006c35'],
            'AE' => ['code' => '971', 'name' => 'United Arab Emirates', 'color' => '#00732f'],
            'EG' => ['code' => '20', 'name' => 'Egypt', 'color' => '#000000'],
            'ZA' => ['code' => '27', 'name' => 'South Africa', 'color' => '#ffb612'],
            'RU' => ['code' => '7', 'name' => 'Russia', 'color' => '#0039a6'],
            'DE' => ['code' => '49', 'name' => 'Germany', 'color' => '#000000'],
            'FR' => ['code' => '33', 'name' => 'France', 'color' => '#0055a4'],
            'IT' => ['code' => '39', 'name' => 'Italy', 'color' => '#009246'],
            'ES' => ['code' => '34', 'name' => 'Spain', 'color' => '#aa151b'],
        ];

        try {
            // 使用免费的ipapi.co服务
            $url = "http://ipapi.co/{$ip}/json/";
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'user_agent' => 'Mozilla/5.0 (compatible; IP Location Service)'
                ]
            ]);
            
            $response = @file_get_contents($url, false, $context);
            
            if ($response) {
                $data = json_decode($response, true);
                
                if (isset($data['country_code']) && $data['country_code']) {
                    $countryCode = strtoupper($data['country_code']);
                    
                    if (isset($countryMapping[$countryCode])) {
                        $country = $countryMapping[$countryCode];
                        return [
                            'country_code' => $country['code'],
                            'country_name' => $country['name'],
                            'flag_color' => $country['color'],
                            'ip' => $ip,
                            'country_iso' => $countryCode
                        ];
                    }
                }
            }
        } catch (\Exception $e) {
            // 记录错误但不抛出异常
            \think\facade\Log::error('IP地理位置查询失败: ' . $e->getMessage());
        }

        // 如果第一个API失败，尝试备用API
        try {
            $url2 = "http://ip-api.com/json/{$ip}?fields=status,country,countryCode";
            $response2 = @file_get_contents($url2, false, $context);
            
            if ($response2) {
                $data2 = json_decode($response2, true);
                
                if (isset($data2['status']) && $data2['status'] === 'success' && isset($data2['countryCode'])) {
                    $countryCode = strtoupper($data2['countryCode']);
                    
                    if (isset($countryMapping[$countryCode])) {
                        $country = $countryMapping[$countryCode];
                        return [
                            'country_code' => $country['code'],
                            'country_name' => $country['name'],
                            'flag_color' => $country['color'],
                            'ip' => $ip,
                            'country_iso' => $countryCode
                        ];
                    }
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('备用IP地理位置查询失败: ' . $e->getMessage());
        }

        return null;
    }
}