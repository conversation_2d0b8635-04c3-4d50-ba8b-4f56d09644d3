<?php
namespace app\index\controller;

use library\Controller;
use think\facade\Request;
use think\Db;

/**
 * 验证登录控制器
 */
class Base extends Controller
{
    protected $rule = ['__token__' => 'token'];
    protected $msg  = ['__token__'  => '无效token！'];

    /**
     * 初始化方法
     */
    protected function initialize()
    {
        parent::initialize();

        // 定义全局常量（如果未定义）
        if (!defined('DS')) {
            define('DS', DIRECTORY_SEPARATOR);
        }

        if (!defined('ROOT_PATH')) {
            define('ROOT_PATH', str_replace('\\', '/', realpath(dirname(__FILE__).'/../../..')) . '/');
        }

        // 获取当前控制器和方法
        $controller = request()->controller();
        $action = request()->action();
        
        // 不需要登录验证的页面列表
        $noAuthPages = [
            'User/login',
            'User/register', 
            'User/forget',
            'Api/check_login',
            'Error/index'
        ];
        
        $currentPage = $controller . '/' . $action;
        
        // 如果是不需要验证的页面，直接跳过登录检查
        if (in_array($currentPage, $noAuthPages)) {
            return;
        }

        // 简化的用户认证
        $uid = cookie('user_id');
        if (!$uid) {
            $this->redirect('User/login');
            return;
        }
        
        // 验证用户是否存在且状态正常
        $user = db('xy_users')->where('id', $uid)->where('status', 1)->find();
        if (!$user) {
            cookie('user_id', null); // 清除无效cookie
            $this->redirect('User/login');
            return;
        }
        
        // 设置session（如果没有的话）
        if (!session('user_id')) {
            session('user_id', $uid);
        }

        // 简化的语言处理
        $lang = cookie('lang') ?: 'en-us';
        $validLangs = ['en-us', 'zh-cn', 'mi', 'thai', 'baxi', 'moxige', 'tuerqi', 'arabic', 'zh'];
        
        if (!in_array($lang, $validLangs)) {
            $lang = 'en-us';
            cookie("lang", $lang, ['expire' => time()+3600*24]);
        }
        
        // 加载语言包
        $langFile = ($lang == 'zh-cn') ? 'zh' : $lang;
        \think\facade\Lang::range($langFile);
        
        // 尝试加载模块语言包
        $moduleLangFile = APP_PATH . 'index/lang/' . $langFile . '.php';
        if (file_exists($moduleLangFile)) {
            \think\facade\Lang::load($moduleLangFile);
        }

        // 设置控制台脚本
        $this->console = '';
        try {
            $this->console = db('xy_script')->where('id', 1)->value('script') ?: '';
        } catch (\Exception $e) {
            // 忽略错误
        }
    }

    /**
     * 空操作 用于显示错误页面
     */
    public function _empty($name){
        return $this->fetch($name);
    }

    /**
     * 获取当前登录用户ID
     * 安全方法：只从session获取，不从cookie获取
     */
    protected function getCurrentUserId()
    {
        return session('user_id');
    }

    //图片上传为base64为的图片
    public function upload_base64($type,$img){
        if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $img, $result)){
            $type_img = $result[2];  //得到图片的后缀
            //上传 的文件目录

            $App = new \think\App();
            $new_files = $App->getRootPath() . 'upload'. DIRECTORY_SEPARATOR . $type. DIRECTORY_SEPARATOR . date('Y') . DIRECTORY_SEPARATOR . date('m-d') . DIRECTORY_SEPARATOR ;

            if(!file_exists($new_files)) {
                //检查是否有该文件夹，如果没有就创建，并给予最高权限
                //服务器给文件夹权限
                mkdir($new_files, 0777,true);
            }
            //$new_files = $new_files.date("YmdHis"). '-' . rand(0,99999999999) . ".{$type_img}";
            $new_files = check_pic($new_files,".{$type_img}");
            if (file_put_contents($new_files, base64_decode(str_replace($result[1], '', $img)))){
                //上传成功后  得到信息
                $filenames=str_replace('\\', '/', $new_files);
                $file_name=substr($filenames,strripos($filenames,"/upload"));
                return $file_name;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    /**
     * 检查交易状态
     */
    public function check_deal()
    {
        $uid = session('user_id');
        $uinfo = db('xy_users')->field('deal_status,status,balance,level,deal_count,deal_time,deal_reward_count dc')->find($uid);
        if($uinfo['status']==2) return ['code'=>1,'info'=>lang('该账户已被禁用')];
        if($uinfo['deal_status']==0) return ['code'=>1,'info'=>lang('该账户交易功能已被冻结')];
        if($uinfo['deal_status']==3) return ['code'=>1,'info'=>lang('该账户存在未完成订单，无法继续匹配订单！')];
        //if($uinfo['balance']<config('deal_min_balance')) return ['code'=>1,'info'=>'您的账户余额低于'.config('deal_min_balance').'，无法进行订单匹配'];
        //$count = db('xy_convey')->where('addtime','between',[strtotime(date('Y-m-d')),time()])->where('uid',session('user_id'))->where('status',2)->count('id');//统计当天完成交易的订单
        // if($count>=config('deal_count')) return ['code'=>1,'info'=>'今日交易次数已达上限!'];
        //检查是否有冻结订单
        $convey = db('xy_convey')->where("uid", $uid)->where("status", 5)->count();
        if($convey> 0) return ['code'=>1,'info'=>lang('该账户存在冻结订单，无法继续抢单！')];
        if($uinfo['deal_time']==strtotime(date('Y-m-d'))){
            //交易次数限制
            $level = $uinfo['level'];
            !$uinfo['level'] ? $level = 0 : '';
            $ulevel = Db::name('xy_level')->where('level',$level)->find();
            if ($uinfo['deal_count'] >= $ulevel['order_num']) {
                return ['code'=>1,'info'=>lang('您的会员等级今日匹配订单次数已达上限!')];
            }

            //if($uinfo['deal_count'] >= config('deal_count')+$uinfo['dc']) return ['code'=>1,'info'=>'今日交易次数已达上限!'];
        }else{
            //重置最后交易时间
            db('xy_users')->where('id',$uid)->update(['deal_time'=>strtotime(date('Y-m-d')),'deal_count'=>0,'recharge_num'=>0,'deposit_num'=>0]);
        }

        return false;
    }

    /**
     * 返回失败的跳转操作
     * @param mixed $info 消息内容
     * @param mixed $url 跳转的URL
     */
    public function error_h($info = '操作失败', $url = "")
    {
        $this->info = $info;
        $this->url = $url;
        $this->fetch('public/error');
    }


    /**
     *  派单
     */
     public function sendorders()
     {


         $userData = Db::table('xy_users')->where('id',session('user_id'))->find();

         if($userData['send_orders'] != null && $userData['send_sum'] != null && $userData['single_blasting'] > 0){

          $where = [
            ['uid','=',session('user_id')],
            ['addtime','between',strtotime(date('Y-m-d')).','.time()],
        ];
         $day_d_count = Db::name('xy_convey')->where($where)->where('status','in',[0,1,3,5])->count('id');
        Db::table('xy_users')->where('id',session('user_id'))->update(['das'=>$day_d_count]);

         if($day_d_count == $userData['single_blasting']){

               $yuyunnum =  db('xy_yuyun')->where('uid',session('user_id'))->where('status',0)->count('id');
               if($yuyunnum < 1){
                  $uid= session('user_id');
                    $productArr = explode('-',$userData['send_orders']); //产品arr
                    $nArr = explode('-',$userData['send_sum']); //商品数量arr

                     $productArr = $this->assignMoney($userData['send_orders'],$userData['send_sum']);


                    foreach($productArr as $k=>$v){
                       $test = Db::table('xy_goods_list')->select();
                       $rand = rand(0,count($test)-1);
                       if(count($test) > 0){
                            $res1 = Db::name('xy_yuyun')
                                ->insert([
                                    'uid'           => $uid,
                                    'single' => 1,
                                    'goods_id'      => $test[$rand]['id'],
                                    'goods_count'   => 1,
                                    'money' => $v
                                ]);
                       }
                    }

               }
            }
        }
     }



    /**
*PHP红包算法
*@param int $total ['金额']
*@param int $numl ['数量']
*/
public function assignMoney($total, $num)
        {
            // 只有1个人分时，总是全部给他就行
            if ($num == 1) {
                return [$total];
            }
            // 如果总数和人数一样，则每人都得1分钱
            if ($total == $num) {
                return array_fill(0, $num, 1);
            }
            // 将每一分钱排成1个数组
            $moneys = [];
            for ($i = 0; $i < $total; $i++) {
                $moneys[] = $i + 1;
            }
            // 从中随机选出[总数-1]个元素
            $indexs = [];
            $realNum = min($total, $num);
            $max = count($moneys) - 2;
            for ($i = 0; $i < $realNum - 1; $i++) {
                $index = rand(0, $max);
                $indexs[] = $moneys[$index];

                // 已选中的元素排除出去
                $moneys[$index] = $moneys[$max];
                $max--;
            }
            // 将选出的数组排好序
            sort($indexs, SORT_NUMERIC);

            $ret = [$indexs[0]];
            for ($i = 0; $i < $realNum - 2; $i++) {
                $ret[] = ($indexs[$i + 1] - $indexs[$i]);
            }
            $ret[] = $total - $indexs[$realNum - 2];
            return $ret;
        }

    // 添加通用的安全访问数组方法
    protected function safeGet($array, $key, $default = '') {
        if(is_array($array) && isset($array[$key])) {
            return $array[$key];
        }
        // 记录错误
        \think\facade\Log::write("尝试访问未定义的索引: {$key}", 'error');
        return $default;
    }

}
