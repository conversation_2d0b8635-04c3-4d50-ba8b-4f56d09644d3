<?php

namespace app\index\controller;

use think\Controller;
use think\Request;

class Chat extends Base
{
    /**
     * 聊天页面
     */
    public function index()
    {
        // 尝试从会话和Cookie中恢复用户ID
        $uid = session('user_id');
        
        // 如果会话中没有，检查Cookie
        if (!$uid && isset($_COOKIE['user_id'])) {
            $uid = $_COOKIE['user_id']; 
            session('user_id', $uid); // 恢复会话
        }
        
        $username = $uid ? db('xy_users')->where('id', $uid)->value('username') : '';
        
        // 调试参数
        $debug = input('get.debug', 0);
        if ($debug) {
            echo "<pre>";
            echo "用户ID: {$uid}, 用户名: {$username}\n";
            echo "会话ID: " . session_id() . "\n";
            echo "Cookie中的用户ID: " . (isset($_COOKIE['user_id']) ? $_COOKIE['user_id'] : '未设置') . "\n";
            echo "会话中的用户ID: " . session('user_id') . "\n";
            echo "所有会话信息:\n";
            print_r(session());
            echo "</pre>";
        }
        
        // 登录检查
        if (!$uid) {
            if ($debug) {
                echo "<p style='color:red'>未登录，即将跳转到登录页...</p>";
                exit;
            }
            return $this->redirect('/index/user/login');
        }
        
        // 检查是否已存在聊天室
        $room = db('xy_chat_room')->where('uid', $uid)->find();
        
        if (!$room) {
            // 创建聊天室
            try {
                $room_id = db('xy_chat_room')->insertGetId([
                    'uid' => $uid,
                    'username' => $username,
                    'unread' => 0,
                    'last_time' => time(),
                    'addtime' => time(),
                    'status' => 1
                ]);
                
                if ($debug) {
                    echo "<p style='color:green'>聊天室创建成功，ID: {$room_id}</p>";
                }
            } catch (\Exception $e) {
                if ($debug) {
                    echo "<p style='color:red'>聊天室创建失败: " . $e->getMessage() . "</p>";
                    exit;
                }
            }
        } else {
            $room_id = $room['id'];
            if ($debug) {
                echo "<p>已找到聊天室，ID: {$room_id}</p>";
            }
        }
        
        // 将未读消息标记为已读
        db('xy_chat_message')
            ->where('room_id', $room_id)
            ->where('from_type', 1) // 客服发送的消息
            ->where('status', 0) // 未读消息
            ->update(['status' => 1]);
            
        // 获取聊天记录
        $messages = db('xy_chat_message')
                    ->where('room_id', $room_id)
                    ->order('id asc')
                    ->select();
        
        $this->messages = $messages;
        $this->room_id = $room_id;
        
        if ($debug) {
            echo "<p>消息总数: " . count($messages) . "</p>";
            echo "<p>准备渲染模板...</p>";
        }
        
        return $this->fetch();
    }
    
    /**
     * 发送消息接口
     */
    public function send()
    {
        $uid = session('user_id');
        if (!$uid) {
            return json(['code' => 1, 'info' => lang('请先登录')]);
        }
        
        $room_id = input('post.room_id/d', 0);
        $content = input('post.content/s', '');
        
        if (empty($content)) {
            return json(['code' => 1, 'info' => lang('消息内容不能为空')]);
        }
        
        // 检查聊天室是否存在
        $room = db('xy_chat_room')->where('id', $room_id)->where('uid', $uid)->find();
        if (!$room) {
            return json(['code' => 1, 'info' => lang('聊天室不存在')]);
        }
        
        // 保存消息
        $data = [
            'room_id' => $room_id,
            'content' => $content,
            'from_type' => 0, // 0表示用户发送
            'addtime' => time(),
            'status' => 0, // 0表示未读
        ];
        
        $res = db('xy_chat_message')->insert($data);
        
        // 更新聊天室最后时间
        db('xy_chat_room')->where('id', $room_id)->update([
            'last_time' => time(),
            'unread' => \think\Db::raw('unread+1')
        ]);
        
        if ($res) {
            return json(['code' => 0, 'info' => lang('发送成功')]);
        } else {
            return json(['code' => 1, 'info' => lang('发送失败，请重试')]);
        }
    }
    
    /**
     * 获取新消息
     */
    public function getNewMessages()
    {
        $uid = session('user_id');
        if (!$uid) {
            return json(['code' => 1, 'info' => lang('请先登录')]);
        }
        
        $room_id = input('room_id/d', 0);
        $last_id = input('last_id/d', 0);
        
        // 检查聊天室是否存在
        $room = db('xy_chat_room')->where('id', $room_id)->where('uid', $uid)->find();
        if (!$room) {
            return json(['code' => 1, 'info' => lang('聊天室不存在')]);
        }
        
        // 获取新消息
        $messages = db('xy_chat_message')
                    ->where('room_id', $room_id)
                    ->where('id', '>', $last_id)
                    ->order('id asc')
                    ->select();
        
        // 将未读消息标记为已读
        db('xy_chat_message')
            ->where('room_id', $room_id)
            ->where('from_type', 1) // 客服发送的消息
            ->where('status', 0) // 未读消息
            ->update(['status' => 1]);
            
        // 格式化时间
        foreach ($messages as &$msg) {
            $msg['addtime_format'] = date('Y-m-d H:i:s', $msg['addtime']);
        }
        
        return json(['code' => 0, 'info' => lang('请求成功'), 'data' => $messages]);
    }
} 