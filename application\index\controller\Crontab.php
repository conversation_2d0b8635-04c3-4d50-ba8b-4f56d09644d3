<?php

// +----------------------------------------------------------------------
// | ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2019
// +----------------------------------------------------------------------

// +----------------------------------------------------------------------

// +----------------------------------------------------------------------
// |
// +---------------------------------------------------------------------

namespace app\index\controller;

use library\Controller;
use think\Db;

/**
 * 定时器
 */
class Crontab extends Controller
{
    /**
     * 生成订单号
     * @param string $prefix 前缀
     * @return string 订单号
     */
    private function getSn($prefix = '')
    {
        return $prefix . date('ymdHis') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
    }
    //冻结订单
    public function freeze_order()
    {
        $timeout = time()-config('deal_timeout');//超时订单
        $oinfo = Db::name('xy_convey')->where('status',0)->where('addtime','<=',$timeout)->field('id')->select();
        if($oinfo){
            foreach ($oinfo as $v) {
                Db::name('xy_convey')->where('id',$v['id'])->update(['status'=>5,'endtime'=>time()]);
            }
        }
        // 2020 10 27 仅冻结
        //$this->cancel_order();
        //$this->reset_deal();
    }

    //强制取消订单并冻结账户
    public function cancel_order()
    {
        $timeout = time()-config('deal_timeout');//超时订单
        //$oinfo = Db::name('xy_convey')->field('id oid,uid')->where('status',5)->where('endtime','<=',$timeout)->select();
        $oinfo = Db::name('xy_convey')->field('id oid,uid')->where('status',0)->where('endtime','<=',$timeout)->select();
        if($oinfo){
            foreach ($oinfo as $v) {
                Db::name('xy_convey')->where('id',$v['oid'])->update(['status'=>4,'endtime'=>time()]);
                $tmp =Db::name('xy_users')->field('deal_error,deal_status')->find($v['uid']);
                //记录违规信息
                if($tmp['deal_status']!=0){
                    if($tmp['deal_error'] < (int)config('deal_error')){
                        Db::name('xy_users')->where('id',$v['uid'])->update(['deal_status'=>1,'deal_error'=>Db::raw('deal_error+1')]);
                        Db::name('xy_user_error')->insert(['uid'=>$v['uid'],'oid'=>$v['oid'],'addtime'=>time(),'type'=>2]);
                    }elseif ($tmp['deal_error'] >= (int)config('deal_error')) {
                        Db::name('xy_users')->where('id',$v['uid'])->update(['deal_status'=>1,'deal_error'=>0]);
                        Db::name('xy_user_error')->insert(['uid'=>$v['uid'],'oid'=>$v['oid'],'addtime'=>time(),'type'=>3]);
                        //记录交易冻结信息
                    }
                }
            }
        }
    }

    //解冻账号
    public function reset_deal()
    {
        $uinfo = Db::name('xy_users')->where('deal_status',0)->field('id')->select();
        if($uinfo){
            foreach ($uinfo as $v) {
                $time = Db::name('xy_user_error')->where('uid',$v['id'])->where('type',3)->order('addtime desc')->limit(1)->value('addtime');
                if($time || $time <= time()-config('deal_feedze')){
                    //解封账号
                    Db::name('xy_users')->where('id',$v['id'])->update(['deal_status'=>1]);
                    Db::name('xy_user_error')->insert(['uid'=>$v['id'],'oid'=>'-','addtime'=>time(),'type'=>1]);
                }
            }
        }
    }

    //发放佣金
    public function do_reward()
    {
        try {
            $time = strtotime(date('Y-m-d', time()));//获取当天凌晨0点的时间戳
            $data = Db::name('xy_reward_log')->where('addtime','between', time()-3600*24 . ',' . time() )->where('status',1)->select();//获取当天佣金
            if($data){
                foreach ($data as $k => $v) {
                    Db::name('xy_users')->where('id',$v['uid'])->setInc('balance',$v['num']);
                    Db::name('xy_reward_log')->where('id',$v['id'])->update(['status'=>2,'endtime'=>time()]);
                }
            }
            echo 1;
        } catch (\Throwable $th) {
            //throw $th;
        }
    }


    //定时器 解除冻结 反还佣金和本金
    public function start333()
    {
        $oinfo = Db::name('xy_convey')->where('status',5)->where('endtime','<=',time())->select();
        if ($oinfo) {
            //
            foreach ($oinfo as $v) {
                //
                Db::name('xy_convey')->where('id',$v['id'])->update(['status'=>1]);

                //
                $res1 = Db::name('xy_users')
                    ->where('id', $v['uid'])
                    //->dec('balance',$info['num'])//
                    ->inc('balance',$v['num']+$v['commission'])
                    //->inc('freeze_balance',$info['num']+$info['commission']) //冻结商品金额 + 佣金//
                    ->dec('freeze_balance',$v['num']+$v['commission']) //冻结商品金额 + 佣金
                    ->update(['deal_status'=>1]);
                $this->deal_reward($v['uid'],$v['id'],$v['num'],$v['commission']);

                //
            }
        }
        $this->cancel_order();
        $this->reset_deal();
        //$this->lixibao_chu();
        //var_dump($oinfo,time(),date('Y-m-d H:i:s', 1577812622));die;
        return json(['code'=>1,'info'=>lang('执行成功！')]);
    }



    //------------------------------------------------------------------------------

    //强制取消订单并冻结账户
    public function start() {
        $timeout = time()-config('deal_timeout');//超时订单
        $timeout = time();//超时订单
        //$oinfo = Db::name('xy_convey')->field('id oid,uid')->where('status',5)->where('endtime','<=',$timeout)->select();
        $oinfo = Db::name('xy_convey')->where('status',0)->where('endtime','<=',$timeout)->select();
        if($oinfo){
            $djsc = config('deal_feedze'); //冻结时长 单位小时
            foreach ($oinfo as $v) {
                Db::name('xy_convey')->where('id',$v['id'])->update(['status'=>5,'endtime'=>time()+ $djsc * 60 *60]);
                //$res = Db::name('xy_convey')->where('id',$oid)->update($tmp);
                $res1 = Db::name('xy_users')
                    ->where('id', $v['uid'])
                    ->dec('balance',$v['num'])
                    ->inc('freeze_balance',$v['num']+$v['commission']) //冻结商品金额 + 佣金
                    ->update(['deal_status' => 1,'status'=>1]);

                $res2 = Db::name('xy_balance_log')->insert([
                    'uid'           => $v['uid'],
                    'oid'           => $v['id'],
                    'num'           => $v['num'],
                    'type'          => 2,
                    'status'        => 2,
                    'addtime'       => time()
                ]);
            }
        }

        //解冻
        $this->jiedong();
    }

    public function jiedong()
    {
        $oinfo = Db::name('xy_convey')->where('status',5)->where('endtime','<=',time())->select();
        if ($oinfo) {
            //
            foreach ($oinfo as $v) {
                //
                Db::name('xy_convey')->where('id',$v['id'])->update(['status'=>1]);

                //
                $res1 = Db::name('xy_users')
                    ->where('id', $v['uid'])
                    //->dec('balance',$info['num'])//
                    ->inc('balance',$v['num']+$v['commission'])
                    //->inc('freeze_balance',$info['num']+$info['commission']) //冻结商品金额 + 佣金//
                    ->dec('freeze_balance',$v['num']+$v['commission']) //冻结商品金额 + 佣金
                    ->update(['deal_status'=>1]);
                $this->deal_reward($v['uid'],$v['id'],$v['num'],$v['commission']);

                //
            }
        }
    }


    /**
     * 交易返佣
     *
     * @return void
     */
    public function deal_reward($uid,$oid,$num,$cnum)
    {
        ///$res = Db::name('xy_users')->where('id',$uid)->where('status',1)->setInc('balance',$num+$cnum);

//        $res1 = Db::name('xy_balance_log')->insert([
//            //记录返佣信息
//            'uid'       => $uid,
//            'oid'       => $oid,
//            'num'       => $num+$cnum,
//            'type'      => 3,
//            'addtime'   => time()
//        ]);
        Db::name('xy_balance_log')->where('oid',$oid)->update(['status'=>1]);


        //将订单状态改为已返回佣金
        Db::name('xy_convey')->where('id',$oid)->update(['c_status'=>1]);
        Db::name('xy_reward_log')->insert(['oid'=>$oid,'uid'=>$uid,'num'=>$num,'addtime'=>time(),'type'=>2]);//记录充值返佣订单
        /************* 发放交易奖励 *********/
        $userList = model('admin/Users')->parent_user($uid,5);
        //echo '<pre>';
        //var_dump($userList);die;
        if($userList){
            foreach($userList as $v){
                if($v['status']===1){
                    Db::name('xy_reward_log')
                        ->insert([
                            'uid'       => $v['id'],
                            'sid'       => $v['pid'],
                            'oid'       => $oid,
                            'num'       => $num*config($v['lv'].'_d_reward'),
                            'lv'        => $v['lv'],
                            'type'      => 2,
                            'status'    => 1,
                            'addtime'   => time(),
                        ]);

                    //
                    $res1 = Db::name('xy_balance_log')->insert([
                        //记录返佣信息
                        'uid'       => $v['id'],
                        'oid'       => $oid,
                        'sid'       => $uid,
                        'num'       => $cnum*config($v['lv'].'_d_reward'),
                        'type'      => 6,
                        'status'    => 1,
                        'f_lv'        => $v['lv'],
                        'addtime'   => time()
                    ]);

                }

                //
                $num3 = $num*config($v['lv'].'_d_reward'); //佣金
                $res = Db::name('xy_users')->where('id',$v['id'])->where('status',1)->setInc('balance',$num3);
                $res2 = Db::name('xy_balance_log')->insert([
                    'uid'           => $v['id'],
                    'oid'           => $oid,
                    'num'           => $num3,
                    'type'          => 6,
                    'status'        => 1,
                    'addtime'       => time()
                ]);

            }
        }
        /************* 发放交易奖励 *********/

    }


   //----------------------------利息宝---------------------------------
    //1 转入 2转出  3每日收益
    public function lixibao_chu()
    {
        //处理从余额里转出的到账时间
        $addMax = time() - ( (config('lxb_time')) * 60*60 ); //向前退一个小时
        $res = Db::name('xy_lixibao')->where('status',0)->where('addtime','<=',$addMax)->where('type',2)->select();
        if ($res) {
            foreach ($res as $re) {
                $uid = $re['uid'];
                $num = $re['num'];

                Db::name('xy_users')->where('id',$re['id'])->setDec('lixibao_dj_balance',$num);  //利息宝月 -
                Db::name('xy_users')->where('id',$uid)->setInc('balance',$num);  //余额 +
                Db::name('xy_lixibao')->where('id',$re['id'])->update(['status'=>1]);  //利息宝月 -
            }
        }
    }

    public function lxb_jiesuan()
    {
        $result = [
            'success' => true,
            'message' => '处理投资到期结算',
            'processed_count' => 0,
            'details' => []
        ];

        $now = time();
        // 查找已到期但未结算的投资记录
        $lxb = Db::name('xy_lixibao')->where('endtime','<',$now)
            ->where('is_qu',0)  // 未取出
            ->where('type',1)   // 转入类型
            ->select();

        // 创建日志文件
        $log_file = app()->getRuntimePath() . 'log/lixibao_maturity.log';
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - 开始处理投资到期结算\n", FILE_APPEND);

        if ($lxb) {
            foreach ($lxb as $item) {
                $price = $item['num'];    // 投资本金
                $uid   = $item['uid'];    // 用户ID
                $id    = $item['id'];     // 投资记录ID
                
                // 获取产品信息
                $lixibao = Db::name('xy_lixibao_list')->find($item['sid']);
                if (!$lixibao) {
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - 警告：找不到产品信息，ID: {$item['sid']}\n", FILE_APPEND);
                    continue;
                }

                // **关键修改：不再计算总收益，因为每日收益已经在lixibao_js中发放了**
                // 旧逻辑：$sy = $price * $lixibao['bili'] * $lixibao['day']; 
                // 新逻辑：只返还本金，收益已经每天发放过了
                
                try {
                    // 开始事务
                    Db::startTrans();
                    
                    // 1. 从余额宝余额中扣除本金
                    Db::name('xy_users')->where('id',$uid)->setDec('lixibao_balance',$price);
                    
                    // 2. 将本金返还到用户余额（不再额外给收益，因为已经每天发放了）
                    Db::name('xy_users')->where('id',$uid)->setInc('balance',$price);
                    
                    // 3. 更新投资记录状态为已取出
                    Db::name('xy_lixibao')->where('id',$id)->update([
                        'is_qu'      => 1,    // 标记为已取出
                        'is_sy'      => 1,    // 标记为已结算
                        'real_num'   => $item['yuji_num'] // 实际收益设置为预期收益（已通过每日发放完成）
                    ]);
                    
                    // 4. 记录本金返还的余额变动
                    Db::name('xy_balance_log')->insert([
                        'uid'       => $uid,
                        'oid'       => $id,
                        'num'       => $price,
                        'type'      => 22,    // 本金返还
                        'status'    => 1,
                        'addtime'   => time()
                    ]);
                    
                    // 提交事务
                    Db::commit();
                    
                    $result['processed_count']++;
                    $result['details'][] = [
                        'investment_id' => $id,
                        'user_id' => $uid,
                        'principal' => $price,
                        'product_name' => $lixibao['name'],
                        'start_time' => date('Y-m-d H:i:s', $item['addtime']),
                        'end_time' => date('Y-m-d H:i:s', $item['endtime'])
                    ];
                    
                    // 记录处理日志
                    $log_msg = "处理到期投资: 用户ID $uid, 投资ID $id, 产品 {$lixibao['name']}, 返还本金 $price";
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $log_msg\n", FILE_APPEND);
                    
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    $error_msg = "处理投资到期失败: 投资ID $id, 错误: " . $e->getMessage();
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $error_msg\n", FILE_APPEND);
                    $result['success'] = false;
                    $result['message'] .= " - 处理失败: $error_msg";
                }
            }
        }
        
        $result['message'] .= " - 处理完成，共处理 {$result['processed_count']} 条到期投资";
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - {$result['message']}\n", FILE_APPEND);
        
        return json(['code' => $result['success'] ? 0 : 1, 'info' => $result['message'], 'data' => $result]);
    }



    /**
     * @地址      lixibao_js
     * @说明      每天12点 10分左右计算 前一天的收益  切莫重复
     * @说明      域名为  http://域名/index/crontab/lixibao_js
     * @参数      @参数 @参数
     */
    //结算 //
    public function lixibao_js()
    {
        // 初始化结果数组
        $result = [
            'success' => true,
            'message' => lang('执行成功'),
            'processed_users' => 0,
            'total_income' => 0,
            'details' => []
        ];

        // 记录当前系统时间和北京时间
        $system_time = date('Y-m-d H:i:s');
        // 获取北京时间（UTC+8）
        $beijing_time = gmdate('Y-m-d H:i:s', time() + 8 * 3600);
        $result['system_time'] = $system_time;
        $result['beijing_time'] = $beijing_time;

        // 检查xy_lixibao表中是否有update_time字段
        try {
            $check_field_sql = "SHOW COLUMNS FROM xy_lixibao LIKE 'update_time'";
            $field_exists = Db::query($check_field_sql);

            if (empty($field_exists)) {
                // 如果字段不存在，添加update_time字段
                $add_field_sql = "ALTER TABLE xy_lixibao ADD COLUMN update_time int(11) NOT NULL DEFAULT 0 COMMENT '最后更新时间'";
                Db::execute($add_field_sql);
                $result['message'] .= " - 已添加update_time字段";
            }
        } catch (\Exception $e) {
            $result['message'] .= " - 检查字段失败: " . $e->getMessage();
        }

        // 查找余额宝收益记录中status为NULL或0的记录
        try {
            $invalid_records = Db::name('xy_balance_log')
                ->where('type', 23)
                ->where(function($query) {
                    $query->whereNull('status')->whereOr('status', 0);
                })
                ->select();

            if (!empty($invalid_records)) {
                foreach ($invalid_records as $record) {
                    Db::name('xy_balance_log')
                        ->where('id', $record['id'])
                        ->update(['status' => 1]);
                }
                $result['message'] .= " - 已修复" . count($invalid_records) . "条收益记录状态";
            }
        } catch (\Exception $e) {
            $result['message'] .= " - 修复收益记录状态失败: " . $e->getMessage();
        }

        // 使用当前服务器时间计算今天的开始和结束时间戳
        $current_time = time();
        $today_date = date('Y-m-d', $current_time);
        $today_start = strtotime($today_date . ' 00:00:00');
        $today_end = strtotime($today_date . ' 23:59:59');

        $result['today_start'] = date('Y-m-d H:i:s', $today_start);
        $result['today_end'] = date('Y-m-d H:i:s', $today_end);

        // 创建日志文件
        $log_file = app()->getRuntimePath() . 'log/lixibao_debug.log';
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - 开始计算收益，今日范围：$today_date\n", FILE_APPEND);

        // 获取所有有投资记录且仍在投资期间的用户
        $users_with_active_investments = Db::name('xy_lixibao')
            ->alias('xl')
            ->leftJoin('xy_users u', 'u.id=xl.uid')
            ->where('xl.type', 1) // 转入类型
            ->where('xl.is_qu', 0) // 未取出
            ->where('xl.endtime', '>', $current_time) // 仍在投资期间内
            ->field('u.id, u.username, u.lixibao_balance')
            ->group('u.id')
            ->select();

        file_put_contents($log_file, date('Y-m-d H:i:s') . " - 找到 " . count($users_with_active_investments) . " 个有活跃投资的用户\n", FILE_APPEND);

        if ($users_with_active_investments) {
            foreach ($users_with_active_investments as $item) {
                $uid = $item['id'];
                $username = $item['username'];

                // **修复：检查今天是否已经发放过收益（但保留每日应发放的逻辑）**
                // 注意：对于利息宝产品，每天都应该产生收益直到投资到期
                $today_income_check = Db::name('xy_balance_log')
                    ->where('uid', $uid)
                    ->where('type', 23)
                    ->where('status', 1)
                    ->where('addtime', '>=', $today_start)
                    ->where('addtime', '<=', $today_end)
                    ->count();

                if ($today_income_check > 0) {
                    // 今天已经发放过收益，跳过（避免重复发放）
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - 用户 $username (ID: $uid) 今日已发放收益，跳过\n", FILE_APPEND);
                    continue;
                }

                // ---- 新算法：按产品分组计算收益 ----
                
                // 1.获取用户所有未取出且在投资期间内的投资记录
                $user_investments = Db::name('xy_lixibao')
                    ->where('uid', $uid)
                    ->where('type', 1) // 转入类型
                    ->where('is_qu', 0) // 未取出
                    ->where('endtime', '>', $current_time) // 添加：仍在投资期间内
                    ->select();
                
                // 记录调试信息
                $debug_info = "用户 $username (ID: $uid), 在投资期间内的投资记录数: " . count($user_investments);
                file_put_contents($log_file, date('Y-m-d H:i:s') . " - $debug_info\n", FILE_APPEND);
                
                // 2.按产品类型分组计算每种产品的投资总额
                $product_amounts = [];
                $product_names = [];
                $product_rates = [];
                $product_invest_details = []; // 记录投资详情用于调试
                
                if (!empty($user_investments)) {
                    foreach ($user_investments as $investment) {
                        $product_id = $investment['sid'];
                        
                        // 检查投资是否已过期（双重检查）
                        if ($investment['endtime'] <= $current_time) {
                            // 记录过期投资
                            $expire_msg = "跳过过期投资: 投资ID {$investment['id']}, 到期时间 " . date('Y-m-d H:i:s', $investment['endtime']);
                            file_put_contents($log_file, date('Y-m-d H:i:s') . " - $expire_msg\n", FILE_APPEND);
                            continue;
                        }
                        
                        // 如果是首次遇到该产品，获取产品信息
                        if (!isset($product_amounts[$product_id])) {
                            $product_amounts[$product_id] = 0;
                            $product_invest_details[$product_id] = [];
                            
                            // 获取产品信息：名称和利率
                            $product_info = Db::name('xy_lixibao_list')
                                ->where('id', $product_id)
                                ->field('name, bili, day')
                                ->find();
                                
                            if ($product_info) {
                                $product_names[$product_id] = $product_info['name'];
                                $product_rates[$product_id] = $product_info['bili'];
                            } else {
                                // 如果找不到产品信息，使用默认值
                                $product_names[$product_id] = "产品#$product_id";
                                // 尝试获取默认利率
                                $default_rate = config('lxb_bili');
                                $product_rates[$product_id] = empty($default_rate) ? 0.05 : floatval($default_rate);
                            }
                        }
                        
                        // 累加该产品类型的投资金额
                        $product_amounts[$product_id] += $investment['num'];
                        
                        // 记录投资详情
                        $product_invest_details[$product_id][] = [
                            'investment_id' => $investment['id'],
                            'amount' => $investment['num'],
                            'start_time' => date('Y-m-d H:i:s', $investment['addtime']),
                            'end_time' => date('Y-m-d H:i:s', $investment['endtime']),
                            'days_left' => ceil(($investment['endtime'] - $current_time) / (24 * 3600))
                        ];
                    }
                }
                
                // 3.根据每种产品的利率计算每日收益
                $total_income = 0;
                $income_details = [];
                
                foreach ($product_amounts as $product_id => $amount) {
                    if ($amount <= 0) continue; // 跳过零金额
                    
                    $rate = $product_rates[$product_id];
                    $product_income = $amount * $rate; // 每日收益 = 投资金额 * 日利率
                    $total_income += $product_income;
                    
                    // 记录每种产品的收益明细
                    $income_details[] = [
                        'product_id' => $product_id,
                        'product_name' => $product_names[$product_id],
                        'amount' => $amount,
                        'rate' => $rate,
                        'income' => $product_income,
                        'investment_details' => $product_invest_details[$product_id]
                    ];
                    
                    // 记录调试信息
                    $debug_msg = "产品ID: $product_id, 名称: {$product_names[$product_id]}, 有效投资金额: $amount, 日利率: " . ($rate*100) . "%, 今日收益: $product_income";
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $debug_msg\n", FILE_APPEND);
                    
                    // 记录该产品的投资详情
                    foreach ($product_invest_details[$product_id] as $detail) {
                        $detail_msg = "  - 投资ID: {$detail['investment_id']}, 金额: {$detail['amount']}, 剩余天数: {$detail['days_left']}天";
                        file_put_contents($log_file, date('Y-m-d H:i:s') . " $detail_msg\n", FILE_APPEND);
                    }
                }
                
                // 如果没有有效投资记录但用户有余额宝余额，使用旧的计算方式
                if (empty($income_details) && $item['lixibao_balance'] > 0) {
                    // 有效余额计算
                    $yue = $item['lixibao_balance'];
                    
                    // 获取利率配置
                    $rate = config('lxb_bili');
                    
                    // 如果利率为0或不存在，尝试获取默认利率
                    if (empty($rate) || $rate == '0') {
                        try {
                            $latest_rate = Db::name('xy_lixibao_list')
                                ->where('status', 1)
                                ->order('addtime desc')
                                ->value('bili');
                                
                            if (!empty($latest_rate) && $latest_rate > 0) {
                                $rate = floatval($latest_rate);
                            } else {
                                $rate = 0.05; // 默认利率5%
                            }
                        } catch (\Exception $e) {
                            $rate = 0.05; // 如果查询失败，使用默认值
                        }
                    } else {
                        $rate = floatval($rate);
                    }
                    
                    // 计算收益
                    $total_income = $yue * $rate;
                    
                    // 获取默认产品
                    $default_product = Db::name('xy_lixibao_list')
                        ->where('status', 1)
                        ->order('id asc')
                        ->find();
                        
                    $default_product_id = $default_product ? $default_product['id'] : 0;
                    $default_product_name = $default_product ? $default_product['name'] : 'Type F';
                    
                    $income_details[] = [
                        'product_id' => $default_product_id,
                        'product_name' => $default_product_name,
                        'amount' => $yue,
                        'rate' => $rate,
                        'income' => $total_income
                    ];
                    
                    // 记录调试信息
                    $debug_msg = "使用默认计算: 余额=$yue, 利率=" . ($rate*100) . "%, 收益=$total_income";
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $debug_msg\n", FILE_APPEND);
                }
                
                // 如果没有任何收益，跳过该用户
                if ($total_income <= 0) {
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - 用户 $username (ID: $uid) 无有效投资或收益为0，跳过\n", FILE_APPEND);
                    continue;
                }
                
                // 4.记录用户收益详情
                $user_detail = [
                    'uid' => $uid,
                    'username' => $username,
                    'balance' => $item['lixibao_balance'],
                    'income' => $total_income,
                    'product_details' => $income_details
                ];
                
                $result['details'][] = $user_detail;
                $result['processed_users']++;
                $result['total_income'] += $total_income;
                
                // 5.更新用户余额 - 只增加余额，不增加余额宝余额
                Db::name('xy_users')->where('id', $uid)->setInc('balance', $total_income);
                
                // 为每个产品添加收益记录
                foreach ($income_details as $detail) {
                    // 添加收益记录到xy_lixibao表
                    $product_income = $detail['income'];
                    $product_id = $detail['product_id'];
                    $product_name = $detail['product_name'];
                    $product_rate = $detail['rate'];
                    
                    Db::name('xy_lixibao')->insert([
                        'uid'         => $uid,
                        'num'         => $product_income,
                        'addtime'     => $current_time,
                        'type'        => 3,
                        'status'      => 1,
                        'yuji_num'    => $product_income,  // 设置预计收益
                        'real_num'    => $product_income,  // 设置实际收益
                        'is_sy'       => 1,                // 标记为已收益
                        'sid'         => $product_id,      // 设置产品ID
                        'shouxu'      => 0,                // 无手续费
                        'bili'        => $product_rate,    // 设置利率
                        'day'         => 1,                // 日收益
                        'update_time' => $current_time,    // 设置更新时间
                    ]);
                    
                    // 记录插入结果
                    $insert_id = Db::name('xy_lixibao')->getLastInsID();
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - 插入收益记录: ID $insert_id, 产品 $product_name (ID $product_id), 收益 $product_income\n", FILE_APPEND);
                }
                
                // 添加余额变动记录到xy_balance_log表
                $oid = $this->getSn('LXB');
                Db::name('xy_balance_log')->insert([
                    'uid'       => $uid,
                    'oid'       => $oid,
                    'num'       => $total_income,
                    'type'      => 23,
                    'status'    => 1,
                    'addtime'   => $current_time
                ]);
                
                file_put_contents($log_file, date('Y-m-d H:i:s') . " - 用户 $username (ID: $uid) 收益发放成功，总金额: $total_income\n", FILE_APPEND);
            }
        } else {
            $result['message'] = lang('没有找到有活跃投资的用户');
            file_put_contents($log_file, date('Y-m-d H:i:s') . " - 没有找到有活跃投资的用户\n", FILE_APPEND);
        }

        file_put_contents($log_file, date('Y-m-d H:i:s') . " - 收益计算完成，处理用户数: {$result['processed_users']}, 总收益: {$result['total_income']}\n", FILE_APPEND);

        // 返回HTML格式的结果，方便在浏览器中查看
        echo '<html><head><title>余额宝收益计算结果</title>';
        echo '<meta charset="utf-8">';
        echo '<style>body{font-family:Arial,sans-serif;margin:20px;line-height:1.6;}';
        echo '.success{color:green;} .error{color:red;} table{border-collapse:collapse;width:100%;}';
        echo 'th,td{border:1px solid #ddd;padding:8px;text-align:left;}';
        echo 'th{background-color:#f2f2f2;} tr:nth-child(even){background-color:#f9f9f9;}</style></head>';
        echo '<body><h1>余额宝收益计算结果</h1>';
        echo '<p class="'.($result['success'] ? 'success' : 'error').'">'.$result['message'].'</p>';
        echo '<p>系统时间: '.$result['system_time'].'</p>';
        echo '<p>北京时间: '.$result['beijing_time'].'</p>';
        echo '<p>今日开始时间: '.$result['today_start'].'</p>';
        echo '<p>今日结束时间: '.$result['today_end'].'</p>';
        echo '<p>处理用户数: '.$result['processed_users'].'</p>';
        echo '<p>总收益金额: '.$result['total_income'].'</p>';

        if (!empty($result['details'])) {
            echo '<h2>用户收益汇总</h2><table>';
            echo '<tr><th>用户ID</th><th>用户名</th><th>余额宝总余额</th><th>总收益金额</th></tr>';
            foreach ($result['details'] as $detail) {
                echo '<tr>';
                echo '<td>'.$detail['uid'].'</td>';
                echo '<td>'.$detail['username'].'</td>';
                echo '<td>'.$detail['balance'].'</td>';
                echo '<td>'.$detail['income'].'</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            echo '<h2>收益明细（按产品类型）</h2>';
            foreach ($result['details'] as $user) {
                echo '<h3>用户: '.$user['username'].' (ID: '.$user['uid'].')</h3>';
                echo '<table>';
                echo '<tr><th>产品ID</th><th>产品名称</th><th>投资金额</th><th>收益率</th><th>收益金额</th></tr>';
                
                foreach ($user['product_details'] as $product) {
                    echo '<tr>';
                    echo '<td>'.$product['product_id'].'</td>';
                    echo '<td>'.$product['product_name'].'</td>';
                    echo '<td>'.$product['amount'].'</td>';
                    echo '<td>'.($product['rate']*100).'%</td>';
                    echo '<td>'.$product['income'].'</td>';
                    echo '</tr>';
                }
                
                echo '</table>';
            }
        }

        echo '</body></html>';

        // 返回JSON格式的结果，方便API调用
        return json(['code'=>1, 'info'=>lang('执行成功'), 'data'=>$result]);
    }



    /**
     * 会员有效期
     * 1分钟一次
     * /index/crontab/member_level_validity
     */
    public function member_level_validity() {
        $date = date("Y-m-d H:i:s");
        $list = Db::name('xy_users')->where("level > 1 and level_validity != 99 and level_validity < '{$date}'")->select();
        if (!empty($list)) {
            $ids = array_column($list, 'id');
            $up = [
                'level' => 1
            ];
            Db::name('xy_users')->where("id", "in", $ids)->update($up);
        }
        exit('end');
    }

    /**
     * @地址      recalculate_lixibao_income
     * @说明      重新计算指定日期范围内的理财宝收益
     * @说明      域名为  http://域名/index/crontab/recalculate_lixibao_income?start_date=2025-05-19&end_date=2025-05-21
     * @参数      start_date 开始日期（格式：Y-m-d）
     * @参数      end_date 结束日期（格式：Y-m-d）
     */
    public function recalculate_lixibao_income()
    {
        // 验证管理员权限（实际使用时请根据您的系统权限验证方式调整）
        /*
        if (!$this->isAdmin()) {
            return json(['code'=>0, 'info'=>lang('无权操作')]);
        }
        */
        
        // 初始化结果数组
        $result = [
            'success' => true,
            'message' => lang('操作成功'),
            'deleted_records' => 0,
            'recalculated_users' => 0,
            'total_income' => 0,
            'details' => []
        ];
        
        // 获取日期参数
        $start_date = input('get.start_date', '');
        $end_date = input('get.end_date', '');
        
        if (empty($start_date) || empty($end_date)) {
            return json(['code'=>0, 'info'=>lang('请提供开始日期和结束日期')]);
        }
        
        // 转换为时间戳
        $start_timestamp = strtotime($start_date . ' 00:00:00');
        $end_timestamp = strtotime($end_date . ' 23:59:59');
        
        if ($start_timestamp === false || $end_timestamp === false) {
            return json(['code'=>0, 'info'=>lang('日期格式不正确，请使用Y-m-d格式')]);
        }
        
        // 1. 查找并删除指定日期范围内的收益记录（type=23的余额记录和type=3的理财宝记录）
        try {
            // 查找需要删除的收益记录
            $income_records = Db::name('xy_balance_log')
                ->where('type', 23) // 余额宝收益类型
                ->where('addtime', 'between', [$start_timestamp, $end_timestamp])
                ->select();
                
            $income_record_ids = [];
            $affected_users = [];
            $income_amounts = [];
            
            // 收集需要删除的记录ID和受影响的用户
            foreach ($income_records as $record) {
                $income_record_ids[] = $record['id'];
                $uid = $record['uid'];
                if (!isset($affected_users[$uid])) {
                    $affected_users[$uid] = 0;
                }
                if (!isset($income_amounts[$uid])) {
                    $income_amounts[$uid] = 0;
                }
                $income_amounts[$uid] += $record['num'];
                
                // 从用户余额中减去已发放的收益
                Db::name('xy_users')
                    ->where('id', $uid)
                    ->setDec('balance', $record['num']);
            }
            
            // 删除收益记录
            if (!empty($income_record_ids)) {
                Db::name('xy_balance_log')
                    ->where('id', 'in', $income_record_ids)
                    ->delete();
                $result['deleted_records'] += count($income_record_ids);
            }
            
            // 查找并删除同一时间段内的理财宝收益记录
            $lixibao_records = Db::name('xy_lixibao')
                ->where('type', 3) // 理财宝收益类型
                ->where('addtime', 'between', [$start_timestamp, $end_timestamp])
                ->select();
                
            $lixibao_record_ids = [];
            
            foreach ($lixibao_records as $record) {
                $lixibao_record_ids[] = $record['id'];
            }
            
            // 删除理财宝收益记录
            if (!empty($lixibao_record_ids)) {
                Db::name('xy_lixibao')
                    ->where('id', 'in', $lixibao_record_ids)
                    ->delete();
                $result['deleted_records'] += count($lixibao_record_ids);
            }
            
            $result['message'] .= " - 已删除{$result['deleted_records']}条收益记录";
        } catch (\Exception $e) {
            return json(['code'=>0, 'info'=>lang('删除旧记录失败') . ': ' . $e->getMessage()]);
        }
        
        // 2. 重新计算收益
        
        // 获取所有余额宝余额大于0的用户
        $uinfo = Db::name('xy_users')->where('lixibao_balance','>',0)->select();
        
        if ($uinfo) {
            // 创建日志文件
            $log_file = app()->getRuntimePath() . 'log/lixibao_recalculate.log';
            file_put_contents($log_file, date('Y-m-d H:i:s') . " - 开始重新计算收益\n", FILE_APPEND);
            
            foreach ($uinfo as $item) {
                $uid = $item['id'];
                
                // 1.获取用户所有未取出的投资记录
                $user_investments = Db::name('xy_lixibao')
                    ->where('uid', $uid)
                    ->where('type', 1) // 转入类型
                    ->where('is_qu', 0) // 未取出
                    ->select();
                
                // 记录调试信息
                $debug_info = "用户ID: $uid, 投资记录数: " . count($user_investments);
                file_put_contents($log_file, date('Y-m-d H:i:s') . " - $debug_info\n", FILE_APPEND);
                
                // 2.按产品类型分组计算每种产品的投资总额
                $product_amounts = [];
                $product_names = [];
                $product_rates = [];
                
                if (!empty($user_investments)) {
                    foreach ($user_investments as $investment) {
                        $product_id = $investment['sid'];
                        
                        // 如果是首次遇到该产品，获取产品信息
                        if (!isset($product_amounts[$product_id])) {
                            $product_amounts[$product_id] = 0;
                            
                            // 获取产品信息：名称和利率
                            $product_info = Db::name('xy_lixibao_list')
                                ->where('id', $product_id)
                                ->field('name, bili')
                                ->find();
                                
                            if ($product_info) {
                                $product_names[$product_id] = $product_info['name'];
                                $product_rates[$product_id] = $product_info['bili'];
                            } else {
                                // 如果找不到产品信息，使用默认值
                                $product_names[$product_id] = "Type " . $product_id;
                                // 尝试获取默认利率
                                $default_rate = config('lxb_bili');
                                $product_rates[$product_id] = empty($default_rate) ? 0.05 : floatval($default_rate);
                            }
                        }
                        
                        // 累加该产品类型的投资金额
                        $product_amounts[$product_id] += $investment['num'];
                    }
                }
                
                // 3.根据每种产品的利率计算收益
                $total_income = 0;
                $income_details = [];
                
                foreach ($product_amounts as $product_id => $amount) {
                    $rate = $product_rates[$product_id];
                    $product_income = $amount * $rate;
                    $total_income += $product_income;
                    
                    // 记录每种产品的收益明细
                    $income_details[] = [
                        'product_id' => $product_id,
                        'product_name' => $product_names[$product_id],
                        'amount' => $amount,
                        'rate' => $rate,
                        'income' => $product_income
                    ];
                    
                    // 记录调试信息
                    $debug_msg = "产品ID: $product_id, 名称: {$product_names[$product_id]}, 投资金额: $amount, 利率: " . ($rate*100) . "%, 收益: $product_income";
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $debug_msg\n", FILE_APPEND);
                }
                
                // 如果没有有效投资记录，使用旧的计算方式（基于总余额）
                if (empty($income_details)) {
                    // 有效余额计算
                    $yue = $item['lixibao_balance'];
                    
                    // 获取利率配置
                    $rate = config('lxb_bili');
                    
                    // 如果利率为0或不存在，尝试获取默认利率
                    if (empty($rate) || $rate == '0') {
                        try {
                            $latest_rate = Db::name('xy_lixibao_list')
                                ->where('status', 1)
                                ->order('addtime desc')
                                ->value('bili');
                                
                            if (!empty($latest_rate) && $latest_rate > 0) {
                                $rate = floatval($latest_rate);
                            } else {
                                $rate = 0.05; // 默认利率5%
                            }
                        } catch (\Exception $e) {
                            $rate = 0.05; // 如果查询失败，使用默认值
                        }
                    } else {
                        $rate = floatval($rate);
                    }
                    
                    // 计算收益
                    $total_income = $yue * $rate;
                    
                    // 获取默认产品
                    $default_product = Db::name('xy_lixibao_list')
                        ->where('status', 1)
                        ->order('id asc')
                        ->find();
                        
                    $default_product_id = $default_product ? $default_product['id'] : 0;
                    $default_product_name = $default_product ? $default_product['name'] : 'Type F';
                    
                    $income_details[] = [
                        'product_id' => $default_product_id,
                        'product_name' => $default_product_name,
                        'amount' => $yue,
                        'rate' => $rate,
                        'income' => $total_income
                    ];
                    
                    // 记录调试信息
                    $debug_msg = "使用默认计算: 余额=$yue, 利率=" . ($rate*100) . "%, 收益=$total_income";
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $debug_msg\n", FILE_APPEND);
                }
                
                // 4.记录用户收益详情
                $user_detail = [
                    'uid' => $uid,
                    'username' => Db::name('xy_users')->where('id', $uid)->value('username'),
                    'balance' => $item['lixibao_balance'],
                    'income' => $total_income,
                    'product_details' => $income_details
                ];
                
                $result['details'][] = $user_detail;
                $result['recalculated_users']++;
                $result['total_income'] += $total_income;
                
                // 5.更新用户余额 - 只增加余额，不增加余额宝余额
                Db::name('xy_users')->where('id', $uid)->setInc('balance', $total_income);
                
                // 当前时间戳
                $current_time = time();
                
                // 为每个产品添加收益记录
                foreach ($income_details as $detail) {
                    // 添加收益记录到xy_lixibao表
                    $product_income = $detail['income'];
                    $product_id = $detail['product_id'];
                    $product_name = $detail['product_name'];
                    $product_rate = $detail['rate'];
                    
                    Db::name('xy_lixibao')->insert([
                        'uid'         => $uid,
                        'num'         => $product_income,
                        'addtime'     => $current_time,
                        'type'        => 3,
                        'status'      => 1,
                        'yuji_num'    => $product_income,  // 设置预计收益
                        'real_num'    => $product_income,  // 设置实际收益
                        'is_sy'       => 1,                // 标记为已收益
                        'sid'         => $product_id,      // 设置产品ID
                        'shouxu'      => 0,                // 无手续费
                        'bili'        => $product_rate,    // 设置利率
                        'day'         => 1,                // 日收益
                        'update_time' => $current_time,    // 设置更新时间
                    ]);
                    
                    // 记录插入结果
                    $insert_id = Db::name('xy_lixibao')->getLastInsID();
                    file_put_contents($log_file, date('Y-m-d H:i:s') . " - 插入收益记录: ID $insert_id, 产品 $product_name (ID $product_id), 收益 $product_income\n", FILE_APPEND);
                }
                
                // 添加余额变动记录到xy_balance_log表
                $oid = $this->getSn('LXB');
                Db::name('xy_balance_log')->insert([
                    'uid'       => $uid,
                    'oid'       => $oid,
                    'num'       => $total_income,
                    'type'      => 23,
                    'status'    => 1,
                    'addtime'   => $current_time
                ]);
            }
        } else {
            $result['message'] = lang('没有找到余额宝余额大于0的用户');
        }
        
        // 返回HTML格式的结果，方便在浏览器中查看
        echo '<html><head><title>余额宝收益重新计算结果</title>';
        echo '<meta charset="utf-8">';
        echo '<style>body{font-family:Arial,sans-serif;margin:20px;line-height:1.6;}';
        echo '.success{color:green;} .error{color:red;} table{border-collapse:collapse;width:100%;}';
        echo 'th,td{border:1px solid #ddd;padding:8px;text-align:left;}';
        echo 'th{background-color:#f2f2f2;} tr:nth-child(even){background-color:#f9f9f9;}</style></head>';
        echo '<body><h1>余额宝收益重新计算结果</h1>';
        echo '<p class="'.($result['success'] ? 'success' : 'error').'">'.$result['message'].'</p>';
        echo '<p>处理日期范围: '.$start_date.' 至 '.$end_date.'</p>';
        echo '<p>删除的收益记录数: '.$result['deleted_records'].'</p>';
        echo '<p>重新计算用户数: '.$result['recalculated_users'].'</p>';
        echo '<p>总收益金额: '.$result['total_income'].'</p>';
        
        if (!empty($result['details'])) {
            echo '<h2>用户收益汇总</h2><table>';
            echo '<tr><th>用户ID</th><th>用户名</th><th>余额宝总余额</th><th>总收益金额</th></tr>';
            foreach ($result['details'] as $detail) {
                echo '<tr>';
                echo '<td>'.$detail['uid'].'</td>';
                echo '<td>'.$detail['username'].'</td>';
                echo '<td>'.$detail['balance'].'</td>';
                echo '<td>'.$detail['income'].'</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            echo '<h2>收益明细（按产品类型）</h2>';
            foreach ($result['details'] as $user) {
                echo '<h3>用户: '.$user['username'].' (ID: '.$user['uid'].')</h3>';
                echo '<table>';
                echo '<tr><th>产品ID</th><th>产品名称</th><th>投资金额</th><th>收益率</th><th>收益金额</th></tr>';
                
                foreach ($user['product_details'] as $product) {
                    echo '<tr>';
                    echo '<td>'.$product['product_id'].'</td>';
                    echo '<td>'.$product['product_name'].'</td>';
                    echo '<td>'.$product['amount'].'</td>';
                    echo '<td>'.($product['rate']*100).'%</td>';
                    echo '<td>'.$product['income'].'</td>';
                    echo '</tr>';
                }
                
                echo '</table>';
            }
        }
        
        echo '</body></html>';
        
        // 返回JSON格式的结果，方便API调用
        return json(['code'=>1, 'info'=>lang('重新计算收益成功'), 'data'=>$result]);
    }

    /**
     * @地址      fix_missing_income
     * @说明      检查并补发缺失的理财收益
     * @说明      域名为  http://域名/index/crontab/fix_missing_income?start_date=2025-05-19&end_date=2025-05-22
     * @参数      start_date 开始检查日期（格式：Y-m-d）
     * @参数      end_date 结束检查日期（格式：Y-m-d）
     */
    public function fix_missing_income()
    {
        // 获取参数
        $start_date = input('get.start_date', date('Y-m-d', strtotime('-7 days')));
        $end_date = input('get.end_date', date('Y-m-d'));
        
        // 初始化结果数组
        $result = [
            'success' => true,
            'message' => '检查并补发缺失收益',
            'start_date' => $start_date,
            'end_date' => $end_date,
            'processed_users' => 0,
            'missing_days' => 0,
            'total_補发_income' => 0,
            'details' => []
        ];

        // 创建日志文件
        $log_file = app()->getRuntimePath() . 'log/fix_missing_income.log';
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - 开始检查缺失收益：{$start_date} 到 {$end_date}\n", FILE_APPEND);

        // 转换日期为时间戳
        $start_timestamp = strtotime($start_date . ' 00:00:00');
        $end_timestamp = strtotime($end_date . ' 23:59:59');

        if ($start_timestamp >= $end_timestamp) {
            $result['success'] = false;
            $result['message'] = '开始日期必须小于结束日期';
            echo json_encode($result);
            return;
        }

        // 生成需要检查的日期列表
        $check_dates = [];
        $current = $start_timestamp;
        while ($current <= $end_timestamp) {
            $check_dates[] = [
                'date' => date('Y-m-d', $current),
                'start' => $current,
                'end' => $current + 86399 // 当天23:59:59
            ];
            $current += 86400; // 增加一天
        }

        // 获取在检查期间内有投资记录的所有用户
        $users_with_investments = Db::name('xy_lixibao')
            ->where('type', 1) // 转入类型
            ->where('is_qu', 0) // 未取出
            ->where('addtime', '<=', $end_timestamp) // 投资时间在检查期间之前或期间内
            ->where('endtime', '>=', $start_timestamp) // 到期时间在检查期间之后或期间内
            ->field('uid')
            ->group('uid')
            ->select();

        file_put_contents($log_file, date('Y-m-d H:i:s') . " - 找到 " . count($users_with_investments) . " 个需要检查的用户\n", FILE_APPEND);

        foreach ($users_with_investments as $user_item) {
            $uid = $user_item['uid'];
            $username = Db::name('xy_users')->where('id', $uid)->value('username');
            
            $user_missing_days = 0;
            $user_total_income = 0;
            $user_details = [];

            foreach ($check_dates as $date_info) {
                $check_date = $date_info['date'];
                $day_start = $date_info['start'];
                $day_end = $date_info['end'];

                // 检查该用户在这一天是否已经有收益记录
                $existing_income = Db::name('xy_balance_log')
                    ->where('uid', $uid)
                    ->where('type', 23) // 收益类型
                    ->where('status', 1)
                    ->where('addtime', 'between', [$day_start, $day_end])
                    ->find();

                if ($existing_income) {
                    // 已有收益记录，跳过
                    continue;
                }

                // 获取该用户在这一天的有效投资记录
                $user_investments = Db::name('xy_lixibao')
                    ->where('uid', $uid)
                    ->where('type', 1) // 转入类型
                    ->where('is_qu', 0) // 未取出
                    ->where('addtime', '<=', $day_end) // 投资时间在这一天之前或当天
                    ->where('endtime', '>', $day_start) // 到期时间在这一天之后
                    ->select();

                if (empty($user_investments)) {
                    // 该用户在这一天没有有效投资，跳过
                    continue;
                }

                // 按产品分组计算收益
                $product_amounts = [];
                $product_rates = [];
                $product_names = [];

                foreach ($user_investments as $investment) {
                    $product_id = $investment['sid'];
                    
                    if (!isset($product_amounts[$product_id])) {
                        $product_amounts[$product_id] = 0;
                        
                        // 获取产品信息
                        $product_info = Db::name('xy_lixibao_list')
                            ->where('id', $product_id)
                            ->field('name, bili')
                            ->find();
                            
                        if ($product_info) {
                            $product_names[$product_id] = $product_info['name'];
                            $product_rates[$product_id] = $product_info['bili'];
                        } else {
                            $product_names[$product_id] = "产品#$product_id";
                            $product_rates[$product_id] = 0.05; // 默认5%
                        }
                    }
                    
                    $product_amounts[$product_id] += $investment['num'];
                }

                // 计算这一天的总收益
                $day_total_income = 0;
                $day_details = [];

                foreach ($product_amounts as $product_id => $amount) {
                    if ($amount <= 0) continue;
                    
                    $rate = $product_rates[$product_id];
                    $product_income = $amount * $rate;
                    $day_total_income += $product_income;
                    
                    $day_details[] = [
                        'product_id' => $product_id,
                        'product_name' => $product_names[$product_id],
                        'amount' => $amount,
                        'rate' => $rate,
                        'income' => $product_income
                    ];
                }

                if ($day_total_income > 0) {
                    // 需要补发收益
                    $user_missing_days++;
                    $user_total_income += $day_total_income;
                    
                    // 使用当天的时间戳（中午12点）作为补发时间
                    $bufa_time = $day_start + 43200; // 12:00:00

                    try {
                        // 更新用户余额
                        Db::name('xy_users')->where('id', $uid)->setInc('balance', $day_total_income);
                        
                        // 为每个产品添加收益记录到xy_lixibao表
                        foreach ($day_details as $detail) {
                            Db::name('xy_lixibao')->insert([
                                'uid'         => $uid,
                                'num'         => $detail['income'],
                                'addtime'     => $bufa_time,
                                'type'        => 3,
                                'status'      => 1,
                                'yuji_num'    => $detail['income'],
                                'real_num'    => $detail['income'],
                                'is_sy'       => 1,
                                'sid'         => $detail['product_id'],
                                'shouxu'      => 0,
                                'bili'        => $detail['rate'],
                                'day'         => 1,
                                'update_time' => $bufa_time,
                            ]);
                        }
                        
                        // 添加余额变动记录
                        $oid = $this->getSn('FIX');
                        Db::name('xy_balance_log')->insert([
                            'uid'       => $uid,
                            'oid'       => $oid,
                            'num'       => $day_total_income,
                            'type'      => 23,
                            'status'    => 1,
                            'addtime'   => $bufa_time
                        ]);

                        $user_details[] = [
                            'date' => $check_date,
                            'income' => $day_total_income,
                            'product_details' => $day_details
                        ];

                        file_put_contents($log_file, date('Y-m-d H:i:s') . " - 补发收益: 用户 $username (ID: $uid), 日期: $check_date, 金额: $day_total_income\n", FILE_APPEND);

                    } catch (\Exception $e) {
                        $error_msg = "补发收益失败: 用户 $username (ID: $uid), 日期: $check_date, 错误: " . $e->getMessage();
                        file_put_contents($log_file, date('Y-m-d H:i:s') . " - $error_msg\n", FILE_APPEND);
                    }
                }
            }

            if ($user_missing_days > 0) {
                $result['processed_users']++;
                $result['missing_days'] += $user_missing_days;
                $result['total_補发_income'] += $user_total_income;
                
                $result['details'][] = [
                    'uid' => $uid,
                    'username' => $username,
                    'missing_days' => $user_missing_days,
                    'total_income' => $user_total_income,
                    'daily_details' => $user_details
                ];
            }
        }

        file_put_contents($log_file, date('Y-m-d H:i:s') . " - 补发收益完成：处理 {$result['processed_users']} 个用户，补发 {$result['missing_days']} 天收益，总金额 {$result['total_補发_income']}\n", FILE_APPEND);

        // 返回HTML格式的结果
        echo '<html><head><title>补发缺失收益结果</title>';
        echo '<meta charset="utf-8">';
        echo '<style>body{font-family:Arial,sans-serif;margin:20px;line-height:1.6;}';
        echo '.success{color:green;} .error{color:red;} table{border-collapse:collapse;width:100%;}';
        echo 'th,td{border:1px solid #ddd;padding:8px;text-align:left;}';
        echo 'th{background-color:#f2f2f2;} tr:nth-child(even){background-color:#f9f9f9;}</style></head>';
        echo '<body><h1>补发缺失收益结果</h1>';
        echo '<p class="'.($result['success'] ? 'success' : 'error').'">'.$result['message'].'</p>';
        echo '<p>检查期间: '.$result['start_date'].' 到 '.$result['end_date'].'</p>';
        echo '<p>处理用户数: '.$result['processed_users'].'</p>';
        echo '<p>补发天数: '.$result['missing_days'].'</p>';
        echo '<p>补发总金额: '.$result['total_補发_income'].'</p>';

        if (!empty($result['details'])) {
            echo '<h2>补发明细</h2><table>';
            echo '<tr><th>用户ID</th><th>用户名</th><th>缺失天数</th><th>补发总金额</th></tr>';
            foreach ($result['details'] as $detail) {
                echo '<tr>';
                echo '<td>'.$detail['uid'].'</td>';
                echo '<td>'.$detail['username'].'</td>';
                echo '<td>'.$detail['missing_days'].'</td>';
                echo '<td>'.$detail['total_income'].'</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            // 显示详细的每日补发记录
            foreach ($result['details'] as $user) {
                echo '<h3>用户: '.$user['username'].' (ID: '.$user['uid'].')</h3>';
                echo '<table>';
                echo '<tr><th>日期</th><th>补发金额</th><th>产品详情</th></tr>';
                
                foreach ($user['daily_details'] as $daily) {
                    echo '<tr>';
                    echo '<td>'.$daily['date'].'</td>';
                    echo '<td>'.$daily['income'].'</td>';
                    echo '<td>';
                    foreach ($daily['product_details'] as $product) {
                        echo $product['product_name'].' (金额:'.$product['amount'].', 收益:'.$product['income'].') ';
                    }
                    echo '</td>';
                    echo '</tr>';
                }
                
                echo '</table>';
            }
        }

        echo '</body></html>';

        return json(['code'=>1, 'info'=>'补发收益完成', 'data'=>$result]);
    }
}