<?php

namespace app\index\controller;

use think\Controller;
use think\Request;
use think\Db;

class My extends Base
{
    protected $msg = ['__token__'  => '请不要重复提交！'];
    /**
     * 首页
     */
    public function index()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            $this->redirect('User/login');
        }

        // 安全修复：确保只获取当前登录用户的信息
        $this->info = db('xy_users')->field('username,tel,level,id,headpic,balance,freeze_balance,lixibao_balance,invite_code,show_td,credit_score,member_level')->find($uid);

        if(!$this->info) {
            // 如果用户信息不存在，清除session并重定向到登录页
            session('user_id', null);
            $this->redirect('User/login');
        }

        // 确保会员等级使用语言键
        if ($this->info && isset($this->info['member_level'])) {
            $this->info['member_level_display'] = lang($this->info['member_level']);
        }

        $this->lv3 = [0,config('vip_3_num')];
        $this->lv2 = [0,config('vip_2_num')];

        // 安全修复：使用安全的用户ID
        $this->sell_y_num = db('xy_convey')->where('status',1)->where('uid',$uid)->sum('commission');

        $level = $this->info['level'];
        !$level ? $level = 0 : $level;

        // 获取等级名称，如果没找到则使用默认格式
        $level_name = db('xy_level')->where('level', $level)->value('name');
        if (!$level_name) {
            // 如果没找到对应的等级名称，尝试通过id查找
            $level_name = db('xy_level')->where('id', $level)->value('name');
        }
        if (!$level_name) {
            // 如果还是没找到，使用默认格式
            $level_name = 'VIP' . $level;
        }
        $this->level_name = $level_name;

        // 确保info中也有正确的VIP等级显示
        $this->info['vip_level_display'] = $level_name;

        $this->info['lixibao_balance'] = number_format($this->info['lixibao_balance'],2);

        $lxb_bili = config('lxb_bili');
        $this->rililv = (is_numeric($lxb_bili) ? $lxb_bili : 0) * 100 . '%';

        // 安全修复：使用安全的用户ID
        $this->lxb_shouyi = db('xy_lixibao')->where('status',1)->where('uid',$uid)->sum('num');

        $cs = db('xy_cs')->where('status',1)->find();
        $this->assign('cs', $cs);

        // 处理语言设置，确保阿拉伯语正确显示
        $lang = cookie('lang') ? cookie('lang') : 'en-us';

        // 如果是阿拉伯语，确保设置正确
        if($lang == 'arabic') {
            Cookie('isArabic', 'true', time()+3600*24*30); // 30天有效期
            Cookie('direction', 'rtl', time()+3600*24*30); // 为阿拉伯语设置从右到左方向
        }

        // 记录错误信息以便调试
        \think\facade\Log::write('当前语言: ' . $lang, 'info');

        $this->assign('lang', $lang);
        return $this->fetch();
    }
    public function index2()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            $this->redirect('User/login');
        }

        // 安全修复：确保只获取当前登录用户的信息
        $this->info = db('xy_users')->field('username,tel,level,id,headpic,balance,freeze_balance,lixibao_balance,invite_code,show_td')->find($uid);

        if(!$this->info) {
            // 如果用户信息不存在，清除session并重定向到登录页
            session('user_id', null);
            $this->redirect('User/login');
        }

        $this->lv3 = [0,config('vip_3_num')];
        $this->lv2 = [0,config('vip_2_num')];

        // 安全修复：使用安全的用户ID
        $this->sell_y_num = db('xy_convey')->where('status',1)->where('uid',$uid)->sum('commission');

        $level = $this->info['level'];
        !$level ? $level = 0 :'';

        $this->level_name = db('xy_level')->where('level',$level)->value('name');

        $this->info['lixibao_balance'] = number_format($this->info['lixibao_balance'],2);

        $lxb_bili = config('lxb_bili');
        $this->rililv = (is_numeric($lxb_bili) ? $lxb_bili : 0) * 100 . '%';

        // 安全修复：使用安全的用户ID
        $this->lxb_shouyi = db('xy_lixibao')->where('status',1)->where('uid',$uid)->sum('num');

        $cs = db('xy_cs')->where('status',1)->find();
        $this->assign('cs', $cs);
        $lang=cookie('think_var') ? cookie('think_var'):'zh-cn';
        $this->assign('lang', $lang);
        return $this->fetch();
    }

    /**
     * 获取收货地址
     */
    public function get_address()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        $data = db('xy_member_address')->where('uid',$uid)->select();
        if($data)
            return json(['code'=>0,'info'=>lang('获取成功'),'data'=>$data]);
        else
            return json(['code'=>1,'info'=>lang('暂未数据')]);
    }

    public function reload()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return $this->error(lang('请先登录'));
        }

        $user = db('xy_users')->find($uid);
        if(!$user) {
            return $this->error(lang('用户不存在'));
        }

        $n = ($uid%20);

        $dir = './upload/qrcode/user/'.$n . '/' . $uid . '.png';
        if(file_exists($dir)) {
            unlink($dir);
        }

        $res = model('admin/Users')->create_qrcode($user['invite_code'],$uid);
        if(0 && $res['code']!==0){
            return $this->error(lang('失败'));
        }
        return $this->success(lang('成功'));
    }


    /**
     * 添加收货地址
     */
    public function add_address()
    {
        if(request()->isPost()){
            // 安全修复：使用session而不是cookie获取用户ID
            $uid = $this->getCurrentUserId();
            if(!$uid) {
                return json(['code'=>1,'info'=>lang('请先登录')]);
            }

            $name = input('post.name/s','');
            $tel = input('post.tel/s','');
            $address = input('post.address/s','');
            $area = input('post.area/s','');
            $token = input("token");//获取提交过来的令牌
            $data = ['__token__' => $token];
            $validate   = \Validate::make($this->rule,$this->msg);
            if (!$validate->check($data)) {
                return json(['code'=>1,'info'=>$validate->getError()]);
            }
            $data = [
                'uid'       => $uid,
                'name'      => $name,
                'tel'       => $tel,
                'area'      => $area,
                'address'   => $address,
                'addtime'   => time()
            ];
            $tmp = db('xy_member_address')->where('uid',$uid)->find();
            if(!$tmp) $data['is_default']=1;
            $res = db('xy_member_address')->insert($data);
            if($res)
                return json(['code'=>0,'info'=>lang('操作成功')]);
            else
                return json(['code'=>1,'info'=>lang('操作失败')]);
        }
        return json(['code'=>1,'info'=>lang('错误请求')]);
    }

    /**
     * 编辑收货地址
     */
    public function edit_address()
    {
        if(request()->isPost()){
            // 安全修复：使用session而不是cookie获取用户ID
            $uid = $this->getCurrentUserId();
            if(!$uid) {
                return json(['code'=>1,'info'=>lang('请先登录')]);
            }

            $name       = input('post.shoujianren/s','');
            $tel        = input('post.shouhuohaoma/s','');
            $address    = input('post.address/s','');
            $area       = input('post.area/s','');

            $ainfo = db('xy_member_address')->where('uid',$uid)->find();
            if ($ainfo) {
                $res = db('xy_member_address')
                    ->where('id',$ainfo['id'])
                    ->update([
                        'uid'       => $uid,
                        'name'      => $name,
                        'tel'       => $tel,
                        'area'      => $area,
                        'address'   => $address,
                        //'addtime'   => time()
                    ]);
            }else{
                $res = db('xy_member_address')
                    ->insert([
                        'uid'       => $uid,
                        'name'      => $name,
                        'tel'       => $tel,
                        'area'      => $area,
                        'address'   => $address,
                        'addtime'   => time()
                    ]);
            }

            if($res)
                return json(['code'=>0,'info'=>lang('操作成功')]);
            else
                return json(['code'=>1,'info'=>lang('操作失败')]);
        }elseif (request()->isGet()) {
            // 安全修复：使用session而不是cookie获取用户ID
            $uid = $this->getCurrentUserId();
            if(!$uid) {
                $this->redirect('User/login');
            }

            $this->info = db('xy_member_address')->where('uid',$uid)->find();
            $this->tel_bak = !empty($this->info['tel']) ? substr($this->info['tel'], 0, 3) . '****' . substr($this->info['tel'], 7, 10) : '';

            return $this->fetch();
        }
    }

    public function team()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            $this->redirect('User/login');
        }

        //$this->info = db('xy_member_address')->where('uid',$id)->find();
        $uids = model('admin/Users')->child_user($uid,5);
        array_push($uids,$uid);
        $uids ? $where[] = ['uid','in',$uids] : $where[] = ['uid','in',[-1]];

        $datum['sl'] = count($uids);
        $datum['yj'] = db('xy_convey')->where('status',1)->where($where)->sum('num');
        $datum['cz'] = db('xy_recharge')->where('status',2)->where($where)->sum('num');
        $datum['tx'] = db('xy_deposit')->where('status',2)->where($where)->sum('num');


        //
        $uids1 = model('admin/Users')->child_user($uid,1);
        $uids1 ? $where1[] = ['sid','in',$uids1] : $where1[] = ['sid','in',[-1]];
        $datum['log1'] = db('xy_balance_log')->where('uid',$uid)->where($where1)->where('f_lv',1)->sum('num');

        $uids2 = model('admin/Users')->child_user($uid,2);
        $uids2 ? $where2[] = ['sid','in',$uids2] : $where2[] = ['sid','in',[-1]];
        $datum['log2'] = db('xy_balance_log')->where('uid',$uid)->where($where2)->where('f_lv',2)->sum('num');

        $uids3 = model('admin/Users')->child_user($uid,3);
        $uids3 ? $where3[] = ['sid','in',$uids3] : $where3[] = ['sid','in',[-1]];
        $datum['log3'] = db('xy_balance_log')->where('uid',$uid)->where($where3)->where('f_lv',3)->sum('num');


        $uids5 = model('admin/Users')->child_user($uid,5);
        $uids5 ? $where5[] = ['sid','in',$uids5] : $where5[] = ['sid','in',[-1]];
        $datum['yj2'] = db('xy_convey')->where('status',1)->where($where)->sum('commission');
        $datum['yj3'] = db('xy_balance_log')->where('uid',$uid)->where($where5)->where('type',6)->sum('num');;


        $this->info = $datum;

        return $this->fetch();
    }

    public function caiwu()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            $this->redirect('User/login');
        }

        $day      = input('get.day/s','');
        $where=[];
        if ($day) {
            $start = strtotime("-$day days");
            $where[] = ['addtime','between',[$start,time()]];
        }

        $start      = input('get.start/s','');
        $end        = input('get.end/s','');
        if ($start || $end) {
            $start ? $start = strtotime($start) : $start = strtotime('2020-01-01');
            $end ? $end = strtotime($end.' 23:59:59') : $end = time();
            $where[] = ['addtime','between',[$start,$end]];
        }

        $this->start = $start ? date('Y-m-d',$start) : '';
        $this->end = $end ? date('Y-m-d',$end) : '';

        $this->type = $type = input('get.type/d',0);

        if ($type == 1) {
            // 提现记录
            $where[] = ['type', '=', 7];
        } elseif ($type == 2) {
            // 充值记录
            $where[] = ['type', 'in', [1, 8, 87]];
        }

        // 获取数据 - 使用安全的用户ID
        $list = db('xy_balance_log')
            ->where('uid', $uid)
            ->where($where)
            ->order('id desc')
            ->paginate(20);

        // 为每条记录添加状态信息
        $processed_list = [];
        foreach ($list as $item) {
            $processed_item = $item;

            // 初始化默认状态
            $processed_item['status_text'] = '';
            $processed_item['status_color'] = '#777777';

            // 添加状态文本和颜色
            if ($item['type'] == 7) {
                // 提现记录，从xy_deposit表获取状态，使用正确的关联字段
                $deposit_info = db('xy_deposit')->where('id', $item['oid'])->find();
                if ($deposit_info) {
                    $processed_item['status_text'] = $this->getDepositStatusText($deposit_info['status']);
                    $processed_item['status_color'] = $this->getDepositStatusColor($deposit_info['status']);
                }
            } elseif (in_array($item['type'], [1, 8, 87])) {
                // 充值相关记录
                $processed_item['status_text'] = $this->getRechargeStatusText($item['type'], $item['status'] ?? 1);
                $processed_item['status_color'] = $this->getRechargeStatusColor($item['type'], $item['status'] ?? 1);
            }

            $processed_list[] = $processed_item;
        }

        $this->list = $processed_list;
        $this->pagehtml = $list->render();

        return $this->fetch();
    }

    /**
     * 获取提现状态文本
     */
    private function getDepositStatusText($status)
    {
        switch ($status) {
            case 0:
            case 1:
                return lang('Withdrawal_pending');
            case 2:
                return lang('Withdrawal_success');
            case 3:
                return lang('Withdrawal_failed');
            case 4:
                return lang('Withdrawal_processing');
            case 5:
                return lang('Withdrawal_completed');
            default:
                return lang('Withdrawal_unknown');
        }
    }

    /**
     * 获取提现状态颜色
     */
    private function getDepositStatusColor($status)
    {
        switch ($status) {
            case 0:
            case 1:
            case 4:
                return '#ff9a2c'; // 橙色 - 待处理
            case 2:
            case 5:
                return '#0cea5b'; // 绿色 - 成功
            case 3:
                return '#ff7070'; // 红色 - 失败
            default:
                return '#777777'; // 灰色 - 未知
        }
    }

    /**
     * 获取充值状态文本
     */
    private function getRechargeStatusText($type, $status)
    {
        if ($type == 87) {
            return lang('Bonus_reward'); // 赠送彩金
        } elseif ($type == 8) {
            return lang('Manual_recharge'); // 手动充值
        } else {
            // 普通充值
            if ($status == 1) {
                return lang('Recharge_success');
            } else {
                return lang('Recharge_failed');
            }
        }
    }

    /**
     * 获取充值状态颜色
     */
    private function getRechargeStatusColor($type, $status)
    {
        if ($type == 87) {
            return '#d814c9'; // 紫色 - 赠送彩金
        } elseif ($type == 8) {
            return '#0cea5b'; // 绿色 - 手动充值
        } else {
            if ($status == 1) {
                return '#0cea5b'; // 绿色 - 成功
            } else {
                return '#ff7070'; // 红色 - 失败
            }
        }
    }

    public function headimg()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        if(request()->isPost()) {
            $username = input('post.pic/s', '');
            $res = db('xy_users')->where('id',$uid)->update(['headpic'=>$username]);
            if($res!==false){
                return json(['code'=>0,'info'=>lang('操作成功')]);
            }else{
                return json(['code'=>1,'info'=>lang('操作失败')]);
            }
        }
        $this->info = db('xy_users')->find($uid);
        return $this->fetch();
    }

    public function bind_bank()
    {
        $id = input('post.id/d',0);
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        $info = db('xy_bankinfo')->where('uid',$uid)->find();
        $uinfo = db('xy_users')->find($uid);

        if(request()->isPost()){
            $username = input('post.username/s','');
            $bankname = input('post.bankname/s','');
            $cardnum = input('post.card_bak/s','');
            $site  = input('post.zhihang/s','');
            $qq  = input('post.qq/s','');
            $address  = input('post.address/s','');
            $ifsc  = input('post.ifsc/s','');

            //同一姓名和卡号只绑定一次
            $res = db('xy_bankinfo')->where('username',$username)->where('cardnum',$cardnum)->find();
            if ($res && $res['uid'] != $uid) {
                return json(['code'=>1,'info'=>lang('该姓名和银行卡已绑定其他账号!')]);
            }
$o_pwd=input('post.old_pwd/s','');
        // $data['pwd2'] = sha1($pwd2.$salt2.config('pwd_str'));
        if($uinfo['pwd2']!=sha1($o_pwd.$uinfo['salt2'].config('pwd_str'))) return json(['code'=>1,'info'=>lang('密码错误')]);


            $data =array(
                'username' =>$username,
                'bankname' =>$bankname,
                'cardnum' =>$cardnum,
                'site' =>$site,
                'address' =>$address,
                'qq' =>$qq,
                'tel' =>$uinfo['tel'],
                'status' =>1,
                'ifsc' =>$ifsc,
                'uid' =>$uid
            );

            if ($info) {
                $res = db('xy_bankinfo')->where('uid',$uid)->update($data);
            }else{
                $res = db('xy_bankinfo')->insert($data);
            }

            if($res){
                //  $salt = mt_rand(0,99999);
                // $data22 = [
                //     'pwd2'       => sha1(input('post.old_pwd/s','').$salt.config('pwd_str')),
                //     'salt2'      => $salt,
                // ];

                // Db::table('xy_users')->where('id',$uid)->update($data22);
                return json(['code'=>0,'info'=>lang('操作成功')]);
            }else{
                return json(['code'=>1,'info'=>lang('操作失败')]);
            }
        }

        $fileurl = APP_PATH . "../config/bank.txt";
        $text = file_get_contents($fileurl); // 写入配置文件
        $text = explode("\r\n",$text);
        $bank=[];
        foreach($text as $k=>$v){
            $str=explode("|",$v);
            $bank[$k]["bankname"]=trim($str[1]);
            $bank[$k]["bankcode"]=trim($str[0]);
        }
        $this->assign('bank',$bank);

        $this->assign('info', $info);
        $this->assign('uinfo', $uinfo);
        $this->assign('cardnum_bak', !empty($info['cardnum']) ? $info['cardnum'] : '');
        $this->tel_bak = !empty($info['tel']) ? substr($info['tel'], 0, 3) . '****' . substr($info['tel'], 7, 10) : '';
        return $this->fetch();
    }

    public function bind_zhifubao()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        $info = db('xy_bankinfo')->where('uid',$uid)->find();

        if(request()->isPost()){
            $username = input('post.username/s','');
            $zhifunum = input('post.zhifunum/s','');

            //同一姓名和卡号只绑定一次
            $res = db('xy_bankinfo')->where('username',$username)->where('zhifunum',$zhifunum)->find();
            if ($res && $res['uid'] != $uid) {
                return json(['code'=>1,'info'=>lang('该姓名和支付宝已绑定其他账号!')]);
            }

            $data =array(
                'username' =>$username,
                'zhifunum' =>$zhifunum,
                'status' =>1,
                'uid' =>$uid
            );

            if ($info) {
                $res = db('xy_bankinfo')->where('uid',$uid)->update($data);
            }else{
                $res = db('xy_bankinfo')->insert($data);
            }

            if($res){
                return json(['code'=>0,'info'=>lang('操作成功')]);
            }else{
                return json(['code'=>1,'info'=>lang('操作失败')]);
            }
        }


        $this->info = $info;
        $this->zhifunum_bak = !empty($this->info['zhifunum']) ? substr($this->info['zhifunum'], 0, 3) . '****' . substr($this->info['zhifunum'], 7, 10) : '';
        $uinfo = db('xy_users')->find($uid);
        $this->uinfo = $uinfo;
        return $this->fetch();
    }




    /**
     * 设置默认收货地址
     */
    public function set_address()
    {
        if(request()->isPost()){
            // 安全修复：使用session而不是cookie获取用户ID
            $uid = $this->getCurrentUserId();
            if(!$uid) {
                return json(['code'=>1,'info'=>lang('请先登录')]);
            }

            $id = input('post.id/d',0);
            Db::startTrans();
            $res = db('xy_member_address')->where('uid',$uid)->update(['is_default'=>0]);
            $res1 = db('xy_member_address')->where('id',$id)->update(['is_default'=>1]);
            if($res && $res1){
                Db::commit();
                return json(['code'=>0,'info'=>lang('操作成功')]);
            }else{
                Db::rollback();
                return json(['code'=>1,'info'=>lang('操作失败')]);
            }
        }
        return json(['code'=>1,'info'=>lang('错误请求')]);
    }

    /**
     * 删除收货地址
     */
    public function del_address()
    {
        if(request()->isPost()){
            $id = input('post.id/d',0);
            $info = db('xy_member_address')->find($id);
            if($info['is_default']==1){
                return json(['code'=>1,'info'=>lang('不能删除默认收货地址')]);
            }
            $res = db('xy_member_address')->where('id',$id)->delete();
            if($res)
                return json(['code'=>0,'info'=>lang('操作成功')]);
            else
                return json(['code'=>1,'info'=>lang('操作失败')]);
        }
        return json(['code'=>1,'info'=>lang('错误请求')]);
    }

    public function get_bot(){
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        $data = model('admin/Users')->get_botuser($uid,3);
        halt($data);
    }

    public function yue(){
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            $this->redirect('User/login');
        }

        $this->info = db('xy_users')->find($uid);
        return $this->fetch();
    }

    /**
     * 绑定我的钱包
     */
    public function bind_wallet()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        $info = db('xy_bankinfo')->where('uid',$uid)->find();
        $uinfo = db('xy_users')->find($uid);

        if(request()->isPost()){
            $username = input('post.username/s','');
            $cardnum = input('post.card_bak/s','');
            $wallet_type = input('post.wallet_type/s','');
            $o_pwd = input('post.old_pwd/s','');

            if(!$username || !$cardnum || !$wallet_type) {
                return json(['code'=>1,'info'=>lang('必填项不能为空')]);
            }

            //同一姓名和钱包地址只绑定一次
            $res = db('xy_bankinfo')->where('username',$username)->where('cardnum',$cardnum)->find();
            if ($res && $res['uid'] != $uid) {
                return json(['code'=>1,'info'=>lang('该姓名和钱包地址已绑定其他账号')]);
            }

            // 验证资金密码
            if($uinfo['pwd2']!=sha1($o_pwd.$uinfo['salt2'].config('pwd_str'))) {
                return json(['code'=>1,'info'=>lang('密码错误')]);
            }

            // 开启事务，确保两个表同时更新
            db()->startTrans();
            try {
                // 构建 xy_bankinfo 表的保存数据
                $bankinfo_data = [
                    'username' => $username,
                    'cardnum' => $cardnum,
                    'wallet_type' => $wallet_type,
                    'tel' => $uinfo['tel'],
                    'status' => 1,
                    'uid' => $uid
                ];

                // 更新或插入 xy_bankinfo 表
                if ($info) {
                    $res1 = db('xy_bankinfo')->where('uid',$uid)->update($bankinfo_data);
                } else {
                    $res1 = db('xy_bankinfo')->insert($bankinfo_data);
                }

                // 构建 xy_user_wallet 表的保存数据
                $wallet_data = [
                    'uid' => $uid,
                    'wallet_address' => $cardnum,
                    'wallet_type' => $wallet_type,
                    'status' => 1
                ];

                // 检查 xy_user_wallet 表中是否已存在该用户的钱包数据
                $wallet_info = db('xy_user_wallet')->where('uid',$uid)->find();
                if ($wallet_info) {
                    // 更新现有记录
                    $res2 = db('xy_user_wallet')->where('uid',$uid)->update($wallet_data);
                } else {
                    // 插入新记录
                    $wallet_data['addtime'] = time();
                    $res2 = db('xy_user_wallet')->insert($wallet_data);
                }

                // 检查两个操作是否都成功
                if($res1 && $res2){
                    db()->commit();
                    return json(['code'=>0,'info'=>lang('操作成功')]);
                } else {
                    db()->rollback();
                    return json(['code'=>1,'info'=>lang('操作失败')]);
                }
            } catch (\Exception $e) {
                db()->rollback();
                return json(['code'=>1,'info'=>lang('操作失败') . ': ' . $e->getMessage()]);
            }
        }

        $this->assign('info', $info);
        $this->assign('uinfo', $uinfo);
        $this->assign('cardnum_bak', !empty($info['cardnum']) ? $info['cardnum'] : '');
        return $this->fetch();
    }

    public function edit_username(){
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        if(request()->isPost()) {
            $username = input('post.username/s', '');
            $res = db('xy_users')->where('id',$uid)->update(['username'=>$username]);
            if($res!==false){
                return json(['code'=>0,'info'=>lang('操作成功')]);
            }else{
                return json(['code'=>1,'info'=>lang('操作失败')]);
            }
        }
        $this->info = db('xy_users')->find($uid);
        return $this->fetch();
    }



    /**
     * 用户账号充值
     */
    public function user_recharge()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        $tel = input('post.tel/s','');
        $num = input('post.num/d',0);
        $pic = input('post.pic/s','');
        $real_name = input('post.real_name/s','');

        if(!$pic || !$num ) return json(['code'=>1,'info'=>lang('参数错误')]);
        //if(!is_mobile($tel)) return json(['code'=>1,'info'=>'手机号码格式不正确']);

        if (is_image_base64($pic))
            $pic = '/' . $this->upload_base64('xy',$pic);  //调用图片上传的方法
        else
            return json(['code'=>1,'info'=>lang('图片格式错误')]);
        $id = getSn('SY');
        $res = db('xy_recharge')
            ->insert([
                'id'        => $id,
                'uid'       => $uid,
                'tel'       => $tel,
                'real_name' => $real_name,
                'pic'       => $pic,
                'num'       => $num,
                'addtime'   => time()
            ]);
        if($res)
            return json(['code'=>0,'info'=>lang('提交成功')]);
        else
            return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
    }

    //邀请界面
    public function invite()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            $this->redirect('User/login');
        }

        $this->assign('pic','/upload/qrcode/user/'.($uid%20).'/'.$uid.'.png');

        $user = db('xy_users')->find($uid);

        $url = SITE_URL . url('@index/user/register/invite_code/'.$user['invite_code']);
        $this->assign('url',$url);
        $this->assign('invite_code',$user['invite_code']);
        return $this->fetch();
    }

    //我的资料
    public function do_my_info()
    {
        if(request()->isPost()){
            // 安全修复：使用session而不是cookie获取用户ID
            $uid = $this->getCurrentUserId();
            if(!$uid) {
                return json(['code'=>1,'info'=>lang('请先登录')]);
            }

            $headpic    = input('post.headpic/s','');
            $wx_ewm    = input('post.wx_ewm/s','');
            $zfb_ewm    = input('post.zfb_ewm/s','');
            $nickname   = input('post.nickname/s','');
            $sign       = input('post.sign/s','');
            $data = [
                'nickname'  => $nickname,
                'signiture' => $sign
            ];

            if($headpic){
                if (is_image_base64($headpic))
                    $headpic = '/' . $this->upload_base64('xy',$headpic);  //调用图片上传的方法
                else
                    return json(['code'=>1,'info'=>lang('图片格式错误')]);
                $data['headpic'] = $headpic;
            }

            if($wx_ewm){
                if (is_image_base64($wx_ewm))
                    $wx_ewm = '/' . $this->upload_base64('xy',$wx_ewm);  //调用图片上传的方法
                else
                    return json(['code'=>1,'info'=>lang('图片格式错误')]);
                $data['wx_ewm'] = $wx_ewm;
            }

            if($zfb_ewm){
                if (is_image_base64($zfb_ewm))
                    $zfb_ewm = '/' . $this->upload_base64('xy',$zfb_ewm);  //调用图片上传的方法
                else
                    return json(['code'=>1,'info'=>lang('图片格式错误')]);
                $data['zfb_ewm'] = $zfb_ewm;
            }

            $res = db('xy_users')->where('id',$uid)->update($data);
            if($res!==false){
                if($headpic) session('avatar',$headpic);
                return json(['code'=>0,'info'=>lang('操作成功')]);
            }else{
                return json(['code'=>1,'info'=>lang('操作失败')]);
            }
        }elseif(request()->isGet()){
            // 安全修复：使用session而不是cookie获取用户ID
            $uid = $this->getCurrentUserId();
            if(!$uid) {
                return json(['code'=>1,'info'=>lang('请先登录')]);
            }

            $info = db('xy_users')->field('username,headpic,nickname,signiture sign,wx_ewm,zfb_ewm')->find($uid);
            return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$info]);
        }
    }

    // 消息
    public function activity()
    {
        $where[] = ['title','like','%' . '活动' . '%'];

        $this->msg = db('xy_index_msg')->where($where)->select();
        return $this->fetch();
    }



    // 消息
    public function msg()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            $this->redirect('User/login');
        }

        $this->info = db('xy_message')->alias('m')
            // ->leftJoin('xy_users u','u.id=m.sid')
            ->leftJoin('xy_reads r','r.mid=m.id and r.uid='.$uid)
            ->field('m.*,r.id rid')
            ->where('m.uid','in',[0,$uid])
            ->order('addtime desc')
            ->select();
        // dump($this->info);exit;
        $this->msg = db('xy_index_msg')->where('status',1)->select();
        return $this->fetch();
    }

    // 消息
    public function detail()
    {
        $id = input('get.id/d',0);
        $this->msg = db('xy_index_msg')->where('id',$id)->find();

        $data =  db('xy_index_msg')->where('id',$id)->find();


        $this->msg['title']=$this->msg['title_e'];
       if(cookie('lang')=="en-us"){

            $this->msg['content']=$data['e'];
        }elseif(cookie('lang')=="baxi"){

            $this->msg['content']=$data['baxi'];
        }elseif(cookie('lang')=="moxige"){

            $this->msg['content']=$data['moxige'];
        }elseif(cookie('lang')=="tuerqi"){

            $this->msg['content']=$data['tuerqi'];
        }else{
            // 如果默认语言是en-us，使用e字段
            if(config('app.default_lang') == 'en-us'){
                $this->msg['content']=$data['e'];
            } else {
                $this->msg['content']=$data[config('app.default_lang')];
            }
        }
        return $this->fetch();
    }

    //记录阅读情况
    public function reads()
    {
        if(\request()->isPost()){
            // 安全修复：使用session而不是cookie获取用户ID
            $uid = $this->getCurrentUserId();
            if(!$uid) {
                return json(['code'=>1,'info'=>lang('请先登录')]);
            }

            $id = input('post.id/d',0);
            db('xy_reads')->insert(['mid'=>$id,'uid'=>$uid,'addtime'=>time()]);
            return $this->success(lang('成功'));
        }
    }

    public function gonggao()
    {

    }

    //修改绑定手机号
    public function reset_tel()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        $pwd = input('post.pwd','');
        $verify = input('post.verify/s','');
        $tel = input('post.tel/s','');
        $userinfo = Db::table('xy_users')->field('id,pwd,salt')->find($uid);
        if($userinfo['pwd'] != sha1($pwd.$userinfo['salt'].config('pwd_str'))) return json(['code'=>1,'info'=>lang('登录密码错误')]);
        if(config('app.verify')){
            $verify_msg = Db::table('xy_verify_msg')->field('msg,addtime')->where(['tel'=>$tel,'type'=>3])->find();
            if(!$verify_msg) return json(['code'=>1,'info'=>lang('验证码不存在')]);
            if($verify != $verify_msg['msg']) return json(['code'=>1,'info'=>lang('验证码错误')]);
            if(($verify_msg['addtime'] + (config('app.zhangjun_sms.min')*60)) < time())return json(['code'=>1,'info'=>lang('验证码已失效')]);
        }
        $res = db('xy_users')->where('id',$uid)->update(['tel'=>$tel]);
        if($res!==false)
            return json(['code'=>0,'info'=>lang('操作成功')]);
        else
            return json(['code'=>1,'info'=>lang('操作失败')]);
    }

    //团队佣金列表
    public function get_team_reward()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if(!$uid) {
            return json(['code'=>1,'info'=>lang('请先登录')]);
        }

        $lv = input('post.lv/d',1);
        $num = Db::name('xy_reward_log')->where('uid',$uid)->where('addtime','between',strtotime(date('Y-m-d')) . ',' . time())->where('lv',$lv)->where('status',1)->sum('num');

        if($num) return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$num]);
        return json(['code'=>1,'info'=>lang('暂无数据')]);
    }

    public function xxxx() {
        return json([1]);
    }

    /**
     * 实名认证
     */
    public function identity_verify()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = $this->getCurrentUserId();
        if (!$uid) {
            // 重定向到登录页面而不是返回JSON
            return $this->redirect('User/login');
        }

        $info = db('xy_users')->where('id', $uid)->find();

        if (request()->isPost()) {
            $real_name = input('post.real_name/s', '');
            $id_card = input('post.id_card/s', '');

            if (empty($real_name)) {
                $this->error(lang('Please enter your real name'));
                return;
            }

            if (empty($id_card)) {
                $this->error(lang('Please enter your ID card number'));
                return;
            }

            // 身份证号码格式验证 - 支持国际格式，要求6-18位
            if (!preg_match('/^[a-zA-Z0-9]{6,18}$/', $id_card)) {
                $this->error(lang('ID card number format is incorrect'));
                return;
            }

            // 处理图片上传
            $front_pic = '';
            $back_pic = '';

            // 获取原图片路径
            $old_front_pic = input('post.old_front_pic/s', '');
            $old_back_pic = input('post.old_back_pic/s', '');

            // 确保上传目录存在
            $upload_path = './upload/id_card/';
            if (!is_dir($upload_path)) {
                @mkdir($upload_path, 0777, true);
            }

            // 检查目录权限
            if (!is_writable($upload_path)) {
                @chmod($upload_path, 0777);
                if (!is_writable($upload_path)) {
                    $this->error(lang('Upload directory is not writable, please check permissions'));
                    return;
                }
            }

            try {
                // 使用已有照片
                if (!empty($old_front_pic) && empty($_FILES['front_pic']['tmp_name'])) {
                    $front_pic = $old_front_pic;
                }

                if (!empty($old_back_pic) && empty($_FILES['back_pic']['tmp_name'])) {
                    $back_pic = $old_back_pic;
                }

                // 处理正面照片上传
                if (!empty($_FILES['front_pic']['tmp_name'])) {
                    if ($_FILES['front_pic']['error'] > 0) {
                        $error = $this->getUploadError($_FILES['front_pic']['error']);
                        $this->error(lang('ID card front photo upload failed') . ': ' . $error);
                        return;
                    }

                    // 简单验证文件类型
                    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                    if (!in_array($_FILES['front_pic']['type'], $allowed_types)) {
                        $this->error(lang('Invalid file type, please upload image file'));
                        return;
                    }

                    // 生成唯一文件名
                    $filename = md5(microtime(true) . rand(1000, 9999)) . '.jpg';
                    $filepath = $upload_path . $filename;

                    // 直接移动上传的文件
                    if (move_uploaded_file($_FILES['front_pic']['tmp_name'], $filepath)) {
                        $front_pic = '/upload/id_card/' . $filename;
                    } else {
                        $this->error(lang('ID card front photo upload failed') . ': 无法移动上传文件');
                        return;
                    }
                }

                // 处理反面照片上传
                if (!empty($_FILES['back_pic']['tmp_name'])) {
                    if ($_FILES['back_pic']['error'] > 0) {
                        $error = $this->getUploadError($_FILES['back_pic']['error']);
                        $this->error(lang('ID card back photo upload failed') . ': ' . $error);
                        return;
                    }

                    // 简单验证文件类型
                    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                    if (!in_array($_FILES['back_pic']['type'], $allowed_types)) {
                        $this->error(lang('Invalid file type, please upload image file'));
                        return;
                    }

                    // 生成唯一文件名
                    $filename = md5(microtime(true) . rand(1000, 9999)) . '.jpg';
                    $filepath = $upload_path . $filename;

                    // 直接移动上传的文件
                    if (move_uploaded_file($_FILES['back_pic']['tmp_name'], $filepath)) {
                        $back_pic = '/upload/id_card/' . $filename;
                    } else {
                        $this->error(lang('ID card back photo upload failed') . ': 无法移动上传文件');
                        return;
                    }
                }
            } catch (\Exception $e) {
                trace("ID card photo upload exception: " . $e->getMessage(), 'error');
                $this->error(lang('Photo upload error') . ': ' . $e->getMessage());
                return;
            }

            if (empty($front_pic)) {
                $this->error(lang('Please upload ID card front photo'));
                return;
            }

            if (empty($back_pic)) {
                $this->error(lang('Please upload ID card back photo'));
                return;
            }

            // 更新用户信息
            $data = [
                'real_name' => $real_name,
                'id_card_num' => $id_card,
                'top_pic' => $front_pic,
                'bot_pic' => $back_pic,
                'id_status' => 0, // 待审核状态
                'real_time' => time()
            ];

            trace("Updating user data: " . json_encode($data), 'info');
            $res = db('xy_users')->where('id', $uid)->update($data);

            if ($res !== false) {
                // 强制使用302重定向，不使用ThinkPHP的success方法
                // 设置session闪存消息，用于在目标页面显示
                session('success_message', lang('Submission successful, please wait for review'));

                // 直接执行重定向
                header('Location: ' . url('my/index'));
                exit;
            } else {
                trace("Database update failed for user ID: " . $uid, 'error');
                // 直接输出错误页面
                echo '<html><head><meta charset="utf-8"><title>'.lang('Error').'</title>';
                echo '<style>body{font-family:Arial,sans-serif;padding:20px;text-align:center;}';
                echo '.error{color:red;font-size:18px;margin:20px 0;}';
                echo '.button{display:inline-block;padding:10px 20px;background:#3a4b9c;color:#fff;text-decoration:none;border-radius:4px;}</style></head>';
                echo '<body><div class="error">'.lang('Submission failed, please try again').'</div>';
                echo '<a href="'.url('my/identity_verify').'" class="button">'.lang('Back').'</a></body></html>';
                exit;
            }
        }

        $this->assign('info', $info);
        return $this->fetch();
    }

    /**
     * 获取上传错误信息
     */
    private function getUploadError($error_code)
    {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return '上传的文件超过了PHP.INI中upload_max_filesize指令限制的大小';
            case UPLOAD_ERR_FORM_SIZE:
                return '上传的文件超过了HTML表单中MAX_FILE_SIZE指令指定的大小';
            case UPLOAD_ERR_PARTIAL:
                return '上传的文件只有部分被上传';
            case UPLOAD_ERR_NO_FILE:
                return '没有文件被上传';
            case UPLOAD_ERR_NO_TMP_DIR:
                return '找不到临时文件夹';
            case UPLOAD_ERR_CANT_WRITE:
                return '文件写入失败';
            case UPLOAD_ERR_EXTENSION:
                return '文件上传因PHP扩展而停止';
            default:
                return '未知上传错误';
        }
    }
}