<?php

namespace app\index\controller;

use think\Controller;
use think\Request;
use think\Db;

/**
 * 下单控制器
 */
class RotOrder extends Base
{
    /**
     * 首页
     */
    public function index()
    {
        $uinfo = db('xy_users')->where('id', session('user_id'))->find();
        // 安全处理level值，确保为数字
        $levelValue = isset($uinfo['level']) && is_numeric($uinfo['level']) ? (int)$uinfo['level'] : 0;

        // 获取URL参数type，如果没有则使用用户的VIP等级
        $requestType = input('get.type/d', 0);
        if ($requestType > 0) {
            // 检查用户是否有权限访问请求的VIP等级
            if ($requestType > $levelValue) {
                $this->error_h('您的VIP等级不足，无法访问此专区', "/index/index/home.html");
            }

            // 检查VIP等级开关是否开启
            if (!\app\common\service\VipLevelService::isVipLevelEnabled($requestType)) {
                $this->error_h('该VIP等级暂时关闭，请选择其他等级', "/index/index/home.html");
            }

            $type = $requestType;
        } else {
            // 修复：直接使用用户的VIP等级，不要加1
            $type = $levelValue > 0 ? $levelValue : 1; // 如果用户等级为0，默认为VIP1
        }

        $level_info = db('xy_level')->where('level', $type)->find();

        // 如果找不到对应等级信息，使用VIP1作为默认
        if (!$level_info) {
            $level_info = db('xy_level')->where('level', 1)->find();
            $type = 1;
        }

        /*if ($uinfo['balance'] < $level_info['deal_min_balance']) {
            $this->error_h('金额不足，最低需要余额' . $level_info['deal_min_balance'] . '元方可进入此专区', "/index/index/home.html");
        }*/
        $this->num_min = $level_info['num_min'];
        // 定义今日时间范围
        $today_start = strtotime(date('Y-m-d 00:00:00'));
        $today_end = strtotime(date('Y-m-d 23:59:59'));
        $today_where = [
            ['uid','=',session('user_id')],
            ['addtime','between',[$today_start, $today_end]],
        ];

        // 计算今日已抢佣金
        $this->day_deal = Db::name('xy_convey')->where($today_where)->where('status','in',[1,3,5])->sum('commission');

        // 计算今日已抢单数
        $this->day_d_count = Db::name('xy_convey')->where($today_where)->where('status','in',[0,1,3,5])->count('id');

        $yes1 = strtotime( date("Y-m-d 00:00:00",strtotime("-1 day")) );
        $yes2 = strtotime( date("Y-m-d 23:59:59",strtotime("-1 day")) );
        $this->price = Db::name('xy_users')->where('id',session('user_id'))->sum('balance');
        $this->lock_deal = Db::name('xy_users')->where('id',session('user_id'))->sum('freeze_balance');
        $this->yes_team_num = Db::name('xy_reward_log')->where('uid',session('user_id'))->where('addtime','between',[$yes1,$yes2])->where('status',1)->sum('num');//获取下级返佣数额
        $this->today_team_num = Db::name('xy_reward_log')->where('uid',session('user_id'))->where('addtime','between',[strtotime('Y-m-d'),time()])->where('status',1)->sum('num');//获取下级返佣数额

        //分类
        $this->cate = Db::name('xy_goods_cate')->alias('c')
            ->leftJoin('xy_level u','u.id=c.level_id')
            ->field('c.name,c.cate_info,c.cate_pic,u.name as levelname,u.pic,u.level,u.bili,u.order_num')
            ->find($type);
        $this->beizhu = db('xy_index_msg')->where('id',9)->value('content');;


        $this->yes_user_yongjin = db('xy_convey')->where('uid',session('user_id'))->where('status',1)->where('addtime','between',[$yes1,$yes2])->sum('commission');
        $this->user_yongjin = db('xy_convey')->where('uid',session('user_id'))->where('status',1)->sum('commission');


        $member_level = db('xy_level')->order('level asc')->select();;
        $order_num = $member_level[0]['order_num'];
        if (!empty($uinfo['level'])){
            $order_num = db('xy_level')->where('level',$uinfo['level'])->value('order_num');;
        }
        $this->order_num = $order_num;

        // 计算今日剩余可抢单次数
        $remaining_orders = max(0, $order_num - $this->day_d_count);
        $this->remaining_orders = $remaining_orders;

        $goods_pic_list = db('xy_goods_list')->field('goods_pic')->limit(100)->select();
        $this->goods_pic_list = array_column($goods_pic_list, 'goods_pic');

        $ll = [];
        $date_time_list = [];
        $h = date('H');
        // 会员动态 白天10点到22点
        $_this_h = date('H');
        if ($_this_h >= 10 && $_this_h < 22) {
            for ($i = 0; $i < 30; $i ++) {
                $_this_i = date('i');
                $iii = mt_rand(0, $_this_i);
                $_this_s = $iii == $_this_i ? date('s') : 60;
                $date_time_list[] = strtotime(date("Y-m-d") . ' ' . $_this_h . ':' . $iii . ':' . mt_rand(0, $_this_s));
            }
        } else if ($_this_h >= 22) {
            $_this_h = 21;
            for ($i = 0; $i < 30; $i ++) {
                $date_time_list[] = strtotime(date("Y-m-d") . ' ' . $_this_h . ':' . mt_rand(0, 59) . ':' . mt_rand(0, 59));
            }
        } else {
            $_this_h = 21;
            $_this_ymd = date('Y-m-d', strtotime(date("Y-m-d")) - 24 * 3600);
            for ($i = 0; $i < 30; $i ++) {
                $date_time_list[] = strtotime($_this_ymd . ' ' . $_this_h . ':' . mt_rand(0, 59) . ':' . mt_rand(0, 59));
            }
        }
        rsort($date_time_list);

        for ($i = 0; $i < 30; $i ++) {
            $mobile = 1 . mt_rand(3, 8) . mt_rand(1, 9) . '****' . mt_rand(1, 9) . mt_rand(1, 9) . mt_rand(1, 9) . mt_rand(1, 9);
            $type = mt_rand(0, 100);
            $ll[] = [
                'date_time' => date('m-d H:i:s', $date_time_list[$i]),
                'mobile' => $mobile,
                'type' => $type,
            ];
        }
        $this->ll = $ll;

        $member_address = db('xy_member_address')->where('uid', session('user_id'))->select();
        $this->has_member_address = !empty($member_address) ? 1 : 0;

        return $this->fetch();
    }
  /**
    *提交抢单
    */
    public function submit_order()
    {
        // $tmp = $this->check_deal();
        // if($tmp) return json($tmp);
        $res = check_time(9,22);
        //if($res) return json(['code'=>1,'info'=>'禁止在9:00~22:00以外的时间段执行当前操作!']);

        $res = check_time(config('order_time_1'),config('order_time_2'));
        $str = config('order_time_1').":00  - ".config('order_time_2').":00";
        if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!')]);

        $uid = session('user_id');

        // 检查实名认证状态
        $user_info = Db::name('xy_users')->where('id', $uid)->find();
        if ($user_info['id_status'] != 1) {
            return json(['code'=>1,'info'=>lang('Please complete identity verification before accepting orders'),'url'=>url('my/identity_verify'),'need_jump'=>1]);
        }

        $add_id = db('xy_member_address')->where('uid',$uid)->value('id');//获取收款地址信息
       // if(!$add_id) return json(['code'=>1,'info'=>lang('还没有设置收货地址')]);

        $uinfo = db('xy_users')->where('id', session('user_id'))->find();
        $level = $uinfo['level'] ?: 1; // 如果用户等级为空，默认为1
        $level_info = db('xy_level')->where('level', $level)->find();

        // 修复：检查level_info是否存在，如果不存在则使用默认VIP1
        if (!$level_info) {
            $level = 1;
            $level_info = db('xy_level')->where('level', 1)->find();
            if (!$level_info) {
                return json(['code'=>1,'info'=>'系统配置错误，请联系管理员']);
            }
        }

        // 计算今日已抢单数 - 限制到今天的时间范围
        $today_start = strtotime(date('Y-m-d 00:00:00'));
        $today_end = strtotime(date('Y-m-d 23:59:59'));
        $today_where = [
            ['uid','=',session('user_id')],
            ['addtime','between',[$today_start, $today_end]],
        ];
        $day_d_count = Db::name('xy_convey')->where($today_where)->where('status','in',[0,1,3,5])->count();


       if($day_d_count >= $level_info['order_num']){
            // 计算下次可抢单时间（第二天的开始时间）
            $tomorrow = strtotime('tomorrow');
            $nextOrderTime = date('Y-m-d H:i:s', $tomorrow + 3600 * config('order_time_1')); // 第二天的开始抢单时间
            return json(['code'=>1,'info'=>lang('抢单次数已达到上限，下次可抢单时间：').$nextOrderTime]);
       }


        if ($uinfo['balance'] < $level_info['num_min']) {
            return json(['code'=>1,'info'=>lang('您的会员专区所对应的匹配订单任务要求账户可用余额最低为') . $level_info['num_min'] . lang('元，该账户余额未满足会员专区任务订单匹配要求，无法继续进行匹配订单～请充值后再试！')]);
        }
        // dump($uinfo);
        // dump($level_info);
        // exit;
        //检查交易状态
        // $sleep = mt_rand(config('min_time'),config('max_time'));
        $res = db('xy_users')->where('id',$uid)->update(['deal_status'=>2]);//将账户状态改为等待交易
        if($res === false) return json(['code'=>1,'info'=>lang('抢单失败，请稍后再试！')]);
        // session_write_close();//解决sleep造成的进程阻塞问题
        // sleep($sleep);
        //
        $cid = input('get.cid/d',1);

        // 检查VIP等级权限 - 如果指定了分类ID，需要验证权限
        if ($cid > 1) {
            $categoryInfo = db('xy_goods_cate')->find($cid);
            if ($categoryInfo) {
                $categoryVipLevel = $categoryInfo['vip_level'] ?: 1;
                // 检查用户VIP等级是否足够
                if ($level < $categoryVipLevel) {
                    return json(['code'=>1,'info'=>'您的VIP等级不足，无法接取此类任务']);
                }
                // 检查VIP等级开关是否开启
                if (!\app\common\service\VipLevelService::isVipLevelEnabled($categoryVipLevel)) {
                    return json(['code'=>1,'info'=>'该VIP等级暂时关闭，无法接取此类任务']);
                }
            }
        }

        $count = db('xy_goods_list')->where('cid','=',$cid)->count();
   //     if($count < 1) return json(['code'=>1,'info'=>lang('抢单失败，商品库存不足！')]);
        // 使用已经计算好的今日订单数，避免重复查询
        $number = $day_d_count;
        if($number>=$level_info['order_num']){
            return json(['code'=>1,'info'=>"The number of orders for this level today has been capped"]);
        }

        $res = model('admin/Convey')->create_order($uid,$cid);
        return json($res);
    }

    /**
     * 停止抢单
     */
    public function stop_submit_order()
    {
        $uid = session('user_id');
        $res = db('xy_users')->where('id',$uid)->where('deal_status',2)->update(['deal_status'=>1]);
        if($res){
            return json(['code'=>0,'info'=>lang('操作成功')]);
        }else{
            return json(['code'=>1,'info'=>lang('操作失败')]);
        }
    }

    public function xxxx() {
        echo 'xxxx';
    }
}