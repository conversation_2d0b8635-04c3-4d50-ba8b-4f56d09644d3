<?php

// +----------------------------------------------------------------------
// | ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2019 
// +----------------------------------------------------------------------

// +----------------------------------------------------------------------

// +----------------------------------------------------------------------
// | 

// +----------------------------------------------------------------------

namespace app\index\controller;

use library\Controller;
use think\Db;
use think\facade\Cookie;
use app\admin\service\CaptchaService;

/**
 * 登录控制器
 */
class User extends Controller
{

    protected $table = 'xy_users';

    /**
     * 空操作 用于显示错误页面
     */
    public function _empty($name){

        return $this->fetch($name);
    }
    public function lang(){
        
         $lang=input('type');
          switch ($lang) {
              case 'en-ww':
                  cookie('think_var', 'en-ww');
              break;
              case 'zh-cn':
                  cookie('think_var', 'zh-cn');
              break;
              case 'en-in':
                 cookie('think_var','en-in');
              break;
              case 'arabic':
                 cookie('think_var','arabic');
                 cookie('lang','arabic', time()+3600*24*30); // 30天有效期
              break;
          }

    }
    //用户登录页面
    public function login()
    {
       
       
        if(session('user_id')) $this->redirect('index/index');
         $lang=cookie('lang');
        if($lang == 'en-us'){
            $lang = 'en-us';
        }elseif($lang == 'baxi'){
            $lang = 'baxi';
        }elseif($lang == 'moxige'){
            $lang = 'moxige';
        }elseif($lang == 'tuerqi'){
            $lang = 'tuerqi';
        }elseif($lang == 'arabic'){
            $lang = 'arabic';
        }else{
            cookie("lang",config('app.default_lang'));
            return $this->redirect('login');
            
        }
        
        
        
        
        
        $this->assign('lang',$lang);
        return $this->fetch();
    }

    //用户登录接口
    // 允许使用 用户名 登陆
    public function do_login()
    {
        // $this->applyCsrfToken();//验证令牌
        $tel = input('post.tel/s','');
        $country_code = input('post.country_code/s', '');
        
        // 如果提供了国家区号，将其添加到电话号码前
        if ($country_code) {
            // 查询是否直接使用带国家区号的手机号登录
            $full_tel = "+" . $country_code . $tel;
            $num = Db::table($this->table)->where('tel', $full_tel)->whereOr("username", $tel)->count();
            if ($num) {
                $tel = $full_tel; // 如果找到带区号的记录，则使用完整号码
            }
        }
        
        /*if(!is_mobile($tel)){
            return json(['code'=>1,'info'=>'手机号码格式不正确']);
        }*/
        // $num = Db::table($this->table)->where(['tel'=>$tel])->count();
        $num = Db::table($this->table)->where('tel', $tel)->whereOr("username", $tel)->count();
        if(!$num){
            return json(['code'=>1,'info'=>lang('账号不存在')]);
        }

        $pwd         = input('post.pwd/s', ''); 
        $keep        = input('post.keep/b', false);    
        $jizhu        = input('post.jizhu/s', 0);

        // $userinfo = Db::table($this->table)->field('id,pwd,salt,pwd_error_num,allow_login_time,status,login_status,headpic,level')->where('tel',$tel)->find();
        $userinfo = Db::table($this->table)->field('id,pwd,salt,pwd_error_num,allow_login_time,status,login_status,headpic,level')->where('tel', $tel)->whereOr("username", $tel)->find();
        if(!$userinfo)return json(['code'=>1,'info'=>lang('用户不存在')]);
        if($userinfo['status'] != 1)return json(['code'=>1,'info'=>lang('用户已被禁用')]);
        //if($userinfo['login_status'])return ['code'=>1,'info'=>'此账号已在别处登录状态'];
        if($userinfo['allow_login_time'] && ($userinfo['allow_login_time'] > time()) && ($userinfo['pwd_error_num'] > config('pwd_error_num')))return ['code'=>1,'info'=>lang('密码连续错误次数太多，请').config('allow_login_min').lang('分钟后再试')];  
        if($userinfo['pwd'] != sha1($pwd.$userinfo['salt'].config('pwd_str'))){
            Db::table($this->table)->where('id',$userinfo['id'])->update(['pwd_error_num'=>Db::raw('pwd_error_num+1'),'allow_login_time'=>(time()+(config('allow_login_min') * 60))]);
            return json(['code'=>1,'info'=>lang('密码错误')]);  
        }
        
        Db::table($this->table)->where('id',$userinfo['id'])->update(['pwd_error_num'=>0,'allow_login_time'=>0,'login_status'=>1, 'ip'=>$this->request->ip()]);
        session('user_id',$userinfo['id']);
        session('avatar',$userinfo['headpic']);
        // 核心修复：确保level值是数字后再存入session
        $level = isset($userinfo['level']) && is_numeric($userinfo['level']) ? (int)$userinfo['level'] : 0;
        session('level', $level);
        
        // 安全修复：不再自动设置user_id cookie，防止跨浏览器自动登录
        // cookie('user_id',$userinfo['id']); // 已移除

        // 只有在用户明确选择"记住我"时才设置持久化cookie
        if($keep){
            Cookie::forever('user_id',$userinfo['id']);
            Cookie::forever('tel',$tel);
            Cookie::forever('pwd',$pwd);
        }
        
        // 只保存登录信息用于自动填充，不用于身份验证
        if ($jizhu) {
            cookie('tel',$tel);
            cookie('pwd',$pwd);
        }
        
        // 保留用户选择的语言设置
        $current_lang = cookie('lang');
        if($current_lang == 'arabic') {
            // 如果当前是阿拉伯语，确保登录后仍然保持阿拉伯语
            cookie('lang', 'arabic', time()+3600*24*30); // 30天有效期
        }
        
        return json(['code'=>0,'info'=>lang('登录成功')]);  
    }

    /**
     * 用户注册接口
     */
    public function do_register()
    {
        try {
            // 记录详细的接收数据，便于调试
            $post_data = input('post.');
            \think\facade\Log::write('注册请求数据详情: ' . json_encode($post_data), 'info');
            
            $tel = input('post.tel/s','');
            $country_code = input('post.country_code/s', '');
            \think\facade\Log::write('原始电话: ' . $tel . ', 国家代码: ' . $country_code, 'info');
            
            // 处理国家区号
            if ($country_code) {
                // 确保国家代码格式正确（如果前端已经加了+号，就不需要再加）
                if (strpos($country_code, '+') !== 0) {
                    $country_code = '+' . $country_code;
                }
                
                // 电话号码不应该包含国家代码，只存储纯号码
                if (strpos($tel, $country_code) === 0) {
                    $tel = substr($tel, strlen($country_code));
                }
                
                \think\facade\Log::write('处理后的电话: ' . $tel . ', 格式化国家代码: ' . $country_code, 'info');
            }
            
            // 验证电话号码
            if(!$tel) {
                return json(['code'=>1,'info'=>'Phone number cannot be empty']);
            }
            
            // 检查电话号码是否已存在 - 注意这里需要检查格式化后的号码
            $formatted_tel = $country_code . $tel;
            \think\facade\Log::write('检查电话是否存在: ' . $formatted_tel, 'info');
            
            $num = Db::table($this->table)->where('tel', $formatted_tel)->count();
            if($num) {
                return json(['code'=>1,'info'=>'Phone number already exists']);
            }
            
            // 验证用户名
            $user_name = input('post.user_name/s','');
            if(!$user_name) {
                return json(['code'=>1,'info'=>'Username cannot be empty']);
            }
            
            // 检查用户名格式
            if(!preg_match('/^([0-9A-Za-z]|[一-龥]){2,12}$/', $user_name)) {
                \think\facade\Log::write('用户名格式检查失败: ' . $user_name, 'error');
                return json(['code'=>1,'info'=>'Username format incorrect']);
            }
            
            // 检查用户名是否已存在
            $num = Db::table($this->table)->where('username', $user_name)->count();
            if($num) {
                return json(['code'=>1,'info'=>'Username already exists']);
            }
            
            // 验证密码
            $pwd = input('post.pwd/s', '');
            $pwd2 = input('post.deposit_pwd/s', '');
            
            if(!$pwd) {
                return json(['code'=>1,'info'=>'Login password cannot be empty']);
            }
            
            if($pwd2 != $pwd){
                return json(['code'=>1,'info'=>'The two passwords do not match']);
            }
            
            $zjpwd = input('post.pwd2/s', '');
            if(!$zjpwd) {
                return json(['code'=>1,'info'=>'Withdrawal password cannot be empty']);
            }
            
            $invite_code = input('post.invite_code/s', '');     //邀请码
            if(!$invite_code) return json(['code'=>1,'info'=>'Invitation code cannot be empty']);

            $pid = 0;
            if($invite_code) {
                $parentinfo = Db::table($this->table)->field('id,status')->where('invite_code',$invite_code)->find();
                if(!$parentinfo) return json(['code'=>1,'info'=>'Invitation code does not exist']);
                if($parentinfo['status'] != 1) return json(['code'=>1,'info'=>'The recommender has been disabled']);

                $pid = $parentinfo['id'];
            }

            // 保存完整的电话号码，包含国家代码
            try {
                $res = model('admin/Users')->add_users($formatted_tel, $user_name, $pwd, $pid, '', $zjpwd);
                \think\facade\Log::write('注册结果: ' . json_encode($res), 'info');
                return json($res);
            } catch (\Exception $e) {
                // 捕获所有异常，记录详细错误信息
                \think\facade\Log::write('注册异常: ' . $e->getMessage() . ' 位置: ' . $e->getFile() . ':' . $e->getLine() . "\n" . $e->getTraceAsString(), 'error');
                return json(['code'=>1,'info'=>'Registration failed: ' . $e->getMessage()]);
            }
        } catch (\Exception $e) {
            // 捕获所有异常，记录详细错误信息
            \think\facade\Log::write('注册异常: ' . $e->getMessage() . ' 位置: ' . $e->getFile() . ':' . $e->getLine(), 'error');
            return json(['code'=>1,'info'=>'Registration failed: ' . $e->getMessage()]);
        }
    }
    
    public function yzm()
    {
        $captcha = new CaptchaService();
         
    return ['status'=>1,'img'=>$captcha->getData(),'hidden'=>$captcha->getUniqid()];
    }

    public function logout(){
        \Session::delete('user_id');
        Cookie::delete('user_id');
        $this->redirect('login');
    }

    /**
     * 重置密码
     */
    public function do_forget()
    {
        if(!request()->isPost()) return json(['code'=>1,'info'=>lang('错误请求')]);
        $tel = input('post.tel/s','');
        $username = input('post.username/s','');
        $pwd = input('post.pwd/s','');
        
        // 首先检查手机号是否存在
        $user = Db::table('xy_users')->field('id,username')->where(['tel'=>$tel])->find();
        if(!$user) return json(['code'=>1,'info'=>lang('手机号不存在')]);
        
        // 然后检查用户名是否匹配
        if($username != $user['username']) return json(['code'=>1,'info'=>lang('信息错误，更改密码失败，请联系在线客服处理')]);

        // 如果匹配，则重置密码
        $res = model('admin/Users')->reset_pwd($tel,$pwd);
        return json($res);
    }

    public function register()
    {
        
        $lang=cookie('lang');
        if($lang == 'en-us'){
            $lang = 'en-us';
        }elseif($lang == 'baxi'){
            $lang = 'baxi';
        }elseif($lang == 'moxige'){
            $lang = 'moxige';
        }elseif($lang == 'tuerqi'){
            $lang = 'tuerqi';
        }else{
            cookie("lang",config('app.default_lang'));
            return $this->redirect('login');
            
        }
        
        $param = \Request::param(true);
        $this->invite_code = isset($param[1]) ? trim($param[1]) : '';  
        
        
         $this->captcha = new CaptchaService();
        
       
        $this->invite_code = isset($param[1]) ? trim($param[1]) : '';  
        return $this->fetch();
    }
    
     //异步回调
    public function hui(){
       // echo 1;
        // $post = json_decode($_POST,true);
        $post=file_get_contents("php://input");
        $post=json_decode($post,true);
        $key=array_keys($post);
        if(in_array('qrurl',$key)){
            return "success";
        }
        $oinfo =  db('xy_recharge')->find($post['orderid']);
        if($oinfo['status']== 1){
          if($post['ispay']==1){
            $res = Db::name('xy_recharge')->where('id',$post['orderid'])->update(['endtime'=>time(),'status'=>2]);
             if ($oinfo['is_vip']) {
                    $res1 = Db::name('xy_users')->where('id',$oinfo['uid'])->update(['level'=>$oinfo['level']]);
                }else{
                    $res1 = Db::name('xy_users')->where('id',$oinfo['uid'])->setInc('balance',$oinfo['num']);
                }
                
                $res2 = Db::name('xy_balance_log')
                        ->insert([
                            'uid'=>$oinfo['uid'],
                            'oid'=>$post['orderid'],
                            'num'=>$oinfo['num'],
                            'type'=>1,
                            'status'=>1,
                            'addtime'=>time(),
                        ]);
          
            }else{
                $res = Db::name('xy_recharge')->where('id',$post['orderid'])->update(['endtime'=>time(),'status'=>3]);
                $res1 = Db::name('xy_message')
                        ->insert([
                            'uid'=>$oinfo['uid'],
                            'type'=>2,
                            'title'=>'system notification',
                            'content'=>'Top-up order'.$post['orderid'].'Has been returned, if you have any questions, please contact customer service',
                            'addtime'=>time()
                        ]);
            }
            
                Db::commit();

                if ($oinfo['is_vip']) {
                    goto end;
                }

                /************* 发放推广奖励 *********/
                $uinfo = Db::name('xy_users')->field('id,active')->find($oinfo['uid']);
                if($uinfo['active']===0){
                    Db::name('xy_users')->where('id',$uinfo['id'])->update(['active'=>1]);
                    //将账号状态改为已发放推广奖励
                    $userList = model('Users')->parent_user($uinfo['id'],3);
                    if($userList){
                        foreach($userList as $v){
                            if($v['status']===1 && ($oinfo['num'] * config($v['lv'].'_reward') != 0)){
                                    Db::name('xy_reward_log')
                                    ->insert([
                                        'uid'=>$v['id'],
                                        'sid'=>$uinfo['id'],
                                        'oid'=>$post['orderid'],
                                        'num'=>$oinfo['num'] * config($v['lv'].'_reward'),
                                        'lv'=>$v['lv'],
                                        'type'=>1,
                                        'status'=>1,
                                        'addtime'=>time(),
                                    ]);
                            }
                        }
                    }
                }
                /************* 发放推广奖励 *********/

                end:
                return "success";
            
        }
         return "success";
    }
   public function hui2(){
       // echo 1;
        $post = $_POST;
        $oinfo =  db('xy_recharge')->find($post['mchOrderNo']);
        if($oinfo['status'] == 1){
          if($post['tradeResult']==1){
            $res = Db::name('xy_recharge')->where('id',$post['mchOrderNo'])->update(['endtime'=>time(),'status'=>2]);
             if ($oinfo['is_vip']) {
                    $res1 = Db::name('xy_users')->where('id',$oinfo['uid'])->update(['level'=>$oinfo['level']]);
                }else{
                    $res1 = Db::name('xy_users')->where('id',$oinfo['uid'])->setInc('balance',$oinfo['num']);
                }
                
                $res2 = Db::name('xy_balance_log')
                        ->insert([
                            'uid'=>$oinfo['uid'],
                            'oid'=>$post['mchOrderNo'],
                            'num'=>$oinfo['num'],
                            'type'=>1,
                            'status'=>1,
                            'addtime'=>time(),
                        ]);
          
            }else{
                $res = Db::name('xy_recharge')->where('id',$post['mchOrderNo'])->update(['endtime'=>time(),'status'=>3]);
                $res1 = Db::name('xy_message')
                        ->insert([
                            'uid'=>$oinfo['uid'],
                            'type'=>2,
                            'title'=>'system notification',
                            'content'=>'Top-up order'.$post['mchOrderNo'].'Has been returned, if you have any questions, please contact customer service',
                            'addtime'=>time()
                        ]);
            }
            if($res && $res1){
                Db::commit();

                if ($oinfo['is_vip']) {
                    goto end;
                }

                /************* 发放推广奖励 *********/
                $uinfo = Db::name('xy_users')->field('id,active')->find($oinfo['uid']);
                if($uinfo['active']===0){
                    Db::name('xy_users')->where('id',$uinfo['id'])->update(['active'=>1]);
                    //将账号状态改为已发放推广奖励
                    $userList = model('Users')->parent_user($uinfo['id'],3);
                    if($userList){
                        foreach($userList as $v){
                            if($v['status']===1 && ($oinfo['num'] * config($v['lv'].'_reward') != 0)){
                                    Db::name('xy_reward_log')
                                    ->insert([
                                        'uid'=>$v['id'],
                                        'sid'=>$uinfo['id'],
                                        'oid'=>$post['mchOrderNo'],
                                        'num'=>$oinfo['num'] * config($v['lv'].'_reward'),
                                        'lv'=>$v['lv'],
                                        'type'=>1,
                                        'status'=>1,
                                        'addtime'=>time(),
                                    ]);
                            }
                        }
                    }
                }
                /************* 发放推广奖励 *********/

                end:
                return "SUCCESS";
            }else{
                return 'error';
            }
        }
    }
    public function ti(){
        $post = $_POST;
        if($post){
            $rows=Db::name("xy_deposit")->where('id',$post['merTransferId'])->find();
            if($rows['status']==4){
                if($post['tradeResult']==1){
                    $data=['status'=>2];
                    Db::name("xy_deposit")->where('id',$rows['id'])->update($data);
                    Db::name('xy_balance_log')->where('oid',$rows['id'])->update(['status'=>1]);
                    return "success";
                }else{
                    $data=['status'=>3];
                    Db::name("xy_deposit")->where('id',$rows['id'])->update($data);
                    Db::name('xy_users')->where('id',$rows['uid'])->setInc('balance',$rows['num']);
                    return "error";
                }
            }
        }
    }
    public function ti2(){
        $post=file_get_contents("php://input");
        $post=json_decode($post,true);
        if($post){
            $rows=Db::name("xy_deposit")->where('id',$post['orderid'])->find();
            if($rows['status']==4){
                $key=array_keys($post);
                if(in_array('iscancel',$key)){
                    $data=['status'=>3];
                    Db::name("xy_deposit")->where('id',$rows['id'])->update($data);
                    Db::name('xy_users')->where('id',$rows['uid'])->setInc('balance',$rows['num']);
                    return "success";
                }
                if($post['ispay']==1){
                    $data=['status'=>2];
                    Db::name("xy_deposit")->where('id',$rows['id'])->update($data);
                    Db::name('xy_balance_log')->where('oid',$rows['id'])->update(['status'=>1]);
                    return "success";
                }else{
                    $data=['status'=>3];
                    Db::name("xy_deposit")->where('id',$rows['id'])->update($data);
                    Db::name('xy_users')->where('id',$rows['uid'])->setInc('balance',$rows['num']);
                    return "error";
                }
            }
        }
    }
    
    public function lixibao(){
        $time=time();
        //查询未取出订单  并且今天还未 收益订单
        $starttime=strtotime(date('y-m-d',$time));
        $endtime=strtotime(date('y-m-d',strtotime("+1 day")));
        $rows=Db::name('xy_lixibao')->where('is_qu',0)->whereTime('update_time', 'not between', [$starttime, $endtime])->select();
        //结算时间  到了自动结算   (间隔一天才能结算)
        foreach($rows as $k=>$v){
            // $zhi=Db::name('xy_lixibao_list')->where('id',$v['sid'])->find();
            if(time()>=($v['addtime']+86400) ){
                if(time()>=$v['endtime']){
                    // $money=($v['num']+$v['yuji_num'])-($v['num']*$zhi['shouxu']);
                    $userfo = Db::name('xy_users')->where('id', $v['uid'])->find();
                    $data=[
                      "balance" => ($userfo['balance'] + $v['num'] + $v['yuji_num']),
                      "freeze_balance" => "0.00",
                      "lixibao_balance" => "0",
                      "lixibao_dj_balance" => "0.0000"
                    ];
                    Db::name('xy_users')->where('id',$v['uid'])->update($data);
                    Db::name('xy_lixibao')->where('uid', $v['uid'])->update(['is_qu'=>1]);
                    // $rows=Db::name('xy_lixibao')->where('id',$v['id'])->update($data);
                    // Db::name('xy_users')->where('id',$v['uid'])->setInc('balance',$money);
                    echo '已自动结算';
                }else{
                    //添加昨日收益
                    $z_time=$time-86400;
                    $dailyIncome = $v['bili']*$v['num'];
                    
                    // 记录每日收益到余额日志
                    db('xy_balance_log')->insert([
                        'uid'=>$v['uid'],
                        'oid'=>getSn('LXB'),
                        'num'=>$dailyIncome,
                        'type'=>23,
                        'status'=>1,
                        'addtime'=>$z_time
                    ]);
                    
                    // 累计到总收益
                    Db::name('xy_lixibao')->where('uid', $v['uid'])->setInc('sum_shouyi', $dailyIncome);
                    
                    // 将收益添加到用户余额
                    Db::name('xy_users')->where('id', $v['uid'])->setInc('balance', $dailyIncome);
                    
                    // 更新最后收益时间
                    Db::name('xy_lixibao')->where('uid', $v['uid'])->update(['update_time'=>$z_time]);
                     
                    echo '每日收益已发放';
                }
            }
        }
        
        if(empty($rows)){
            echo '没有数据';
        }
    }
      public function reset_qrcode()
    {
        $uinfo = Db::name('xy_users')->field('id,invite_code')->select();
        foreach ($uinfo as $v) {
            $model = model('admin/Users');
            $model->create_qrcode($v['invite_code'],$v['id']);
        }
        return '重新生成用户二维码图片成功';
    } 
}