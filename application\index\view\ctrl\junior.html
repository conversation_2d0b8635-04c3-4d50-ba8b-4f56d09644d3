<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="stylesheet" href="/p_static1/css/base.css">
		<title>{:lang('团队报表')}</title>
		
		<link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
		<script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
		<script charset="utf-8" src="/static_new/js/common.js"></script>
		
		<script charset="utf-8" src="/static_new6/js/rolldate.js"></script>
		<link rel="stylesheet" href="/static_new6/css/rolldate.css">
		<style type="text/css" title="fading circle style">
		    .circle-color-8 > div::before {
		        background-color: #ccc;
		    }
		    .list {
		        height: 77vh;
		        overflow-y: scroll;
		    }
		
		    .list>li {
		        font-size: .5rem;
		        border-bottom: .1rem solid rgb(248, 242, 242);
		        padding: .5rem 1rem;
		    }
		
		    .order_info {
		        margin-top: .5rem;
		        display: flex;
		    }
		
		    .info_img {
		        width: auto;
		        height: 3rem;
		        background: #eeeeee;
		    }
		
		    .info_data {
		        max-width: 55%;
		        margin: 0 0 0 0.5rem;
		        display: flex;
		        justify-content: space-between;
		        flex-direction: column;
		    }
		
		    nav p{color:#907878}
		    .wait_list2 *{color:#907878}
		
		    .info_store,
		    .money {
		        color: #00bcd4;
		    }
		
		    .info_money {
		        margin-left: auto;
		        text-align: right;
		    }
		    .no-data{
		        border: none !important;
		        text-align: center;
		    }
		    .info_name,.order_num{color:#000;font-size: 13px}
		    .info_name,.info_store,.money,.info_num{font-size: 12px}
		    .info_data{display: inline-block}
		    .info_data>p,.info_money>p{
		        margin-bottom: 3px;
		    }
		    .info_img img{max-height: 60px}
		    .info_img{background: none;height: auto}
		    .mint-tab-container-item li {
		        border-bottom: .1rem solid rgb(248, 242, 242);
		        padding: 0;
		    }/* 通用分页 */
		    .pagination-container {
		        line-height: 40px;
		        text-align: right;
		    }
		    .pagination-container > span {
		        color: #666;
		        font-size: 9pt;
		    }
		    .pagination-container > ul {
		        float: right;
		        display: inline-block;
		        margin: 0;
		        padding: 0;
		    }
		    .pagination-container > ul > li {
		        z-index: 1;
		        display: inline-block;
		    }
		    .pagination-container > ul > li > a, .pagination-container > ul > li > span {
		        color: #333;
		        width: 33px;
		        height: 30px;
		        border: 1px solid #dcdcdc;
		        display: inline-block;
		        margin-left: -1px;
		        text-align: center;
		        line-height: 28px;
		    }
		    .pagination-container > ul > li > span {
		        background: #dcdcdc;
		        cursor: default;
		    }
		    .van-tab--active span{
		        color: #ff9a2c;
		    }
		    .circle-color-23 > div::before {
		        background-color: #ccc;
		    }
		    .notdata{
		        display: block;
		        text-align: center;
		        padding: 30px;
		    }
		</style>
		<style>
			body {
				padding-top: 3.1rem;
				padding-bottom: 6.5rem;
				background-color: rgba(245, 245, 247, 1);
			}
			/* 导航栏 */
			.p_nav {
				box-sizing: border-box;
				position: fixed;
				top: 0;
				left: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 3.1rem;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
				background-color: #fff;
				z-index: 99;
			}
			.p_nav-arrow {
				position: absolute;
				left: 0.75rem;
				top: 50%;
				width: 1.65rem;
				height: 1.65rem;
				transform: translate(0, -50%);
			}
			/* 信息 */
			.p_info-wrapper {
				margin: 0.75rem 0.75rem 0;
				padding: 0.75rem 0.75rem 0;
				background-color: #fff;
				border-radius: 0.5rem;
			}
			.p_info {
				display: flex;
			}
			.p_info-item {
				width: 33.33%;
				padding-bottom: 1rem;
			}
			.p_info-item-title {
				max-width: 90%;
				height: 1.9rem;
				font-size: 0.6rem;
				line-height: 0.7rem;
				color: rgba(119, 123, 158, 1);
			}
			.p_info-item-num {
				font-size: 0.7rem;
				line-height: 0.7rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_info-divide {
				margin-bottom: 1rem;
				width: 100%;
				height: 0.05rem;
				background-color: rgba(207, 209, 230, 1);
			}
			/* 导航标签 */
			.p_tabs {
				display: flex;
				margin-top: 1.25rem;
				padding: 0 0.75rem 1rem;
				border-bottom: 0.05rem solid rgba(207, 209, 230, 1);
			}
			.p_tab-item {
				position: relative;
				display: flex;
				align-items: center;
				margin-right: 3.075rem;
				font-size: 0.9rem;
				line-height: 0.9rem;
				font-weight: 600;
				color: rgba(119, 123, 158, 1);
				white-space: nowrap;
			}
			.p_tab-item.active {
				color: rgba(36, 44, 107, 1);
			}
			.p_tab-item.active::after {
				display: block;
				content: '';
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translate(-50%, 300%) rotate(45deg);
				width: 0.2rem;
				height: 0.2rem;
				background-color: rgba(36, 44, 107, 1);
				border-radius: 0.05rem;
			}
			/* 页面 */
			.p_wrapper {}
			.p_list {
				margin: 1.075rem 0.75rem 0;
			}
			.p_list-item {
				position: relative;
				box-sizing: border-box;
				height: 10rem;
				margin-bottom: 0.55rem;
				padding: 1.175rem 0.75rem 0;
				background-color: rgba(255, 255, 255, 1);
				border-radius: 0.5rem;
			}
			.p_list-item-avatar {
				position: absolute;
				left: 0.75rem;
				top: -0.3rem;
				width: 3.25rem;
				height: 3.25rem;
				border-radius: 50%;
			}
			.p_list-item-name-wrapper {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-left: 3.75rem;
			}
			.p_list-item-name-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(119, 123, 158, 1);
			}
			.p_list-item-name-text {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(36, 44, 107, 1);
			}
			.p_list-item-time {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-left: 3.75rem;
				margin-top: 0.5rem;
				font-size: 0.55rem;
				line-height: 0.55rem;
				color: rgba(172, 175, 194, 1);
			}
			.p_list-item-divide {
				margin: 0.75rem 0;
				width: 100%;
				height: 0;
				border: 0.05rem dashed rgba(207, 209, 230, 1);
			}
			.p_list-item-paragraph {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 0.5rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
			}
			.p_list-item-paragraph-left {
				color: rgba(119, 123, 158, 1);
			}
			.p_list-item-paragraph-right {
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.red {
				color: rgba(255, 112, 112, 1);
			}
			/* 搜索 */
			.p_search-wrapper {
				box-sizing: border-box;
				position: fixed;
				left: 0;
				bottom: 0;
				width: 100%;
				height: 5rem;
				padding: 1rem 0.75rem 0;
				background-color: #fff;
				border-radius: 0.5rem 0.5rem 0 0;
				box-shadow: 0px 0.05rem 0.5rem rgba(204, 204, 230, 1);
			}
			.p_search-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(36, 44, 107, 1);
			}
			.p_search {
				display: flex;
				align-items: center;
				margin-top: 0.85rem;
			}
			.p_search-input-wrapper {
				box-sizing: border-box;
				display: flex;
				align-items: center;
				width: 5rem;
				padding: 0 0.5rem;
				height: 1.5rem;
				background-color: rgba(237, 239, 247, 1);
				border-radius: 0.25rem;
			}
			.p_search-input-wrapper input {
				width: 100%;
				height: 100%
			}
			.p_search-text {
				margin: 0 0.5rem;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_search-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left: 1.2rem;
				width: 3.875rem;
				height: 1.5rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				font-weight: 600;
				color: rgba(248, 217, 193, 1);
				background-color: rgba(36, 44, 107, 1);
				border-radius: 0.25rem;
			}
			
			/* 补充样式 */
			.rolldate-btn.rolldate-cancel {
				background-color: rgba(213, 215, 227, 1)!important;
				color: rgba(36, 44, 107, 1)!important;
			}
			.rolldate-btn.rolldate-confirm {
				background: rgba(36, 44, 107, 1)!important;
				color: rgba(248, 217, 193, 1)!important;
			}
			.rolldate-container header {
				background: rgba(248, 217, 193, 1)!important;
				color: rgba(122, 70, 31, 1)!important;
			}
		</style>
	</head>
	<body>
		<!-- 导航栏 -->
		<div class="p_nav">
			<!-- <img src="/p_static1/img/arrowleft_circle_blue.png" class="p_nav-arrow" onclick="window.history.go(-1);"> -->
			<img src="/p_static1/img/arrowleft_circle_blue.png" class="p_nav-arrow" onclick="window.location.href='/index/my/index';">
			<div>{:lang('团队报表')}</div>
		</div>
		
		<!-- 信息 -->
		<div class="p_info-wrapper">
			<div class="p_info">
				<div class="p_info-item">
					<div class="p_info-item-title">{:lang("团队余额")}</div>
					<div class="p_info-item-num">{$teamyue}</div>
				</div>
				<div class="p_info-item">
					<div class="p_info-item-title">{:lang("团队总上分")}</div>
					<div class="p_info-item-num">{$teamcz}</div>
				</div>
				<div class="p_info-item">
					<div class="p_info-item-title">{:lang('团队订单佣金')}</div>
					<div class="p_info-item-num">{$teamyj}</div>
				</div>
			</div>
			<div class="p_info-divide"></div>
			<div class="p_info">
				<div class="p_info-item">
					<div class="p_info-item-title">{:lang("团队流水")}</div>
					<div class="p_info-item-num">{$teamls}</div>
				</div>
				<div class="p_info-item">
					<div class="p_info-item-title">{:lang("团队总下分")}</div>
					<div class="p_info-item-num">{$teamtx}</div>
				</div>
				<div class="p_info-item">
					<div class="p_info-item-title">{:lang('团队人数')}</div>
					<div class="p_info-item-num">{$tuandui}{:lang('人')}</div>
				</div>
			</div>
		</div>
		
		<!-- 导航标签 -->
		<div class="p_tabs">
			<a class="p_tab-item <?php echo $level===1?'active':'' ?>" href="/index/ctrl/junior?level=1<?php $currentLang = cookie('lang') ?: cookie('think_lang') ?: 'en-us'; echo '&lang=' . $currentLang; ?>">
				{:lang('一级')}
			</a>
			<a class="p_tab-item <?php echo $level===2?'active':'' ?>" href="/index/ctrl/junior?level=2<?php $currentLang = cookie('lang') ?: cookie('think_lang') ?: 'en-us'; echo '&lang=' . $currentLang; ?>">
				{:lang('二级')}
			</a>
			<a class="p_tab-item <?php echo $level===3?'active':'' ?>" href="/index/ctrl/junior?level=3<?php $currentLang = cookie('lang') ?: cookie('think_lang') ?: 'en-us'; echo '&lang=' . $currentLang; ?>">
				{:lang('三级')}
			</a>
		</div>
		
		<!-- 页面 -->
		<div class="p_wrapper">
			<div class="p_list">
				{if $list}
				{volist name='list' id='v'}
				<?php
				//充值，改成用户余额
				$v['chongzhi'] = db('xy_recharge')->where('uid', $v['id'])->where('status', 2)->sum('num');
				//提现
				$v['tixian'] = db('xy_deposit')->where('uid', $v['id'])->where('status', 2)->sum('num');
				
				
				if ($uinfo['kouchu_balance_uid'] == $v['id']) {
				    $v['chongzhi'] -= $uinfo['kouchu_balance'];
				    $iskou = 1;
				}
				
				if ($uinfo['show_tel2']) {
				    $v['tel'] = substr_replace($v['tel'], '****', 3, 4);
				}
				if (!$uinfo['show_tel']) {
				    $v['tel'] = lang('无权限');
				}
				if (!$uinfo['show_num']) {
				    $v['childs'] = lang('无权限');
				}
				if (!$uinfo['show_cz']) {
				    $v['chongzhi'] = lang('无权限');
				}
				if (!$uinfo['show_tx']) {
				    $v['tixian'] = lang('无权限');
				}
				
				
				?>
				<div class="p_list-item">
					<img src="{$v.headpic}" onerror="this.src='/public/img/head.png'" class="p_list-item-avatar">
					<div class="p_list-item-name-wrapper">
						<div class="p_list-item-name-title">{:lang('姓名')}</div>
						<div class="p_list-item-name-text">{$v.username}</div>
					</div>
					<div class="p_list-item-time">
						<div>{:lang('注册时间')}</div>
						<div>{$v.addtime|date="Y-m-d H:i:s"}</div>
					</div>
					<div class="p_list-item-divide"></div>
					<div class="p_list-item-paragraph">
						<div class="p_list-item-paragraph-left">{:lang('电话')}</div>
						<div class="p_list-item-paragraph-right">{$v['tel']}</div>
					</div>
					<div class="p_list-item-paragraph">
						<div class="p_list-item-paragraph-left">{:lang('提现')}</div>
						<div class="p_list-item-paragraph-right">{$v['tixian']}</div>
					</div>
					<div class="p_list-item-paragraph">
						<div class="p_list-item-paragraph-left">{:lang('推荐人数')}</div>
						<div class="p_list-item-paragraph-right">{$v['childs']}</div>
					</div>
					<div class="p_list-item-paragraph">
						<div class="p_list-item-paragraph-left">{:lang('充值')}</div>
						<div class="p_list-item-paragraph-right red">{$v.chongzhi}</div>
					</div>
				</div>
				{/volist}
				{else\}
				
				{/if}
				
				{empty name='list'}<span class="notdata">{:lang('没有记录哦')}</span>{else}{$pagehtml|raw|default=''}{/empty}
			</div>
			<!-- <div data-v-b7e8b406="" class="no_more" style="">{:lang('没有数据')}</div> -->
		</div>
		
				<!-- 搜索 -->
		<div class="p_search-wrapper">
			<div class="p_search-title">{:lang('Search date')}</div>
			<form   action="" method="get">
			<input type="hidden" name="lang" value="<?php echo cookie('lang') ?: cookie('think_lang') ?: 'en-us'; ?>">
			<input type="hidden" name="level" value="{$level}">
			<div class="p_search">
				<div class="p_search-input-wrapper">
					<input type="text" name="start" id="start" value="{$start}" >
				</div>
				<div class="p_search-text">{:lang('至')}</div>
				<div class="p_search-input-wrapper">
					<input type="text" name="end" id="end" value="{$end}" >
				</div>
				<!-- <div data-v-b7e8b406="" class="mint-popup mint-datetime mint-popup-bottom"
				     style="z-index: 2003; display: none;">
				    <div class="picker mint-datetime-picker">
				        <div class="picker-toolbar"><span class="mint-datetime-action mint-datetime-cancel">{:lang('取消')}</span> <span
				                class="mint-datetime-action mint-datetime-confirm">{:lang('确定')}</span></div>
				        <div class="picker-items">
		
				        </div>
				    </div>
				</div> -->
				<button class="p_search-btn">{:lang('搜索')}</button>
			</div>
			</form>
		</div>
		
		<script>
		    $('.pagination li').click(function () {
		        var class2= $(this).attr('class');
		        if( class2 == 'active' || class2 == 'disabled' ) {
		
		        }else{
		            var url = $(this).find('a').attr('href');
		            window.location.href = url;
		        }
		    })
		    $(function () {
		        $('.pagination-container select').attr('disabled','disabled');
		        $('.pagination-container select').attr('readonly','readonly');
		
		        // 主题
		        new rolldate.Date({
		            el:'#start',
		            format:'YYYY-MM-DD',
		            beginYear:2000,
		            endYear:2100,
		            theme:'blue'
		        })
		
		
		        // 主题
		        new rolldate.Date({
		            el:'#end',
		            format:'YYYY-MM-DD',
		            beginYear:2000,
		            endYear:2100,
		            theme:'blue'
		        })
		    })
		</script>
	</body>
</html>
