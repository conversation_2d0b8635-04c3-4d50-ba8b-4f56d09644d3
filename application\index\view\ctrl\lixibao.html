<!DOCTYPE html>
<html lang="{:cookie('think_var') ?: 'zh-cn'}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <title>{:lang('余额宝')}</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="/p_static1/css/base.css">
    <link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
    <link rel="stylesheet" href="/static_new/css/public.css">
    
    <!-- 脚本文件 -->
    <script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
    <script charset="utf-8" src="/static_new/js/jquery.cookie.js"></script>
    <script charset="utf-8" src="/static_new/js/cookie-helper.js"></script>
    <script charset="utf-8" src="/static_new/js/dialog.min.js"></script>
    <script charset="utf-8" src="/static_new/js/common.js"></script>

    <style>
        /* 全局样式重置 */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding-top: 0;
        }

        /* 主容器 */
        .main-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 顶部导航 */
        .top-header {
            position: relative;
            padding: 1rem 1.5rem;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .header-nav {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .back-btn {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 2rem;
            height: 2rem;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: white;
            font-size: 1.2rem;
        }

        .page-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            text-align: center;
        }

        .menu-btn {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 2rem;
            height: 2rem;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .menu-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .menu-btn span {
            width: 0.25rem;
            height: 0.25rem;
            background-color: white;
            border-radius: 50%;
            margin: 0.1rem 0;
        }

        /* 余额卡片 */
        .balance-card {
            margin: 1.5rem;
            padding: 2rem 1.5rem;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .balance-title {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .balance-amount {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .balance-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-top: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 0.5rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .stat-value {
            font-size: 0.9rem;
            font-weight: 600;
            color: #333;
        }

        /* 投资区域 */
        .investment-section {
            margin: 1.5rem;
            padding: 1.5rem;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
            padding-left: 0.75rem;
            position: relative;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0.25rem;
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 0.125rem;
        }

        /* 输入区域 */
        .input-group {
            margin-bottom: 1.5rem;
        }

        .input-wrapper {
            position: relative;
            background: #f8f9fa;
            border-radius: 0.75rem;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .input-wrapper:focus-within {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .amount-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: none;
            background: transparent;
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            outline: none;
        }

        .amount-input::placeholder {
            color: #999;
            font-weight: normal;
        }

        .currency-symbol {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2rem;
            font-weight: 700;
            color: #667eea;
        }

        /* 产品选择 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .product-card {
            padding: 1rem;
            background: #f8f9fa;
            border: 2px solid transparent;
            border-radius: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .product-card:hover {
            background: #e9ecef;
        }

        .product-card.active {
            background: white;
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .product-card.active::after {
            content: '✓';
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 1.5rem;
            height: 1.5rem;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .product-name {
            font-size: 0.9rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .product-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #666;
        }

        .product-rate {
            color: #28a745;
            font-weight: 600;
        }

        /* 预期收益 */
        .expected-income {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1rem;
            border-radius: 0.75rem;
            margin: 1rem 0;
            text-align: center;
        }

        .expected-income-label {
            font-size: 0.8rem;
            opacity: 0.9;
            margin-bottom: 0.25rem;
        }

        .expected-income-value {
            font-size: 1.2rem;
            font-weight: 700;
        }

        /* 按钮 */
        .btn-group {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .btn {
            flex: 1;
            padding: 1rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        /* 菜单弹窗 */
        .menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .menu-popup {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background: white;
            border-radius: 1.5rem 1.5rem 0 0;
            padding: 1.5rem;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .menu-popup.show {
            transform: translateY(0);
        }

        .menu-header {
            text-align: center;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
            margin-bottom: 1rem;
        }

        .menu-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
        }

        .menu-items {
            display: grid;
            gap: 0.75rem;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .menu-item:hover {
            background: #e9ecef;
            transform: translateX(0.25rem);
        }

        .menu-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-weight: bold;
            font-size: 1rem;
        }

        .menu-text {
            font-size: 0.9rem;
            font-weight: 500;
            color: #333;
        }

        /* 详情页面 */
        .detail-page {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 2000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .detail-page.show {
            transform: translateX(0);
        }

        .detail-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            color: white;
        }

        .detail-back-btn {
            width: 2rem;
            height: 2rem;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            cursor: pointer;
            font-size: 1.2rem;
        }

        .detail-title {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .detail-content {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
        }

        /* 月份选择器 */
        .month-filter {
            margin-bottom: 1.5rem;
        }

        .month-selector {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 0.5rem;
            background: white;
            font-size: 0.9rem;
            color: #333;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .month-selector:focus {
            border-color: #667eea;
        }

        /* 折叠列表 */
        .collapsible-list {
            margin-bottom: 1rem;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .collapsible-header {
            background: #f8f9fa;
            padding: 1rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .collapsible-header:hover {
            background: #e9ecef;
        }

        .collapsible-header.active {
            background: #667eea;
            color: white;
        }

        .collapsible-title {
            font-weight: 600;
        }

        .collapsible-arrow {
            width: 0;
            height: 0;
            border-left: 0.25rem solid transparent;
            border-right: 0.25rem solid transparent;
            border-top: 0.25rem solid currentColor;
            transition: transform 0.3s ease;
        }

        .collapsible-header.active .collapsible-arrow {
            transform: rotate(180deg);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            background: white;
            transition: max-height 0.3s ease;
        }

        .collapsible-content.show {
            max-height: 1000px;
        }

        /* 记录项 */
        .record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-left {
            flex: 1;
        }

        .record-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .record-time {
            font-size: 0.8rem;
            color: #666;
        }

        .record-amount {
            font-weight: 700;
            color: #28a745;
        }

        .record-amount.negative {
            color: #dc3545;
        }

        /* 统计表格 */
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5rem;
            background: white;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .stats-table th {
            background: #f8f9fa;
            padding: 0.75rem;
            text-align: left;
            font-weight: 600;
            color: #333;
            font-size: 0.8rem;
        }

        .stats-table td {
            padding: 0.75rem;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.8rem;
        }

        .stats-table tr:last-child td {
            border-bottom: none;
        }

        /* 规则部分 */
        .rules-section {
            background: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .rules-section h4 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .rules-section p {
            margin-bottom: 0.5rem;
            font-size: 0.85rem;
            color: #666;
            line-height: 1.5;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 2rem;
            color: #999;
        }

        .loading::before {
            content: '';
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .balance-card {
                margin: 1rem;
                padding: 1.5rem 1rem;
            }

            .investment-section {
                margin: 1rem;
                padding: 1rem;
            }

            .product-grid {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
            }

            .balance-stats {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</head>

<body>
    <div class="main-container">
        <!-- 顶部导航 -->
        <div class="top-header">
            <div class="header-nav">
                <a href="/index/my/index" class="back-btn">‹</a>
                <div class="page-title">{:lang('余额宝')}</div>
                <div class="menu-btn" onclick="openMenuPopup()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>

        <!-- 余额卡片 -->
        <div class="balance-card">
            <div class="balance-title">{:lang('利息宝余额')} (USDT)</div>
            <div class="balance-amount">{$balance_total|number_format=2}</div>
            
            <div class="balance-stats">
                <div class="stat-item">
                    <div class="stat-label">{:lang('昨日收益')}</div>
                    <div class="stat-value">+{$yes_shouyi}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">{:lang('累计收益')}</div>
                    <div class="stat-value">+{$balance_shouru}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">{:lang('年化收益率')}</div>
                    <div class="stat-value">{$rililv}</div>
                </div>
            </div>
        </div>

        <!-- 投资区域 -->
        <div class="investment-section">
            <div class="section-title">{:lang('投资理财')}</div>
            
            <div class="input-group">
                <div class="input-wrapper">
                    <div class="currency-symbol">$</div>
                    <input type="number" 
                           id="price" 
                           name="price" 
                           class="amount-input" 
                           placeholder="{:lang('请输入投资金额')}"
                           min="1"
                           step="0.01">
                </div>
            </div>

            <div class="product-grid p_type-list" data-id="">
                {volist name="lixibao" id="v"}
                <div class="product-card p_type-list-item" data-id="{$v.id}">
                    <div class="product-name">{$v.name}</div>
                    <div class="product-details">
                        <span>{$v.day} {:lang('天')}</span>
                        <span class="product-rate">{$v.bili*100}%</span>
                        <span>{:lang('起投')} {$v.min_num}</span>
                    </div>
                </div>
                {/volist}
            </div>

            <div class="expected-income">
                <div class="expected-income-label">{:lang('预期收益')}</div>
                <div class="expected-income-value" id="yjsy">0.00 USDT</div>
            </div>

            <div class="btn-group">
                <button class="btn btn-primary save-btn">{:lang('立即投资')}</button>
                <button class="btn btn-secondary auto">{:lang('全部取出')}</button>
            </div>
        </div>
    </div>

    <!-- 菜单弹窗 -->
    <div class="menu-overlay" id="menuOverlay" onclick="closeMenuPopup()">
        <div class="menu-popup" id="menuPopup" onclick="event.stopPropagation()">
            <div class="menu-header">
                <div class="menu-title">{:lang('更多功能')}</div>
            </div>
            <div class="menu-items">
                <div class="menu-item" onclick="openFundsDetail()">
                    <div class="menu-icon">资</div>
                    <div class="menu-text">{:lang('资金明细')}</div>
                </div>
                <div class="menu-item" onclick="openReportDetail()">
                    <div class="menu-icon">收</div>
                    <div class="menu-text">{:lang('收益报表')}</div>
                </div>
                <div class="menu-item" onclick="openRulesDetail()">
                    <div class="menu-icon">规</div>
                    <div class="menu-text">{:lang('利息宝规则')}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 资金明细页面 -->
    <div class="detail-page" id="fundsDetailPage">
        <div class="detail-header">
            <div class="detail-back-btn" onclick="closeFundsDetail()">‹</div>
            <div class="detail-title">{:lang('资金明细')}</div>
        </div>
        <div class="detail-content">
            <div class="month-filter" id="monthFilter">
                <!-- 月份选择器将通过JS生成 -->
            </div>
            <div id="fundsDetailContent">
                <!-- 资金明细内容将通过AJAX加载 -->
            </div>
        </div>
    </div>

    <!-- 收益报表页面 -->
    <div class="detail-page" id="reportDetailPage">
        <div class="detail-header">
            <div class="detail-back-btn" onclick="closeReportDetail()">‹</div>
            <div class="detail-title">{:lang('收益报表')}</div>
        </div>
        <div class="detail-content">
            <div id="reportDetailContent">
                <!-- 收益报表内容将通过AJAX加载 -->
            </div>
        </div>
    </div>

    <!-- 利息宝规则页面 -->
    <div class="detail-page" id="rulesDetailPage">
        <div class="detail-header">
            <div class="detail-back-btn" onclick="closeRulesDetail()">‹</div>
            <div class="detail-title">{:lang('利息宝规则')}</div>
        </div>
        <div class="detail-content">
            <div id="rulesDetailContent">
                <!-- 利息宝规则内容将通过AJAX加载 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        var cid = '';
        var currentLang = cookie('think_var') || 'zh-cn';
        
        // 多语言文本
        var langTexts = {
            'zh-cn': {
                loading: '正在加载中...',
                noData: '暂无数据',
                networkError: '网络错误',
                selectMonth: '选择月份',
                year: '年',
                month: '月',
                day: '天',
                confirm: '确定',
                cancel: '取消',
                tips: '提醒',
                withdrawAll: '是否要取出所有利息宝余额?',
                amountError: '存入金额不符合系统要求',
                success: '操作成功'
            },
            'en': {
                loading: 'Loading...',
                noData: 'No data',
                networkError: 'Network error',
                selectMonth: 'Select month',
                year: '',
                month: '',
                day: 'days',
                confirm: 'Confirm',
                cancel: 'Cancel',
                tips: 'Tips',
                withdrawAll: 'Withdraw all balance?',
                amountError: 'Invalid amount',
                success: 'Success'
            }
        };
        
        var texts = langTexts[currentLang] || langTexts['zh-cn'];

        // 初始化
        $(function() {
            $('.p_type-list-item').eq(0).trigger("click");
        });

        // 产品选择
        $('.p_type-list-item').click(function() {
            cid = $(this).data('id');
            $('.p_type-list').attr('data-id', cid);
            $('.p_type-list-item').removeClass('active');
            $(this).addClass('active');
            calculateExpectedIncome();
        });

        // 计算预期收益
        function calculateExpectedIncome() {
            var price = $("#price").val();
            if (price <= 0) return false;
            
            $.ajax({
                url: '/index/ctrl/deposityj',
                data: {price: price, cid: cid},
                type: 'POST',
                success: function(data) {
                    $("#yjsy").text(data.data + ' USDT');
                },
                error: function() {
                    // 静默处理错误，不显示控制台信息
                }
            });
        }

        // 输入金额变化时计算收益
        $("#price").on('input', function() {
            calculateExpectedIncome();
        });

        // 验证输入
        function validateInput() {
            if ($("input[name=price]").val() == '') {
                $(document).dialog({infoText: texts.amountError});
                return false;
            }
            return true;
        }

        // 全部取出
        $('.auto').click(function() {
            $(document).dialog({
                type: 'confirm',
                titleText: "<h2>" + texts.tips + "</h2><br>" + texts.withdrawAll,
                autoClose: 0,
                buttonTextConfirm: texts.confirm,
                buttonTextCancel: texts.cancel,
                onClickConfirmBtn: function() {
                    $.post("{:url('lixibao_chu')}", {type: 1}, function(data) {
                        if (data.code == 0) {
                            window.location.href = "{:url('lixibao')}";
                        } else {
                            $(document).dialog({infoText: data.info});
                        }
                    });
                }
            });
        });

        // 投资按钮
        $(".save-btn").click(function() {
            if (validateInput()) {
                var loading = null;
                var price = $("input[name=price]").val();
                
                $.ajax({
                    url: "{:url('lixibao_ru')}",
                    data: {price: price, cid: cid},
                    type: 'POST',
                    beforeSend: function() {
                        loading = $(document).dialog({
                            type: 'notice',
                            infoIcon: '/static_new/img/loading.gif',
                            infoText: texts.loading,
                            autoClose: 0
                        });
                    },
                    success: function(data) {
                        if (data.code == 0) {
                            $(document).dialog({infoText: data.info});
                            setTimeout(function() {
                                loading.close();
                                window.location.href = '/index/ctrl/lixibao';
                            }, 2000);
                        } else {
                            $(document).dialog({infoText: data.info});
                            loading.close();
                        }
                    },
                    error: function() {
                        $(document).dialog({infoText: texts.networkError});
                        loading.close();
                    }
                });
            }
        });

        // 菜单相关函数
        function openMenuPopup() {
            document.getElementById('menuOverlay').style.display = 'block';
            setTimeout(() => {
                document.getElementById('menuPopup').classList.add('show');
            }, 10);
        }

        function closeMenuPopup() {
            document.getElementById('menuPopup').classList.remove('show');
            setTimeout(() => {
                document.getElementById('menuOverlay').style.display = 'none';
            }, 300);
        }

        // 资金明细相关函数
        function openFundsDetail() {
            closeMenuPopup();
            document.getElementById('fundsDetailPage').classList.add('show');
            generateMonthFilter();
            loadFundsDetail();
        }

        function closeFundsDetail() {
            document.getElementById('fundsDetailPage').classList.remove('show');
        }

        function generateMonthFilter() {
            const monthFilter = document.getElementById('monthFilter');
            const currentDate = new Date();
            let optionsHtml = '<option value="">' + texts.selectMonth + '</option>';
            
            for (let i = 0; i < 12; i++) {
                const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
                const year = date.getFullYear();
                const month = date.getMonth() + 1;
                const monthStr = year + '-' + (month < 10 ? '0' + month : month);
                const selected = i === 0 ? ' selected' : '';
                
                const displayText = currentLang === 'zh-cn' ? 
                    `${year}年${month}月` : 
                    `${date.toLocaleString('en', {month: 'short'})} ${year}`;
                
                optionsHtml += `<option value="${monthStr}"${selected}>${displayText}</option>`;
            }
            
            monthFilter.innerHTML = `
                <select class="month-selector" onchange="filterByMonth(this.value)">
                    ${optionsHtml}
                </select>
            `;
        }

        function filterByMonth(month) {
            loadFundsDetail(month);
        }

        function loadFundsDetail(month = '') {
            const content = document.getElementById('fundsDetailContent');
            content.innerHTML = '<div class="loading">' + texts.loading + '</div>';
            
            $.ajax({
                url: '{:url("index/ctrl/get_funds_detail")}',
                type: 'POST',
                data: {month: month},
                dataType: 'json',
                timeout: 10000,
                success: function(data) {
                    if (data && data.code === 0) {
                        renderFundsDetail(data.data);
                    } else {
                        content.innerHTML = '<div class="loading">' + (data ? data.info : texts.networkError) + '</div>';
                    }
                },
                error: function() {
                    content.innerHTML = '<div class="loading">' + texts.networkError + '</div>';
                }
            });
        }

        function renderFundsDetail(data) {
            let html = '';
            
            if (data.length === 0) {
                html = '<div class="loading">' + texts.noData + '</div>';
            } else {
                const groupedData = {};
                data.forEach(item => {
                    const monthKey = item.month;
                    if (!groupedData[monthKey]) {
                        groupedData[monthKey] = [];
                    }
                    groupedData[monthKey].push(item);
                });
                
                Object.keys(groupedData).forEach((month, index) => {
                    const items = groupedData[month];
                    const isExpanded = index === 0;
                    
                    html += `
                        <div class="collapsible-list">
                            <div class="collapsible-header ${isExpanded ? 'active' : ''}" onclick="toggleCollapsible(this)">
                                <div class="collapsible-title">${month}</div>
                                <div class="collapsible-arrow"></div>
                            </div>
                            <div class="collapsible-content ${isExpanded ? 'show' : ''}">
                    `;
                    
                    items.forEach(item => {
                        html += `
                            <div class="record-item">
                                <div class="record-left">
                                    <div class="record-title">${item.product_name}</div>
                                    <div class="record-time">${item.addtime_fmt}</div>
                                </div>
                                <div class="record-amount ${item.type_prefix === '-' ? 'negative' : ''}">${item.type_prefix}${item.num}</div>
                            </div>
                        `;
                    });
                    
                    html += `
                            </div>
                        </div>
                    `;
                });
            }
            
            document.getElementById('fundsDetailContent').innerHTML = html;
        }

        function toggleCollapsible(header) {
            const content = header.nextElementSibling;
            const isActive = header.classList.contains('active');
            
            if (isActive) {
                header.classList.remove('active');
                content.classList.remove('show');
            } else {
                header.classList.add('active');
                content.classList.add('show');
            }
        }

        // 收益报表相关函数
        function openReportDetail() {
            closeMenuPopup();
            document.getElementById('reportDetailPage').classList.add('show');
            loadReportDetail();
        }

        function closeReportDetail() {
            document.getElementById('reportDetailPage').classList.remove('show');
        }

        function loadReportDetail() {
            const content = document.getElementById('reportDetailContent');
            content.innerHTML = '<div class="loading">' + texts.loading + '</div>';
            
            $.ajax({
                url: '{:url("index/ctrl/get_report_detail")}',
                type: 'POST',
                dataType: 'json',
                timeout: 10000,
                success: function(data) {
                    if (data && data.code === 0) {
                        renderReportDetail(data.data);
                    } else {
                        content.innerHTML = '<div class="loading">' + (data ? data.info : texts.networkError) + '</div>';
                    }
                },
                error: function() {
                    content.innerHTML = '<div class="loading">' + texts.networkError + '</div>';
                }
            });
        }

        function renderReportDetail(data) {
            let tableHeaders = '';
            let statsLabels = {};
            
            if (currentLang === 'zh-cn') {
                tableHeaders = '<th>日期</th><th>投资产品</th><th>投资金额</th><th>收益</th><th>状态</th>';
                statsLabels = {
                    startDate: '投资开始时间',
                    totalInvestment: '累计投资金额',
                    totalIncome: '累计收益',
                    avgDailyIncome: '平均日收益',
                    investmentDays: '投资天数'
                };
            } else {
                tableHeaders = '<th>Date</th><th>Product</th><th>Amount</th><th>Income</th><th>Status</th>';
                statsLabels = {
                    startDate: 'Start Date',
                    totalInvestment: 'Total Investment',
                    totalIncome: 'Total Income',
                    avgDailyIncome: 'Avg Daily Income',
                    investmentDays: 'Investment Days'
                };
            }
            
            let html = `
                <table class="stats-table">
                    <thead>
                        <tr>${tableHeaders}</tr>
                    </thead>
                    <tbody>
            `;
            
            if (data.records && data.records.length > 0) {
                data.records.forEach(record => {
                    html += `
                        <tr>
                            <td>${record.date}</td>
                            <td>${record.product_name}</td>
                            <td>${record.amount}</td>
                            <td>${record.income}</td>
                            <td>${record.status_text}</td>
                        </tr>
                    `;
                });
            } else {
                html += `
                    <tr>
                        <td colspan="5" style="text-align: center; color: #999;">${texts.noData}</td>
                    </tr>
                `;
            }
            
            html += `
                    </tbody>
                </table>
                
                <div class="rules-section">
                    <h4>${currentLang === 'zh-cn' ? '统计信息' : 'Statistics'}</h4>
                    <p>${statsLabels.startDate}: ${data.start_date || texts.noData}</p>
                    <p>${statsLabels.totalInvestment}: ${data.total_investment || '0.00'} USDT</p>
                    <p>${statsLabels.totalIncome}: ${data.total_income || '0.00'} USDT</p>
                    <p>${statsLabels.avgDailyIncome}: ${data.avg_daily_income || '0.00'} USDT</p>
                    <p>${statsLabels.investmentDays}: ${data.investment_days || '0'} ${texts.day}</p>
                </div>
            `;
            
            document.getElementById('reportDetailContent').innerHTML = html;
        }

        // 利息宝规则相关函数
        function openRulesDetail() {
            closeMenuPopup();
            document.getElementById('rulesDetailPage').classList.add('show');
            loadRulesDetail();
        }

        function closeRulesDetail() {
            document.getElementById('rulesDetailPage').classList.remove('show');
        }

        function loadRulesDetail() {
            const content = document.getElementById('rulesDetailContent');
            content.innerHTML = '<div class="loading">' + texts.loading + '</div>';
            
            $.ajax({
                url: '{:url("index/ctrl/get_rules_detail")}',
                type: 'POST',
                dataType: 'json',
                timeout: 10000,
                success: function(data) {
                    if (data && data.code === 0) {
                        renderRulesDetail(data.data);
                    } else {
                        content.innerHTML = '<div class="loading">' + (data ? data.info : texts.networkError) + '</div>';
                    }
                },
                error: function() {
                    content.innerHTML = '<div class="loading">' + texts.networkError + '</div>';
                }
            });
        }

        function renderRulesDetail(data) {
            let html = '';
            
            if (data.products && data.products.length > 0) {
                data.products.forEach(product => {
                    const labels = currentLang === 'zh-cn' ? {
                        annualRate: '年化收益率',
                        investmentPeriod: '投资周期',
                        minAmount: '最低投资金额',
                        calculation: '收益计算方式',
                        dailyIncome: '每日收益',
                        investmentAmount: '投资金额',
                        example: '举例说明',
                        invest: '投资',
                        description: '投资周期结束后，本金和收益会自动返还到您的利息宝余额中'
                    } : {
                        annualRate: 'Annual Rate',
                        investmentPeriod: 'Investment Period',
                        minAmount: 'Minimum Amount',
                        calculation: 'Income Calculation',
                        dailyIncome: 'Daily Income',
                        investmentAmount: 'Investment Amount',
                        example: 'Example',
                        invest: 'Invest',
                        description: 'Principal and income will be automatically returned to your balance after the investment period ends'
                    };
                    
                    html += `
                        <div class="rules-section">
                            <h4>${product.name}</h4>
                            <p><strong>${labels.annualRate}:</strong> ${(product.bili * 100).toFixed(2)}%</p>
                            <p><strong>${labels.investmentPeriod}:</strong> ${product.day} ${texts.day}</p>
                            <p><strong>${labels.minAmount}:</strong> ${product.min_amount || '1.00'} USDT</p>
                            <p><strong>${labels.calculation}:</strong></p>
                            <p>${labels.dailyIncome} = ${labels.investmentAmount} × ${(product.bili * 100).toFixed(2)}% ÷ 365</p>
                            <p><strong>${labels.example}:</strong></p>
                            <p>${labels.invest} 1000 USDT，${labels.dailyIncome} = 1000 × ${(product.bili * 100).toFixed(2)}% ÷ 365 = ${((1000 * product.bili) / 365).toFixed(4)} USDT</p>
                            <p>${labels.description}</p>
                        </div>
                    `;
                });
            }
            
            const ruleLabels = currentLang === 'zh-cn' ? {
                investmentRules: '投资规则',
                riskWarning: '风险提示',
                rules: [
                    '转入利息宝的资金将按照选择的产品开始计息',
                    '收益每日结算，自动加入利息宝余额',
                    '投资期间可随时查看收益明细',
                    '到期后本金自动返还，可选择续投或转出',
                    '转出资金T+1到账'
                ],
                risks: [
                    '投资有风险，收益有波动',
                    '过往收益不代表未来表现',
                    '请根据自身风险承受能力理性投资'
                ]
            } : {
                investmentRules: 'Investment Rules',
                riskWarning: 'Risk Warning',
                rules: [
                    'Funds transferred to Interest Treasure will start earning interest according to the selected product',
                    'Income is settled daily and automatically added to Interest Treasure balance',
                    'You can view income details at any time during investment',
                    'Principal will be automatically returned upon maturity, with options to reinvest or withdraw',
                    'Withdrawn funds arrive within T+1'
                ],
                risks: [
                    'Investment involves risks and income fluctuations',
                    'Past performance does not represent future results',
                    'Please invest rationally according to your risk tolerance'
                ]
            };
            
            html += `
                <div class="rules-section">
                    <h4>${ruleLabels.investmentRules}</h4>
                    ${ruleLabels.rules.map((rule, index) => `<p>${index + 1}. ${rule}</p>`).join('')}
                </div>
                
                <div class="rules-section">
                    <h4>${ruleLabels.riskWarning}</h4>
                    ${ruleLabels.risks.map((risk, index) => `<p>${index + 1}. ${risk}</p>`).join('')}
                </div>
            `;
            
            document.getElementById('rulesDetailContent').innerHTML = html;
        }
    </script>
</body>
</html> 