<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="stylesheet" href="/p_static1/css/base.css">
		<title>{$Think.lang.Rechargerecord}</title>
		
		<link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
		<script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
		<script charset="utf-8" src="/static_new/js/common.js"></script>
		<style type="text/css" title="fading circle style">
		    .circle-color-8 > div::before {
		        background-color: #ccc;
		    }
		    /* 通用分页 */
		    .pagination-container {
		        line-height: 40px;
		        text-align: right;
		    }
		    .pagination-container > span {
		        color: #666;
		        font-size: 9pt;
		    }
		    .pagination-container > ul {
		        float: right;
		        display: inline-block;
		        margin: 0;
		        padding: 0;
		    }
		    .pagination-container > ul > li {
		        z-index: 1;
		        display: inline-block;
		    }
		    .pagination-container > ul > li > a, .pagination-container > ul > li > span {
		        color: #333;
		        width: 33px;
		        height: 30px;
		        border: 1px solid #dcdcdc;
		        display: inline-block;
		        margin-left: -1px;
		        text-align: center;
		        line-height: 28px;
		    }
		    .pagination-container > ul > li > span {
		        background: #dcdcdc;
		        cursor: default;
		    }
		    .van-tab--active span{
		        color: #ff9a2c;
		    }
		    .circle-color-23 > div::before {
		        background-color: #ccc;
		    }
		    .notdata{
		        display: block;
		        text-align: center;
		        padding: 30px;
		    }
		</style>
		
		<style>
			body {
				padding-top: 3.1rem;
				background-color: rgba(245, 245, 247, 1);
			}
			/* 导航栏 */
			.p_nav {
				box-sizing: border-box;
				position: fixed;
				top: 0;
				left: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 3.1rem;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
				background-color: #fff;
				border-bottom: 0.05rem solid rgba(207, 209, 230, 1);
				z-index: 99;
			}
			.p_nav-arrow {
				position: absolute;
				left: 0.75rem;
				top: 50%;
				width: 1.65rem;
				height: 1.65rem;
				transform: translate(0, -50%);
			}
			/* 内容列表 */
			.p_list {
				margin: 0.75rem 0.75rem 0;
			}
			.p_list-item {
				box-sizing: border-box;
				padding: 0.825rem 0.75rem 0;
				margin-bottom: 0.5rem;
				height: 5.5rem;
				background-color: #fff;
				border-radius: 0.5rem;
			}
			.p_list-item-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.p_list-item-header-left {
				font-size: 0.9rem;
				line-height: 0.9rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_list-item-header-right {
				font-size: 1.05rem;
				line-height: 1.05rem;
				font-weight: 700;
				color: rgba(36, 44, 107, 1);
			}
			.p_list-item-text {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 0.75rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(119, 123, 158, 1);
			}
			.red {
				color: rgba(255, 112, 112, 1);
			}
			/* 悬浮礼物 */
			.p_gift {
				position: fixed;
				right: 0;
				bottom: 20%;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 5.95rem;
				height: 5.95rem;
				z-index: 99;
			}
			.p_gift img {
				width: 100%;
				height: 100%;
			}
			.p_pop-up-wrapper {
				position: fixed;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, .3);
				z-index: 1000;
			}
			/* 邀请好友弹窗 */
			#invite-popup {
				display: none;
			}
			.p_invite {
				box-sizing: border-box;
				position: absolute;
				top: 20%;
				left: 50%;
				padding-top: 2.2rem;
				width: 12.8rem;
				height: 15rem;
				background: url(../p_static1/img/index_alert-img2.png) no-repeat;
				background-size: 100% 100%;
				transform: translate(-50%, 0);
			}
			.p_invite-title {
				font-size: 0.7rem;
				line-height: 0.8rem;
				font-weight: bold;
				color: rgba(179, 126, 84, 1);
				text-align: center;
			}
			.p_invite-subtitle {
				margin: 0.875rem 2.05rem 0 2.75rem;
				font-size: 0.6rem;
				line-height: 0.9rem;
				font-weight: bold;
				color: rgba(179, 126, 84, 1);
			}
			.p_invite-btn {
				position: absolute;
				left: 2.875rem;
				bottom: 1.6rem;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 7.75rem;
				height: 2.35rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				font-weight: bold;
				color: rgba(179, 126, 84, 1);
				background: url(../p_static1/img/index_alert-img3.png) no-repeat;
				background-size: 100% 100%;
			}
			
		</style>
	</head>
	<body>
		<!-- 导航栏 -->
		<div class="p_nav">
			<img src="/p_static1/img/arrowleft_circle_blue.png" class="p_nav-arrow" onclick="window.history.go(-1);">
			<div>{$Think.lang.Rechargerecord}</div>
		</div>
		
		<!-- 内容列表 -->
		<div class="p_list">
			{volist name='list' id='v'}
			<div class="p_list-item">
				<div class="p_list-item-header">
					<div class="p_list-item-header-left">{$Think.lang.Rechargeamount}</div>
					<div class="p_list-item-header-right">{$v.num}</div>
				</div>
				<div class="p_list-item-text">
					<div>{$Think.lang.Ordernumber}</div>
					<div>{$v.id}</div>
				</div>
				<div class="p_list-item-text">
					<div>{$Think.lang.Rechargestatus}</div>
					<div>
						{switch $v.status}
						{case 1}<font color="#ff7070">{$Think.lang.Pending}</font>{/case}
						{case 2}<font color="#777b9e">{$Think.lang.examinationpassed}</font>{/case}
						{case 3}<font color="#ff7070">{$Think.lang.Auditfailure}</font>{if $v.remark}({$v.remark}){/if}{/case}
						{/switch}
					</div>
				</div>
			</div>
			{/volist}
			
			{empty name='list'}
			<span class="notdata">{$Think.lang.Thereisnorecordonispage}</span>
			{else/}
			{$pagehtml|raw|default=''}
			{/empty}
		</div>
		
		<script>
		    $(function () {
		        $('.pagination-container select').attr('disabled','disabled');
		    })
		</script>
	</body>
</html>
