<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="stylesheet" href="/p_static1/css/base.css">
		<title>{$Think.lang.Accountdetails}</title>
		
		<link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
		<script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
		<script charset="utf-8" src="/static_new/js/common.js"></script>
		
		<script charset="utf-8" src="/static_new6/js/rolldate.js"></script>
		<link rel="stylesheet" href="/static_new6/css/rolldate.css">
		<style type="text/css" title="fading circle style">
		    .circle-color-8 > div::before {
		        background-color: #ccc;
		    }
		    /* 通用分页 */
		    .pagination-container {
		        line-height: 40px;
		        text-align: right;
		    }
		    .pagination-container > span {
		        color: #666;
		        font-size: 9pt;
		    }
		    .pagination-container > ul {
		        float: right;
		        display: inline-block;
		        margin: 0;
		        padding: 0;
		    }
		    .pagination-container > ul > li {
		        z-index: 1;
		        display: inline-block;
		    }
		    .pagination-container > ul > li > a, .pagination-container > ul > li > span {
		        color: #333;
		        width: 33px;
		        height: 30px;
		        border: 1px solid #dcdcdc;
		        display: inline-block;
		        margin-left: -1px;
		        text-align: center;
		        line-height: 28px;
		    }
		    .pagination-container > ul > li > span {
		        background: #dcdcdc;
		        cursor: default;
		    }
		    .van-tab--active span{
		        color: #ff9a2c;
		    }
		    .circle-color-23 > div::before {
		        background-color: #ccc;
		    }
		    .notdata{
		        display: block;
		        text-align: center;
		        padding: 30px;
		    }
		</style>
		<style>
			body {
				padding-top: 3.1rem;
				padding-bottom: 6.5rem;
				background-color: rgba(245, 245, 247, 1);
			}
			/* 导航栏 */
			.p_nav {
				box-sizing: border-box;
				position: fixed;
				top: 0;
				left: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 3.1rem;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
				background-color: #fff;
				border-bottom: 0.05rem solid rgba(207, 209, 230, 1);
				z-index: 99;
			}
			.p_nav-arrow {
				position: absolute;
				left: 0.75rem;
				top: 50%;
				width: 1.65rem;
				height: 1.65rem;
				transform: translate(0, -50%);
			}
			/* 导航标签 */
			.p_tabs {
				display: flex;
				padding: 0.75rem 0.75rem 1.05rem;
				background-color: #fff;
			}
			.p_tab-item {
				position: relative;
				display: flex;
				align-items: center;
				margin-right: 1.5rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				font-weight: 600;
				color: rgba(119, 123, 158, 1);
			}
			.p_tab-item.active {
				color: rgba(36, 44, 107, 1);
			}
			.p_tab-item.active::after {
				display: block;
				content: '';
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translate(-50%, 300%) rotate(45deg);
				width: 0.2rem;
				height: 0.2rem;
				background-color: rgba(36, 44, 107, 1);
				border-radius: 0.05rem;
			}
			/* 页面 */
			.p_list {
				margin: 0.75rem 0.75rem 0;
			}
			.p_list-item {
				box-sizing: border-box;
				position: relative;
				padding: 0.75rem 0.75rem 0;
				margin-bottom: 0.5rem;
				height: 4rem;
				background-color: rgba(255, 255, 255, 1);
				border-radius: 0.5rem;
			}
			.p_list-item-title {
				font-size: 0.9rem;
				line-height: 0.9rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_list-item-time {
				margin-top: 0.725rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(119, 123, 158, 1);
			}
			.p_list-item-num {
				position: absolute;
				right: 0.75rem;
				bottom: 0.75rem;
				font-size: 1.05rem;
				line-height: 1.05rem;
				font-weight: 700;
				color: rgba(36, 44, 107, 1);
			}
			.red {
				color: rgba(255, 112, 112, 1);
			}
			/* 搜索 */
			.p_search-wrapper {
				box-sizing: border-box;
				position: fixed;
				left: 0;
				bottom: 0;
				width: 100%;
				height: 5rem;
				padding: 1rem 0.75rem 0;
				background-color: #fff;
				border-radius: 0.5rem 0.5rem 0 0;
				box-shadow: 0px 0.05rem 0.5rem rgba(204, 204, 230, 1);
			}
			.p_search-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(36, 44, 107, 1);
			}
			.p_search {
				display: flex;
				align-items: center;
				margin-top: 0.85rem;
			}
			.p_search-input-wrapper {
				box-sizing: border-box;
				display: flex;
				align-items: center;
				width: 5rem;
				padding: 0 0.5rem;
				height: 1.5rem;
				background-color: rgba(237, 239, 247, 1);
				border-radius: 0.25rem;
			}
			.p_search-input-wrapper input {
				width: 100%;
				height: 100%
			}
			.p_search-text {
				margin: 0 0.5rem;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_search-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left: 1.2rem;
				width: 3.875rem;
				height: 1.5rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				font-weight: 600;
				color: rgba(248, 217, 193, 1);
				background-color: rgba(36, 44, 107, 1);
				border-radius: 0.25rem;
			}
			/* 悬浮礼物 */
			.p_gift {
				position: fixed;
				right: 0;
				bottom: 20%;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 5.95rem;
				height: 5.95rem;
				z-index: 99;
			}
			.p_gift img {
				width: 100%;
				height: 100%;
			}
			.p_pop-up-wrapper {
				position: fixed;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, .3);
				z-index: 1000;
			}
			/* 邀请好友弹窗 */
			#invite-popup {
				display: none;
			}
			.p_invite {
				box-sizing: border-box;
				position: absolute;
				top: 20%;
				left: 50%;
				padding-top: 2.2rem;
				width: 12.8rem;
				height: 15rem;
				background: url(/p_static1/img/index_alert-img2.png) no-repeat;
				background-size: 100% 100%;
				transform: translate(-50%, 0);
			}
			.p_invite-title {
				font-size: 0.7rem;
				line-height: 0.8rem;
				font-weight: bold;
				color: rgba(179, 126, 84, 1);
				text-align: center;
			}
			.p_invite-subtitle {
				margin: 0.875rem 2.05rem 0 2.75rem;
				font-size: 0.6rem;
				line-height: 0.9rem;
				font-weight: bold;
				color: rgba(179, 126, 84, 1);
			}
			.p_invite-btn {
				position: absolute;
				left: 2.875rem;
				bottom: 1.6rem;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 7.75rem;
				height: 2.35rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				font-weight: bold;
				color: rgba(179, 126, 84, 1);
				background: url(/p_static1/img/index_alert-img3.png) no-repeat;
				background-size: 100% 100%;
			}
			
			/* 补充样式 */
			.rolldate-btn.rolldate-cancel {
				background-color: rgba(213, 215, 227, 1)!important;
				color: rgba(36, 44, 107, 1)!important;
			}
			.rolldate-btn.rolldate-confirm {
				background: rgba(36, 44, 107, 1)!important;
				color: rgba(248, 217, 193, 1)!important;
			}
			.rolldate-container header {
				background: rgba(248, 217, 193, 1)!important;
				color: rgba(122, 70, 31, 1)!important;
			}
		</style>
	</head>
	<body>
		<!-- 导航栏 -->
		<div class="p_nav">
			<img src="/p_static1/img/arrowleft_circle_blue.png" class="p_nav-arrow" onclick="window.location.href='/index/my/index';">
			<div>{$Think.lang.Accountdetails}</div>
		</div>
		
		<!-- 导航标签 -->
		<div class="p_tabs">
			<a class="p_tab-item <?php echo !$type?'active':'';  ?>" href="/index/my/caiwu">{$Think.lang.Alltypes}</a>
			<a class="p_tab-item <?php echo $type==1?'active':'';  ?>" href="/index/my/caiwu.html?type=1">{$Think.lang.Withdrawalsrecord}</a>
			<a class="p_tab-item <?php echo $type==2?'active':'';  ?>" href="/index/my/caiwu.html?type=2">{$Think.lang.Rechargerecord}</a>
		</div>
		
		<!-- alltypes页面 -->
		<div class="p_wrapper">
			<div class="p_list">
				{if $list}
				{volist name='list' id='v'}
				<div class="p_list-item">
					<div class="p_list-item-title">
						{switch $v.type}
						{case 1}<font color="#ff7070">{$Think.lang.Userrecharge}</font>{/case}
						{case 2}<font color="#242c6b">{$Think.lang.orderamount}</font>{/case}
						{case 3}<font color="#ff7070">{$Think.lang.Orderrebate}</font>{/case}
						{case 30}<font color="#ff7070">{$Think.lang.Accountupgrade}</font>{/case}
						{case 6}<font color="#0cea5b">{$Think.lang.Lowerlevelrebate}</font>{/case}
						{case 7}<font color="#d814c9">{$Think.lang.Userwithdrawal}</font>{/case}
						{case 8}<font color="#d814c9">{$Think.lang.ManualTopUp}</font>{/case}
						{case 21}<font color="#ff7070">{$Think.lang.Yuin}</font>{/case}
						{case 22}<font color="#ff7070">{$Think.lang.Outtreasure}</font>{/case}
						{case 23}<font color="#ff7070">{$Think.lang.YiBaoincome}</font>{/case}
						{case 87}<font color="#ff7070">{:lang('抽奖')}</font>{/case}
						{/switch}
					</div>
					<div class="p_list-item-time"><?php echo date("Y-m-d H:i:s",$v['addtime']);?></div>
					
					<!-- 金额区域包含状态信息 -->
					<div style="position: absolute; right: 0.75rem; bottom: 0.75rem; text-align: right;">
						<!-- 状态信息显示在金额上方 -->
						{if $v.status_text != ''}
						<div style="color: {$v.status_color}; font-size: 0.55rem; font-weight: 600; margin-bottom: 0.2rem; line-height: 0.55rem;">
							{$v.status_text}
						</div>
						{/if}
						
						<!-- 金额显示 -->
						{if $v.type == 30}
						<div style="font-size: 1.05rem; line-height: 1.05rem; font-weight: 700; color: rgba(36, 44, 107, 1);">-{$v.num}</div>
						{else}
						<div style="font-size: 1.05rem; line-height: 1.05rem; font-weight: 700; color: rgba(255, 112, 112, 1);">+{$v.num}</div>
						{/if}
					</div>
				</div>
				{/volist}
				{else\}
				
				{/if}
				
				{empty name='list'}<span class="notdata">{$Think.lang.Thereisnorecordonispage}</span>{else}{$pagehtml|raw|default=''}{/empty}
			</div>
		</div>
		
		<!-- 搜索 -->
		<div class="p_search-wrapper">
			<div class="p_search-title">Search date</div>
			<form action="" method="get">
			<div class="p_search">
				<div class="p_search-input-wrapper">
					<input type="text" id="start" name="start" value="{$start}">
				</div>
				<div class="p_search-text">{$Think.lang.to}</div>
				<div class="p_search-input-wrapper">
					<input type="text" id="end" name="end" value="{$end}">
				</div>
				<button class="p_search-btn">{$Think.lang.search}</button>
			</div>
			</form>
		</div>
		
		<script>
		    $('.pagination li').click(function () {
		        var class2= $(this).attr('class');
		        if( class2 == 'active' || class2 == 'disabled' ) {
		
		        }else{
		            var url = $(this).find('a').attr('href');
		            window.location.href = url;
		        }
		    })
		    $(function () {
		        $('.pagination-container select').attr('disabled','disabled');
		
		        ///
		        // 主题
		        new rolldate.Date({
		            el:'#start',
		            format:'YYYY-MM-DD',
		            beginYear:2000,
		            endYear:2100,
		            theme:'blue'
		        })
		
		
		        // 主题
		        new rolldate.Date({
		            el:'#end',
		            format:'YYYY-MM-DD',
		            beginYear:2000,
		            endYear:2100,
		            theme:'blue'
		        })
		    });
		
		
		</script>
		
	</body>
</html>
