<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="stylesheet" href="/p_static1/css/base.css">
		<title>my</title>

		<link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
		<link rel="stylesheet" href="/static_new/css/public.css">

		<script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
		<script charset="utf-8" src="/static_new/js/dialog.min.js"></script>

		<script charset="utf-8" src="/static_new/js/common.js"></script>
		<!-- 阿拉伯语修复脚本 -->
		<script>
			// 避免控制台错误输出
			window.onerror = function(message, source, lineno, colno, error) {
				// 忽略特定的错误消息
				if (message && typeof message === 'string' &&
					(message.indexOf('Unchecked runtime.lastError') !== -1 ||
					message.indexOf('The message port closed before a response was received') !== -1)) {
					return true; // 阻止默认处理
				}
				return false; // 允许默认处理
			};

			// 针对阿拉伯语的特殊处理
			document.addEventListener('DOMContentLoaded', function() {
				if (document.cookie.indexOf('lang=arabic') !== -1) {
					// 如果是阿拉伯语，给body添加RTL支持
					document.body.style.direction = 'rtl';
					document.body.classList.add('arabic-lang');
				}
			});
		</script>
		<style>
			body {
				position: relative;
				padding-top: 3rem;
				padding-bottom: 5rem;
			}
			/* 阿拉伯语特殊样式 */
			.arabic-lang .p_asset-btn-wrapper {
				right: auto;
				left: 0.25rem;
			}
			.arabic-lang .p_asset-btn {
				margin-right: 0;
				margin-left: 0.75rem;
			}
			.arabic-lang .p_header-arrow {
				transform: rotate(180deg);
			}
			.arabic-lang .p_list-item-icon {
				left: auto;
				right: 0;
			}
			.arabic-lang .p_list-item {
				padding-left: 0;
				padding-right: 2.15rem;
			}
			.arabic-lang .p_header-info {
				padding-left: 0;
				padding-right: 4.75rem;
			}
			.arabic-lang .p_header-avatar {
				left: auto;
				right: 0;
			}
			.arabic-lang .p_header-icon {
				right: auto;
				left: 0.75rem;
			}
			/* 头部背景图片 */
			.p_header-img {
				box-sizing: border-box;
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 8rem;
				z-index: -10;
			}
			.p_header-icon {
				box-sizing: border-box;
				position: absolute;
				right: 0.75rem;
				top: 0.75rem;
			}
			.p_setting-icon {
				width: 1.65rem;
				height: 1.65rem;
			}
			/* 头部信息 */
			.p_header-info {
				box-sizing: border-box;
				position: relative;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin: 0 1rem;
				padding-left: 4.75rem;
				height: 4rem;
				cursor: pointer;
			}
			.p_header-avatar {
				position: absolute;
				left: 0;
				top: 0;
				width: 4rem;
				height: 4rem;
			}
			.p_header-avatar-img {
				width: 100%;
				height: 100%;
				border-radius: 50%;
			}
			.p_hatcircle-icon {
				position: absolute;
				right: 0.45rem;
				bottom: -0.275rem;
				width: 1rem;
				height: 1rem;
			}
			.p_header-content {}
			.p_username {
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 700;
				color: rgba(36, 44, 107, 1);
			}
			.p_level {
				display: inline-block;
				margin-top: 0.5rem;
				padding: 0.2rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				background-color: #343A73;
				border-radius: 0.2rem;
				color: #fff;
			}
			.p_invitecode {
				margin-top: 0.5rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(159, 161, 179, 1);
			}
			.p_header-arrow {
				width: 0.4rem;
				height: 0.7rem;
			}
			/* 账户余额 */
			.p_asset {
				box-sizing: border-box;
				position: relative;
				margin: 1.5rem 0.625rem 0;
				height: 3.9rem;
				background: url(/p_static1/img/my_bg-1.png) no-repeat;
				background-size: 100% 100%;
			}
			.p_asset-balance {
				position: absolute;
				left: 1.5rem;
				top: 0.75rem;
			}
			.p_asset-balance-title {
				display: flex;
				align-items: center;
				font-size: 0.6rem;
				line-height: 0.6rem;
				color: rgba(248, 217, 193, 1);
			}
			.p_hat-icon {
				margin-right: 0.2rem;
				width: 0.6rem;
				height: 0.5rem;
			}
			.p_asset-balance-num {
				margin-top: 0.55rem;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 700;
				color: rgba(248, 217, 193, 1);
			}
			.p_asset-btn-wrapper {
				position: absolute;
				right: 0.25rem;
				bottom: 0.8rem;
				display: flex;
				align-items: center;
			}
			.p_asset-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 3.5rem;
				height: 1.5rem;
				margin-right: 0.75rem;
				font-size: 0.6rem;
				line-height: 0.6rem;
				font-weight: 600;
				color: rgba(166, 114, 75, 1);
				background-color: rgba(248, 217, 193, 1);
				border-radius: 1.2rem;
			}
			/* 导航列表 */
			.p_list {
				margin: 1.45rem 0.95rem 0 0.5rem;
			}
			.p_list-item {
				position: relative;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-left: 2.15rem;
				margin-bottom: 1rem;
				height: 1.65rem;
			}
			.p_list-item:last-child {
				margin-bottom: 0;
			}
			.p_list-item-icon {
				position: absolute;
				left: 0;
				top: 50%;
				width: 1.65rem;
				height: 1.65rem;
				transform: translate(0, -50%);
			}
			.p_list-item-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(52, 58, 115, 1);
			}
			.p_list-item-arrow {
				width: 0.3rem;
				height: 0.45rem;
			}
			/* 邀请好友和抽奖 */
			.p_other-wrapper {
				margin: 0.5rem 0.75rem 0;
			}
			.p_other {
				display: flex;
				justify-content: space-between;
			}
			.p_other-item {
				box-sizing: border-box;
				width: 48.6%;
				height: 4.75rem;
				padding: 0.75rem 0 0 0.5rem;
				background-repeat: no-repeat;
				background-size: 100% 100%;
			}
			.p_other-item.invite {
				background-image: url(/p_static1/img/my_bg-2.png);
			}
			.p_other-item.turntable {
				background-image: url(/p_static1/img/my_bg-3.png);
			}
			.p_other-item-title {
				font-size: 0.7rem;
				line-height: 0.8rem;
				font-weight: 600;
			}
			.invite .p_other-item-title {
				color: rgba(135, 66, 66, 1);
			}
			.turntable .p_other-item-title {
				color: rgba(90, 102, 201, 1);
			}
			.p_other-item-subtitle {
				margin-top: 0.25rem;
				font-size: 0.5rem;
				line-height: 0.5rem;
			}
			.invite .p_other-item-subtitle {
				color: rgba(191, 130, 130, 1);
			}
			.turntable .p_other-item-subtitle {
				color: rgba(143, 149, 194, 1);
			}
			/* 退出按钮 */
			.p_login-out-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 1rem 0.75rem 0;
				height: 2.3rem;
				font-size: 0.75rem;
				line-height: 0.75rem;
				font-weight: 600;
				color: #fff;
				background-color: #343A73;
				border-radius: 1rem;
			}
			/* 底部导航栏 */
			.p_footer {
				position: fixed;
				left: 0;
				bottom: 0;
				box-sizing: border-box;
				display: flex;
				width: 100%;
				height: 3rem;
				background-color: #fff;
				border: 0.05rem solid rgba(237, 240, 255, 1);
				z-index: 99;
			}
			.p_footer-item {
				position: relative;
				box-sizing: border-box;
				padding: 0.625rem 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 20%;
				height: 100%;
			}
			.p_footer-item-img {
				width: 0.85rem;
				height: 0.85rem;
			}
			.p_footer-item-text {
				margin-top: 0.4rem;
				font-size: 0.5rem;
				line-height: 0.5rem;
				color: rgba(200, 203, 227, 1);
			}
			.p_footer-item-text.active {
				color: rgba(36, 44, 107, 1);
			}
			.p_footer-middle {
				position: absolute;
				left: 50%;
				top: 0;
				width: 4.3rem;
				height: 4.3rem;
				background: url(/p_static1/img/footer_img-middle.svg) no-repeat;
				background-size: 100% 100%;
				transform: translate(-50%, -40%);
			}
			.p_footer-middle-text {
				margin-top: 2.5rem;
				font-size: 0.5rem;
				line-height: 0.5rem;
				text-align: center;
				color: rgba(248, 217, 193, 1);
				transform: scale(.8);
			}
			.score{
			    box-sizing: border-box;
                position: absolute;
                left: 0.75rem;
                top: 0.75rem;
			}
			.score span{
			    font-size: 1rem;
			}
		</style>
	</head>
	<body>
		<!-- 头部背景图片 -->
		<img src="/p_static1/img/my_bg-0.png" class="p_header-img">

		<!-- 添加成功消息提示 -->
		{if condition="session('success_message')"}
		<div style="position: fixed; top: 10px; left: 0; right: 0; z-index: 9999; background-color: rgba(76, 175, 80, 0.9); color: white; text-align: center; padding: 10px; border-radius: 4px; margin: 0 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
			{:session('success_message')}
			{php}session('success_message', null);{/php}
		</div>
		{/if}

		<div class="score">
		     <span>{:lang('信用')}：</span>
		     <span style="color:red">{$info.credit_score}&nbsp;{:lang('分')}</span>
		 </div>
		<div class="p_header-icon">
		    <img class="p_setting-icon" style="float: right;" src="/p_static1/img/my_setting-icon.svg" onclick="window.location.href=`/index/ctrl/set`" >
		    <div class="foo50" style="float: right;margin-right: 10px;">
                    <select class="lang">
                        <option value="en-us" {if $lang=='en-us'}selected{/if}>English</option>
                       <option value="mi" {if $lang=='mi'}selected{/if}>Maori</option>
                       <option value="thai" {if $lang=='thai'}selected{/if}>Thai</option>
                        <option value="baxi" {if $lang=='baxi'}selected{/if}>Brasil</option>

                        <option value="moxige" {if $lang=='moxige'}selected{/if}>México</option>
                        <option value="tuerqi" {if $lang=='tuerqi'}selected{/if}>turkey</option>
                        <option value="arabic" {if $lang=='arabic'}selected{/if}>العربية</option>

                    </select>
                <script>
                     /*语言切换*/
                    $(".lang").change(function(){
                        var lang = $(".lang").val();
                        console.log("选择的语言: " + lang); // 调试日志

                        // 防止重复点击
                        $(this).prop('disabled', true);

                        $.ajax({
                            url:"/index/cutLangss/cutlangs",
                            data:{lang:lang},
                            type:'POST',
                            dataType:'json',
                            success:function(data){
                                console.log("语言切换响应:", data);
                                if(data.code==1){
                                    // 设置本地存储，以便在页面刷新后保持选择
                                    localStorage.setItem('selectedLanguage', lang);
                                    // 延迟刷新页面，确保Cookie已经设置
                                    setTimeout(function() {
                                        window.location.reload();
                                    }, 100);
                                }else{
                                    alert("语言切换失败，请重试");
                                    $(".lang").prop('disabled', false);
                                }
                            },
                            error:function(data){
                                console.error("语言切换错误:", data);
                                alert("语言切换出错，请重试");
                                $(".lang").prop('disabled', false);
                                if(typeof loading !== 'undefined') {
                                    loading.close();
                                }
                            }
                        });
                    });

                    // 页面加载时检查本地存储中的语言设置
                    $(document).ready(function() {
                        var savedLang = localStorage.getItem('selectedLanguage');
                        if(savedLang) {
                            // 如果有保存的语言设置，确保下拉列表显示正确的选项
                            $(".lang").val(savedLang);
                        }
                    });
                </script>
            </div>

		</div>

		<!-- 头部信息 -->
		<div class="p_header-info" onclick="window.location.href='/index/ctrl/set'">
			<div class="p_header-avatar">
				<img class="p_header-avatar-img" src="{:isset($info['headpic']) ? $info['headpic'] : '/public/img/head.png'}" onerror="this.src='/public/img/head.png'" >
				<img class="p_hatcircle-icon" src="/p_static1/img/hat_circle.svg" >
			</div>
			<div class="p_header-content">
				<div class="p_username">{$info.username}</div>
				<div class="p_level">VIP{$info.level|default=0}</div>
				<div class="p_level" style="margin-left:.3rem">{$info.member_level_display|default=lang($info.member_level)}</div>
				<div class="p_invitecode">{$Think.lang.yqm}:{$info.invite_code}</div>
			</div>
			<img src="/p_static1/img/arrowright.png" class="p_header-arrow">
		</div>

		<!-- 账户余额 -->
		<div class="p_asset">
			<div class="p_asset-balance">
				<div class="p_asset-balance-title">
					<img class="p_hat-icon" src="/p_static1/img/hat.svg" >
					<div>{$Think.lang.Account_balance}</div>
				</div>
				<div class="p_asset-balance-num">{$info.balance} USDT</div>
			</div>
			<div class="p_asset-btn-wrapper">
				<div class="p_asset-btn" onclick="window.location.href=`/index/ctrl/recharge`">{:lang('Recarga')}</div>
				<div class="p_asset-btn" onclick="window.location.href=`/index/ctrl/deposit`">{:lang('Retirar')}</div>
			</div>
		</div>

		<!-- 导航列表 -->
		<div class="p_list">
			<a class="p_list-item" href="/index/my/caiwu">
				<img src="/p_static1/img/my_icon-1.svg" class="p_list-item-icon">
				<div class="p_list-item-title">{$Think.lang.Accountdetails}</div>
				<img src="/p_static1/img/arrowright.png" class="p_list-item-arrow">
			</a>
			<a class="p_list-item" href="/index/ctrl/recharge_admin">
				<img src="/p_static1/img/my_icon-2.svg" class="p_list-item-icon">
				<div class="p_list-item-title">{$Think.lang.Rechargerecord}</div>
				<img src="/p_static1/img/arrowright.png" class="p_list-item-arrow">
			</a>
			<a class="p_list-item" href="/index/ctrl/deposit_admin">
				<img src="/p_static1/img/my_icon-3.svg" class="p_list-item-icon">
				<div class="p_list-item-title">{$Think.lang.Withdrawalsrecord}</div>
				<img src="/p_static1/img/arrowright.png" class="p_list-item-arrow">
			</a>
			<a class="p_list-item" href="/index/my/msg">
				<img src="/p_static1/img/my_icon-4.svg" class="p_list-item-icon">
				<div class="p_list-item-title">{$Think.lang.systeminformation}</div>
				<img src="/p_static1/img/arrowright.png" class="p_list-item-arrow">
			</a>
			<a class="p_list-item" href="/index/ctrl/junior">
				<img src="/p_static1/img/my_icon-5.svg" class="p_list-item-icon">
				<div class="p_list-item-title">{$Think.lang.Startorders22}</div>
				<img src="/p_static1/img/arrowright.png" class="p_list-item-arrow">
			</a>
			<a class="p_list-item" href="/index/order/index">
				<img src="/p_static1/img/my_icon-6.svg" class="p_list-item-icon">
				<div class="p_list-item-title">{$Think.lang.Graborderrecord}</div>
				<img src="/p_static1/img/arrowright.png" class="p_list-item-arrow">
			</a>
			<a class="p_list-item" href="/index/ctrl/vip">
				<img src="/p_static1/img/my_icon-5.svg" class="p_list-item-icon">
				<div class="p_list-item-title">{:lang('member_level')}</div>
				<img src="/p_static1/img/arrowright.png" class="p_list-item-arrow">
			</a>
		</div>

		<!-- 邀请好友和抽奖 -->
		<div class="p_other-wrapper">
			<div class="p_other">
				<a class="p_other-item invite" href="/index/my/invite">
					<div class="p_other-item-title">{:lang('Shippingaddress')}</div>
					<div class="p_other-item-subtitle">{:lang('A lot of gifts')}</div>
				</a>
				 <a class="p_other-item turntable" href="/index/ctrl/dial">
				<!--<a class="p_other-item turntable">-->
					<div class="p_other-item-title">{:lang('Lucky turntable')}</div>
					<div class="p_other-item-subtitle">{:lang('Don t miss it')}</div>
				</a>
			</div>
		</div>

		<!-- 退出按钮 -->
		<div class="p_login-out-btn tabs_btn1">{:lang('signout')}</div>

		<!-- 底部导航栏 -->
		<div class="p_footer">
			<a class="p_footer-item" href="{:url(\'index/home\')}">
				<img src="/p_static1/img/footer_img-1_active.png" class="p_footer-item-img">
				<div class="p_footer-item-text active">{:lang('Home')}</div>
			</a>
			<a class="p_footer-item" href="{:url(\'order/index\')}">
				<img src="/p_static1/img/footer_img-2.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Registro')}</div>
			</a>
			<?php
				$level = session('level') ? session('level') : 0;
				// 安全处理level值，确保为数字
				$level = is_numeric($level) ? (int)$level : 0;
				$level = $level + 1;
				$url = '/index/rot_order/index.html?type=' . $level;
			?>
			<a class="p_footer-item" href="<?=$url?>">
				<div class="p_footer-middle">
					<div class="p_footer-middle-text">{:lang('Apero')}</div>
				</div>
			</a>
			<a class="p_footer-item" href="{:url(\'support/index\')}">
				<img src="/p_static1/img/footer_img-3.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Servicio')}</div>
			</a>
			<a class="p_footer-item" href="{:url(\'my/index\')}">
				<img src="/p_static1/img/footer_img-4.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Mi')}</div>
			</a>
		</div>

		<script>
		$(function() {
		        $('.footer li').eq(4).addClass("on");

		        // 检查本地存储中的语言设置
		        var savedLang = localStorage.getItem('selectedLanguage');
		        if(savedLang) {
		            // 如果有保存的语言设置，确保下拉列表显示正确的选项
		            $("#qwerty").val(savedLang);
		        }

		        $('#qwerty').change(function(){
		            var lang=$('#qwerty').val();
		            console.log("选择的语言: " + lang); // 调试日志

		            // 防止重复点击
		            $(this).prop('disabled', true);

		            $.ajax({
		                url:'/index/cutLangss/cutlangs', // 使用与login页面相同的控制器
		                type:'post',
		                data:{lang:lang},
		                dataType:'json',
		                success:function(data){
		                    console.log("语言切换响应:", data);
		                    if(data.code==1){
		                        // 设置本地存储，以便在页面刷新后保持选择
		                        localStorage.setItem('selectedLanguage', lang);
		                        // 延迟刷新页面，确保Cookie已经设置
		                        setTimeout(function() {
		                            location.reload();
		                        }, 100);
		                    }else{
		                        alert("语言切换失败，请重试");
		                        $('#qwerty').prop('disabled', false);
		                    }
		                },
		                error:function(data){
		                    console.error("语言切换错误:", data);
		                    alert("语言切换出错，请重试");
		                    $('#qwerty').prop('disabled', false);
		                }
		            });
		        }


					);
		    });
		    $(function() {
		        $('.footer li').eq(4).addClass("on");

		    })

		    $('.tabs_btn1').click(function () {
		        $(document).dialog({
		            type: 'confirm',
		            titleText: "{$Think.lang.Areout}",
		            autoClose: 0,
		          buttonTextConfirm:'{$Think.lang.ok}',
		         buttonTextCancel:'{$Think.lang.no}',
		            onClickConfirmBtn: function () {
		                window.location.href="{:url('user/logout')}";
		            },
		            onClickCancelBtn: function () {

		            }
		        });
		//  $('document').dialog({
		//                 autoOpen: false,
		//                 width: 600,
		//                 modal: true,
		//                 title: 'cs',
		//                 buttons: {
		//                     "确定新增": function () {
		//                         alert('确定');
		//                     },
		//                     "关闭": function () {
		//                         $(this).dialog("close");
		//                     }
		//                 }
		//               });

		    });

		</script>
	</body>
</html>
