<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="stylesheet" href="/p_static1/css/base.css">
		<title>{$Think.lang.Orderrecord}</title>
		
		<link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
		<link rel="stylesheet" href="/static_new/css/public.css">
		<script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
		<script charset="utf-8" src="/static_new/js/dialog.min.js"></script>
		<script charset="utf-8" src="/static_new/js/common.js"></script>
		<link rel="stylesheet" href="__ROOT__/public/js/layer_mobile/need/layer.css">
		<script src="__ROOT__/public/js/layer_mobile/layer.js"></script>
		<style type="text/css" title="fading circle style">
		    .circle-color-9 > div::before {
		        background-color: #ccc;
		    }
		
		    /* 通用分页 */
		    .pagination-container {
		        line-height: 40px;
		        text-align: right;
		        display: none;
		    }
		    /*.pagination-container > span {*/
		    /*    color: #666;*/
		    /*    font-size: 9pt;*/
		    /*}*/
		    /*.pagination-container > ul {*/
		    /*    float: right;*/
		    /*    display: inline-block;*/
		    /*    margin: 0;*/
		    /*    padding: 0;*/
		    /*}*/
		    /*.pagination-container > ul > li {*/
		    /*    z-index: 1;*/
		    /*    display: inline-block;*/
		    /*}*/
		    /*.pagination-container > ul > li > a, .pagination-container > ul > li > span {*/
		    /*    color: #333;*/
		    /*    width: 33px;*/
		    /*    height: 30px;*/
		    /*    border: 1px solid #dcdcdc;*/
		    /*    display: inline-block;*/
		    /*    margin-left: -1px;*/
		    /*    text-align: center;*/
		    /*    line-height: 28px;*/
		    /*}*/
		    .pagination-container > ul > li > span {
		        background: #dcdcdc;
		        cursor: default;
		    }
		    .van-tab--active span{
		        color: #ff9a2c;
		    }
		    .circle-color-23 > div::before {
		        background-color: #ccc;
		    }
		    .dialog {
		        position: fixed;
		        left: 0;
		        top: 0;
		        z-index: 10001;
		        width: 100%;
		        height: 100%;
		    }
		</style>
		<style>
			body {
				padding-top: 0 !important;
				padding-bottom: 5rem;
				background-color: rgba(245, 245, 247, 1);
			}
			/* 头部 */
			.p_header {
				box-sizing: border-box;
				width: 100%;
				height: 9.7rem;
				background: url(/p_static1/img/record_bg-1.png) no-repeat;
				background-size: 100% 100%;
			}
			/* 资产信息 */
			.p_asset {
				padding-top: 2.175rem;
			}
			.p_asset-title {
				padding: 0 1rem;
				font-size: 0.8rem;
				line-height: 0.8rem;
				color: rgba(255, 255, 255, 1);
			}
			.p_asset-num {
				margin-top: 0.7rem;
				padding: 0 1rem;
				font-size: 1rem;
				line-height: 1rem;
				color: rgba(255, 255, 255, 1);
			}
			/* 导航标签 */
			.p_tabs {
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 2.2rem;
				padding: 0 1.6rem;
				height: 3rem;
				border-top: 0.05rem solid rgba(94, 100, 149, 1);
			}
			.p_tabs-item {
				position: relative;
				font-size: 0.8rem;
				line-height: 0.8rem;
				color: rgba(191, 194, 219, 1);
			}
			.p_tabs-item.active {
				color: rgba(250, 229, 202, 1);
			}
			.p_tabs-item.active::after {
				display: block;
				content: '';
				position: absolute;
				left: 50%;
				bottom: -0.6rem;
				width: 0.2rem;
				height: 0.2rem;
				background: url(/p_static1/img/record_icon-1.svg) no-repeat;
				background-size: 100% 100%;
				transform: translate(0, 0);
			}
			/* 列表 */
			.p_list {
				margin: 0.5rem 0.75rem 0;
			}
			.p_item {
				position: relative;
				box-sizing: border-box;
				padding: 1.15rem 1rem 0.75rem;
				margin-bottom: 0.5rem;
				background-color: #fff;
				border-radius: 0.5rem;
				overflow: hidden;
			}
			.p_item-product {
				box-sizing: border-box;
				display: flex;
				margin-bottom: 1.45rem;
				height: 4.5rem;
			}
			.p_item-product-img {
				flex: 0 0 6.5rem;
				height: 100%;
				border-radius: 0.5rem;
				background-color: rgba(235, 237, 255, 1);
			}
			.p_item-product-img img {
				width: 100%;
				height: 100%;
			}
			.p_item-product-msg {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				width: 100%;
				margin-left: 0.5rem;
			}
			.p_item-product-name {
				display: -webkit-box;
				-webkit-line-clamp: 3;
				-webkit-box-orient: vertical;
				overflow: hidden;
				height: 3.15rem;
				font-size: 0.7rem;
				line-height: 1.05rem;
				color: rgba(159, 161, 179, 1);
			}
			.p_item-num-wrapper {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.p_item-product-price {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(255, 112, 112, 1);
			}
			.p_item-product-num {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(36, 44, 107, 1);
			}
			.p_item-paragraph {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0.25rem 0;
			}
			.p_item-paragraph-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(139, 142, 166, 1);
			}
			.p_item-paragraph-num {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(36, 44, 107, 1);
			}
			.red {
				color: rgba(255, 112, 112, 1) !important;
			}
			.p_item-submit-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-top: 0.75rem;
				height: 2.15rem;
				font-size: 0.8rem;
				line-height: 0.8rem;
				color: #fff;
				background-color: rgba(36, 44, 107, 1);
				border-radius: 0.5rem;
			}
			.p_item-text-wrapper {
				margin-top: 1.25rem;
			}
			.p_item-text {
				padding: 0.25rem 0;
				font-size: 0.6rem;
				line-height: 0.6rem;
				color: rgba(159, 161, 179, 1);
			}
			.p_item-bgimg {
				position: absolute;
				right: -0.25rem;
				bottom: -0.325rem;
				width: 3.75rem;
				height: 3.75rem;
			}
			.p_nodata {
				font-size: 0.8rem;
				color: rgba(36, 44, 107, 1);
			}
			/* 提交订单弹窗 */
			.p_submit-popup-wrapper {
				display: none;
				position: fixed;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, .3);
				z-index: 1000;
			}
			.p_submit-popup {
				box-sizing: border-box;
				position: absolute;
				top: 10%;
				left: 50%;
				width: 16.65rem;
				height: 25.55rem;
				background: url(/p_static1/img/record_alert-bg.png) no-repeat;
				background-size: 100% 100%;
				transform: translate(-50%, 0);
			}
			.p_submit-popup-header {
				margin-top: 1.75rem;
				height: 4.825rem;
			}
			.p_submit-popup-title {
				padding-top: 0.7rem;
				font-size: 0.8rem;
				line-height: 1.05rem;
				font-weight: 700;
				color: rgba(156, 84, 33, 1);
				text-align: center;
			}
			.p_submit-popup-content {
				box-sizing: border-box;
				position: relative;
				margin: 0 0.75rem;
				padding: 1.3rem 0.75rem 0;
				height: 17.7rem;
				overflow: hidden;
			}
			.p_submit-popup-product {
				box-sizing: border-box;
				display: flex;
				margin-bottom: 0.75rem;
				height: 4.5rem;
			}
			.p_submit-popup-product-img {
				flex: 0 0 6.5rem;
				height: 100%;
				border-radius: 0.5rem;
				background-color: rgba(235, 237, 255, 1);
			}
			.p_submit-popup-product-img img {
				width: 100%;
				height: 100%;
			}
			.p_submit-popup-product-msg {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				margin-left: 0.5rem;
			}
			.p_submit-popup-product-name {
				display: -webkit-box;
				-webkit-line-clamp: 3;
				-webkit-box-orient: vertical;
				overflow: hidden;
				height: 3.15rem;
				font-size: 0.7rem;
				line-height: 1.05rem;
				color: rgba(159, 161, 179, 1);
			}
			.p_submit-popup-num-wrapper {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.p_submit-popup-product-price {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(255, 112, 112, 1);
			}
			.p_submit-popup-product-num {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(36, 44, 107, 1);
			}
			.p_submit-popup-paragraph {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0.25rem 0;
			}
			.p_submit-popup-paragraph-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(139, 142, 166, 1);
			}
			.p_submit-popup-paragraph-num {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(36, 44, 107, 1);
			}
			.p_submit-popup-btn-wrapper {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 0.95rem;
			}
			.p_submit-popup-btn {
				box-sizing: border-box;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 6.5rem;
				height: 2.5rem;
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: 700;
				border-radius: 0.5rem;
			}
			.p_submit-popup-btn.no {
				color: rgba(36, 44, 107, 1);
				border: 0.05rem solid rgba(36, 44, 107, 1);
			}
			.p_submit-popup-btn.yes {
				color: rgba(248, 217, 193, 1);
				background-color: rgba(36, 44, 107, 1);
			}
			.p_submit-popup-text-wrapper {
				margin-top: 1.25rem;
			}
			.p_submit-popup-text {
				padding: 0.25rem 0;
				font-size: 0.6rem;
				line-height: 0.7rem;
				color: rgba(159, 161, 179, 1);
			}
			.p_submit-popup-bgimg {
				position: absolute;
				right: -0.25rem;
				bottom: -0.325rem;
				width: 3.5rem;
				height: 3.5rem;
			}
			/* 底部导航栏 */
			.p_footer {
				position: fixed;
				left: 0;
				bottom: 0;
				box-sizing: border-box;
				display: flex;
				width: 100%;
				height: 3rem;
				background-color: #fff;
				border: 0.05rem solid rgba(237, 240, 255, 1);
				z-index: 99;
			}
			.p_footer-item {
				position: relative;
				box-sizing: border-box;
				padding: 0.625rem 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 20%;
				height: 100%;
			}
			.p_footer-item-img {
				width: 0.85rem;
				height: 0.85rem;
			}
			.p_footer-item-text {
				margin-top: 0.4rem;
				font-size: 0.5rem;
				line-height: 0.5rem;
				color: rgba(200, 203, 227, 1);
			}
			.p_footer-item-text.active {
				color: rgba(36, 44, 107, 1);
			}
			.p_footer-middle {
				position: absolute;
				left: 50%;
				top: 0;
				width: 4.3rem;
				height: 4.3rem;
				background: url(/p_static1/img/footer_img-middle.svg) no-repeat;
				background-size: 100% 100%;
				transform: translate(-50%, -40%);
			}
			.p_footer-middle-text {
				margin-top: 2.5rem;
				font-size: 0.5rem;
				line-height: 0.5rem;
				text-align: center;
				color: rgba(248, 217, 193, 1);
				transform: scale(.8);
			}
		</style>
	</head>
	<body>
		<!-- 加载中 -->
		<div class="mint-indicator" id="load" style="display: block;">
		    <div class="mint-indicator-wrapper" style="padding: 20px;z-index:999">
		    <span class="mint-indicator-spin">
		        <div class="mint-spinner-fading-circle circle-color-23" style="width: 32px; height: 32px;">
		            <div class="mint-spinner-fading-circle-circle is-circle2"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle3"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle4"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle5"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle6"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle7"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle8"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle9"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle10"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle11"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle12"></div>
		            <div class="mint-spinner-fading-circle-circle is-circle13"></div>
		        </div>
		    </span>
		        <span class="mint-indicator-text"style="">{$Think.lang.Loading}...</span>
		    </div>
		    <div class="mint-indicator-mask"></div>
		</div>
		
		<!-- 头部 -->
		<div class="p_header">
			<!-- 资产信息 -->
			<div class="p_asset">
				<div class="p_asset-title">{$Think.lang.Account_balance}</div>
				<div class="p_asset-num">{$balance}</div>
			</div>
			<!-- 导航标签 -->
			<div class="p_tabs">
				<a class="p_tabs-item <?php echo $status == -1 ? 'active' : ''?>" id="pending-tab" href="/index/order/index.html?status=-1">{$Think.lang.Pending}</a>
				<a class="p_tabs-item <?php echo $status == 1 ? 'active' : ''?>" id="completed-tab" href="/index/order/index.html?status=1">{$Think.lang.completed}</a>
				<a class="p_tabs-item <?php echo $status == 5 ? 'active' : ''?>" id="freezing-tab" href="/index/order/index.html?status=5">{$Think.lang.Freezing}</a>
			</div>
		</div>
		
		<!-- 列表 -->
		<div class="p_list" id="pending">
			{if $list}
			{volist name='list' id='v'}
			<?php
			$img= '/p_static1/img/record_match.png';
			if($v['status']==0) $img= '/p_static1/img/record_pending.png';
			if($v['status']==1) $img= '/p_static1/img/record_match.png';
			if($v['status']==5) $img= '/p_static1/img/record_freezing.png';
			?>
			<div class="p_item">
				<div class="p_item-product">
					<div class="p_item-product-img">
						<img src="{$v.goods_pic}" >
					</div>
					<div class="p_item-product-msg">
						<div class="p_item-product-name">{$v.goods_name}</div>
						<div class="p_item-num-wrapper">
							<div class="p_item-product-price">
							{$v.num * lang('duna') }
								<?php if($v['status'] == 5) {?>
								<span>{$Think.lang.Freezing}</span>
								<?php } ?>
							</div>
							<div class="p_item-product-num">x {$v.goods_count}</div>
						</div>
					</div>
				</div>
				<div class="p_item-paragraph">
					<div class="p_item-paragraph-title">{$Think.lang.Ordertotal}</div>
					<div class="p_item-paragraph-num"> {$v.num * lang('duna') }</div>
				</div>
				<div class="p_item-paragraph">
					<div class="p_item-paragraph-title">{$Think.lang.commission}</div>
					<div class="p_item-paragraph-num">{$v.commission * lang('duna') }</div>
				</div>
				<div class="p_item-paragraph">
					<div class="p_item-paragraph-title">{$Think.lang.Refundamount}</div>
					<div class="p_item-paragraph-num red"><?php echo ($v['commission'] + $v['num']) * lang('duna')  ?></div>
				</div>
				<?php if($v['status'] == 5) {?>
				<div class="p_item-submit-btn J_jiedong">{$Think.lang.Applyforthawing}</div>
				<?php } ?>
				<?php if($v['status'] == 0) {?>
				<div class="p_item-submit-btn" id="submit-btn" onclick="tijiao('{$v.id}')">{$Think.lang.SubmitOrder}</div>
				<?php } ?>
				<div class="p_item-text-wrapper">
					<div class="p_item-text">{$Think.lang.Collectiontime}：<?php echo date("Y-m-d H:i:s",$v['addtime']);?></div>
					<div class="p_item-text">{$Think.lang.Ordernumber}：{$v.id}</div>					
				</div>
				
				<img class="p_item-bgimg" src="{$img}">
			</div>
			
			{/volist}
			{else\}
			
			{/if}
			
			<!-- {empty name='list'}<div class="p_nodata">{$Think.lang.Thereisnorecordonispage} </div>{else}{$pagehtml|raw|default=''}{/empty} -->
			{empty name='list'}<div class="p_nodata">{$Think.lang.Thereisnorecordonispage} </div>{/empty}
			
		</div>
		
		<!-- 提交订单弹窗 -->
		<div class="p_submit-popup-wrapper" id="popup">
			<div class="p_submit-popup">
				<div class="p_submit-popup-header">
					<div class="p_submit-popup-title">felicitar <br> Coincidir con éxito</div>
				</div>
				<div class="p_submit-popup-content">
					<div class="p_submit-popup-product">
						<div class="p_submit-popup-product-img">
							<img id="oimg" src="/static_new6/img/wenhao.png" >
						</div>
						<div class="p_submit-popup-product-msg">
							<div class="p_submit-popup-product-name" id="otitle">{$Think.lang.Gettingproductinformation}</div>
							<div class="p_submit-popup-num-wrapper">
								<div class="p_submit-popup-product-price"><span id="oprice">???</span></div>
								<div class="p_submit-popup-product-num">x <span id="onum">???</span></div>
							</div>
						</div>
					</div>
					<div class="p_submit-popup-paragraph">
						<div class="p_submit-popup-paragraph-title">{$Think.lang.Ordertotal}</div>
						<div class="p_submit-popup-paragraph-num" id="ototal">???</div>
					</div>
					<div class="p_submit-popup-paragraph">
						<div class="p_submit-popup-paragraph-title">{$Think.lang.commission}</div>
						<div class="p_submit-popup-paragraph-num"><span id="yongjin">???</span></div>
					</div>
					<div class="p_submit-popup-paragraph">
						<div class="p_submit-popup-paragraph-title">{$Think.lang.Refundamount}</div>
						<div class="p_submit-popup-paragraph-num red"><span id="yuji">???</span></div>
					</div>
					<div class="p_submit-popup-btn-wrapper">
						<div class="p_submit-popup-btn no" id="popup-no-btn">{$Think.lang.Notsubmit}</div>
						<div class="p_submit-popup-btn yes" id="popup-yes-btn">{$Think.lang.Submitnow}</div>
					</div>
					<div class="p_submit-popup-text-wrapper">
						<div class="p_submit-popup-text">{$Think.lang.Collectiontime}：<span id="otime">2020-03-17T17:11:41</span></div>
						<div class="p_submit-popup-text">{$Think.lang.Ordernumber}：<span id="oid">202003171711414080</span></div>					
					</div>
					<img class="p_submit-popup-bgimg" src="/p_static1/img/record_match.png">
				</div>
				
			</div>
		</div>
		
		<!-- 底部导航栏 -->
	<div class="p_footer">
			<a class="p_footer-item" href="{:url(\'index/home\')}">
				<img src="/p_static1/img/footer_img-1_active.png" class="p_footer-item-img">
				<div class="p_footer-item-text active">{:lang('Home')}</div>
			</a>
			<a class="p_footer-item" href="{:url(\'order/index\')}">
				<img src="/p_static1/img/footer_img-2.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Registro')}</div>
			</a>
			<?php
				$level = session('level') ? session('level') : 0;
				// 安全处理level值，确保为数字
				$level = is_numeric($level) ? (int)$level : 0;
				$level = $level + 1;
				$url = '/index/rot_order/index.html?type=' . $level;
			?>
			<a class="p_footer-item" href="<?=$url?>">
				<div class="p_footer-middle">
					<div class="p_footer-middle-text">{:lang('Apero')}</div>
				</div>
			</a>
			<a class="p_footer-item" href="{:url(\'support/index\')}">
				<img src="/p_static1/img/footer_img-3.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Servicio')}</div>
			</a>
			<a class="p_footer-item" href="{:url(\'my/index\')}">
				<img src="/p_static1/img/footer_img-4.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Mi')}</div>
			</a>
		</div>
		
		<script type="text/javascript" src="/static_new6/js/manifest.3ad1d5771e9b13dbdad2.js"></script>
		<script>
		    var oid,add_id='';
		    $(function () {
		        $('#load').hide();
		    });
		    $('.pagination li').click(function () {
		        var class2= $(this).attr('class');
		        if( class2 == 'active' || class2 == 'disabled' ) {
		
		        }else{
		            var url = $(this).find('a').attr('href');
		            window.location.href = url;
		        }
		    });
		    $(function () {
		        $('.pagination-container select').attr('disabled','disabled');
		    })
		
			// 点击不提交按钮时隐藏order弹窗
		    $("#popup-no-btn").click(function() {
		    	$("#popup").hide();
		    })
		    function tijiao(id) {
		        oid = id;
		        $("#popup").show();
		        $.ajax({
		            url: "/index/order/order_info",
		            type: "POST",
		            dataType: "JSON",
		            data: { id: id },
		            beforeSend: function () {
		                loading = $(document).dialog({
		                    type: 'notice',
		                    infoIcon: '/static_new/img/loading.gif',
		                    infoText: '{$Think.lang.z_Loading}',
		                    autoClose: 0
		                });
		            },
		            success: function(res) {
		                console.log(res);
		                loading.close();
		                var data = res.data;
		                if (res.code == 0) {
		                    $('#otime').html(data.addtime)
		                    $('#oid').html(data.oid)
		                    $('#otitle').html(data.goods_name)
		                    $('#oimg').attr('src',data.goods_pic)
		                    $('#oprice').html((data.num * "{:lang('duna')}").toFixed(2))
		                    $('#onum').html(data.goods_count)
		                  //  $('#ototal').html('{$Think.lang.yuan} '+(data.num * "{:lang('duna')}").toFixed(2))
		                    $('#ototal').html((data.num * "{:lang('duna')}").toFixed(2))
		                    $('#yongjin').html(''+(data.commission * "{:lang('duna')}").toFixed(2))
		                    var yuji = ( (data.commission * 1 +  data.num * 1 ) * "{:lang('duna')}");
		                    yuji = yuji.toFixed(2);
		                    $('#yuji').html(yuji)
		                    oid = data.oid;
		                    add_id = data.add_id;
		                }
		
		            },
		            error: function(err) {
		                loading.close();
		                console.log(err) }
		        })
		    }
		    var zhujiTime = "{:config('deal_zhuji_time')}";
		    var shopTime = "{:config('deal_shop_time')}";
		
		    zhujiTime = zhujiTime *1000;
		    shopTime = shopTime *1000;
		
		    //提交
		    $('#popup-yes-btn').click(function () {
		        //--------------------------------
		        var i = 0;
		        layer.open({
		            type: 2
		            , content: '{$Think.lang.Ordersubmission}',
		            time: zhujiTime,
		            shadeClose: false,
		        });
		
		        //--------------------------------
		        var i = 0;
		        layer.open({
		            type: 2
		            , content: '{$Think.lang.Ordersubmission}',
		            time: zhujiTime,
		            shadeClose: false,
		        });
		        var timer = setInterval(function() {
		            i++;
		            if (i == 1) {
		                layer.open({
		                    type: 2
		                    , content: '{$Think.lang.Theremotehostisbeingallocated}',
		                    time: zhujiTime,
		                    shadeClose: false,
		                })
		            } else if (i == 2) {
		                layer.open({
		                    type: 2
		                    , content: '{$Think.lang.Waitforthemerchantsystemtorespond}',
		                    time: shopTime,
		                    shadeClose: false,
		                })
		                var ajaxT = setTimeout(function(){
		                    $.ajax({
		                        url: "/index/order/do_order",
		                        type: "POST",
		                        dataType: "JSON",
		                        data: { oid:oid, add_id:add_id },
		                        success: function(res) {
		                            layer.closeAll();
		                            console.log(res)
		                            if (res.code == 0) {
		                                $(document).dialog({
		                                    infoText: "{$Think.lang.Theordericompletednthesubmissionssuccessful}",
		                                    autoClose: 2000
		                                });
		                                clearInterval(timer);
		                                var linkTime = setTimeout(function() {
		                                    location.reload()
		                                }, 1800);
		                                
		                            }else {
		                                $(document).dialog({
		                                    infoText: res.info,
		                                    autoClose: 2000
		                                });
		                            }
		                            sumbit = true;
		                        },
		                        error: function(err) {
		                            layer.closeAll();
		                            console.log(err); sumbit = true;
		                        }
		                    })
		                },shopTime)
		            }
		        }, zhujiTime)
		
		
		    });
		
		    $(".J_jiedong").click(function () {
		        $(document).dialog({
		            infoText: "{$Think.lang.Pleasecontactonlinecustomerservice}",
		            autoClose: 2000
		        });
		    });
		</script>
		
	</body>
</html>
