<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="stylesheet" href="/p_static1/css/base.css">
		<title>{:lang('app_name')}</title>
		
		<link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css?v=1.6" rel="stylesheet">
		<link rel="stylesheet" href="/static_new/css/public.css">
		
		<script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
		<script charset="utf-8" src="/static_new/js/dialog.min.js"></script>
		<script charset="utf-8" src="/static_new/js/common.js"></script>
		
		<link rel="stylesheet" href="__ROOT__/public/js/layer_mobile/need/layer.css">
		<script src="__ROOT__/public/js/layer_mobile/layer.js"></script>
		<style type="text/css" title="fading circle style">
		    .circle-color-23 > div::before {
		        background-color: #ccc;
		    }
		</style>
		<style>
			body {
				position: relative;
				padding-top: 0;
				padding-bottom: 5rem;
				background: linear-gradient(140.47deg, rgba(74, 82, 143, 1) 0%, rgba(36, 45, 107, 1) 100%);
			}
			/* 标题 */
			.p_title {
				padding: 1.2rem 0 0 1.4rem;
			}
			.p_title img {
				width: 11.5rem;
				height: 4.55rem;
			}
			/* 等级图片 */
			.p_level-img {
				display: flex;
				align-items: center;
				padding: 1.2rem 1.4rem 0;
			}
			.p_level-img img {
				height: 70px;
			}
			.p_level-text {
				margin-left: 0.8rem;
				font-size: 0.9rem;
				line-height: 0.9rem;
				color: rgba(232, 235, 255, 1);
			}
			.p_detail-text {
				padding: 0.6rem 1.4rem 0;
				font-size: 0.7rem;
				line-height: 1rem;
				color: rgba(232, 235, 255, 1);
				word-break: break-word;
			}
			/* 规则按钮 */
			.p_rule-btn {
				position: fixed;
				top: 5.6rem;
				right: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 6.7rem;
				height: 1.65rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(204, 210, 252, 1);
				border-radius: 1.875rem 0 0 1.875rem;
				background-color: rgba(106, 115, 184, 1);
				z-index: 99;
			}
			/* 获取单 机器 */
			.p_machine {
				position: relative;
				margin: 2.2rem auto 0;
				width: 17.5rem;
				height: 20.55rem;
				background: url(/p_static1/img/task_tv.svg) no-repeat;
				background-size: 100% 100%;
			}
			/* 图片滚动 */
			.p_picture-wrapper {
				position: absolute;
				display: flex;
				/* top: 50px;
				left: 42px;
				width: 243px;
				height: 80px; */
				top: 2.5rem;
				left: 2.1rem;
				width: 12.15rem;
				height: 4rem;
				/* background-color: #fff; */
				overflow: hidden;
			}
			.p_picture-list {
				width: 33.33%;
				/* transform: translate(0px, -1840px); */
				transform: translate(0px, 0px);
				transition: transform 3s ease;
			}
			.p_picture-item {
				width: 100%;
				/* height: 80px; */
				height: 4rem;
			}
			.p_picture-item img {
				width: 100%;
				height: 100%;
			}
			.p_machine-msg {
				position: absolute;
				top: 9.325rem;
				left: 0;
				display: flex;
				padding-left: 2rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
			}
			.p_machine-msg-text {
				color: rgba(248, 217, 193, 1);
			}
			.p_machine-msg-time {
				margin-left: 0.6rem;
				color: rgba(232, 235, 255, 1);
			}
			.p_machine-btn {
				box-sizing: border-box;
				position: absolute;
				display: flex;
				justify-content: center;
				width: 10.5rem;
				height: 3rem;
				padding-top: 0.75rem;
				font-size: 1rem;
				line-height: 1rem;
				color: rgba(39, 48, 110, 1);
				background-repeat: no-repeat;
				background-size: 100% 100%;
			}
			.p_machine-btn.start {
				top: 11rem;
				left: 2.875rem;
				background-image: url(/p_static1/img/task_bg-3.svg);
			}
			.p_machine-btn.recharge {
				top: 14.125rem;
				left: 2.875rem;
				background-image: url(/p_static1/img/task_bg-4.svg);
			}
			/* My team */
			.p_team {
				display: flex;
				align-items: center;
			}
			.p_team-title {
				padding-left: 2.125rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(232, 235, 255, 1);
			}
			.p_team-num {
				margin-left: 1rem;
				font-size: 0.9rem;
				line-height: 0.9rem;
				font-weight: 700;
				color: rgba(232, 235, 255, 1);
			}
			/* 今日成果 */
			.p_content {
				margin: 1rem 0.75rem 0;
			}
			.p_content-line1 {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.p_total-assets {
				box-sizing: border-box;
				width: 52.5%;
				height: 4.55rem;
				padding: 1.125rem 0 0 0.65rem;
				background: url(/p_static1/img/task_bg-1.png) no-repeat;
				background-size: 100% 100%;
				color: rgba(248, 217, 193, 1);
			}
			.p_total-assets-title {
				font-size: 0.8rem;
				line-height: 0.8rem;
			}
			.p_total-assets-num {
				margin-top: 0.5rem;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 700;
			}
			.p_today-commisson {
				box-sizing: border-box;
				width: 44.6%;
				height: 4.55rem;
				padding: 1rem 0 0 0.7rem;
				background-color: rgba(36, 44, 107, 1);
				border: 0.15rem solid rgba(104, 112, 173, 1);
				border-radius: 0.5rem;
				color: rgba(232, 235, 255, 1);
			}
			.p_today-commisson-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
			}
			.p_today-commisson-num {
				margin-top: 0.3rem;
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: 700;
			}
			.p_content-line2 {
				box-sizing: border-box;
				display: flex;
				margin-top: 0.5rem;
				padding-top: 1.2rem;
				height: 5.45rem;
				background-color: rgba(36, 44, 107, 1);
				border: 0.15rem solid rgba(104, 112, 173, 1);
				border-radius: 0.5rem;
				color: rgba(232, 235, 255, 1);
			}
			.p_line2-item {
				position: relative;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;
				width: 33.33%;
				height: 2.775rem;
				text-align: center;
			}
			.p_line2-item::after {
				display: block;
				content: '';
				position: absolute;
				top: 50%;
				right: 0;
				width: 0.05rem;
				height: 1rem;
				background-color: rgba(87, 95, 161, 1);
				transform: translate(0, -50%);
			}
			.p_line2-item:last-child::after {
				display: none;
			}
			.p_line2-item-title {
				font-size: 0.65rem;
				line-height: 0.65rem;
			}
			.p_line2-item-num {
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: 700;
			}
			/* 升级图片 */
			.p_upgrade {
				position: relative;
				display: block;
				margin: 2.725rem 0.75rem 0;
				height: 7.8rem;
				background: url(/p_static1/img/task_bg-2.png) no-repeat;
				background-size: 100% 100%;
			}
			.p_upgrade-title {
				position: absolute;
				left: 1.125rem;
				top: 2.05rem;
				font-size: 1rem;
				line-height: 1.2rem;
				color: rgba(248, 217, 193, 1);
			}
			.p_upgrade-subtitle {
				position: absolute;
				left: 1.125rem;
				top: 5.425rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(251, 237, 218, 1);
			}
			.p_upgrade-yellow {
				position: absolute;
				left: 6.1rem;
				top: 5.225rem;
				width: 1.1rem;
				height: 1.1rem;
			}
			/* 规则弹窗 */
			.p_pop-up-wrapper {
				display: none;
				position: fixed;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, .3);
				z-index: 1000;
			}
			.p_pop-up {
				box-sizing: border-box;
				position: absolute;
				top: 20%;
				left: 50%;
				width: 80%;
				max-height: 70vh;
				background-color: #fff;
				border-radius: 0.5rem;
				transform: translate(-50%, 0);
			}
			.p_pop-up-title {
				position: relative;
				box-sizing: border-box;
				padding: 1.1rem 6.1rem 0 0.825rem;
				width: 100%;
				height: 3.25rem;
				font-size: 0.8rem;
				font-weight: bold;
				line-height: 1.05rem;
				color: rgba(173, 94, 33, 1);
				background: url(/p_static1/img/index_bg-5.svg) no-repeat;
				background-size: cover;
			}
			.p_pop-up-title-img {
				position: absolute;
				top: -2.4rem;
				right: 0.8rem;
				width: 6.1rem;
				height: 5.5rem;
				background: url(/p_static1/img/task_alert-img1.png) no-repeat;
				background-size: 100% 100%;
			}
			.p_pop-up-content {
				padding: 0.8rem 0.625rem 0;
				max-height: calc(70vh - 6rem);
				overflow-y: auto;
			}
			.p_pop-up-text {
				margin-bottom: 1.3rem;
				font-size: 0.7rem;
				line-height: 1.5rem;
				color: rgba(173, 94, 33, 1);
			}
			.p_pop-up-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 0 0.625rem;
				height: 2.5rem;
				font-size: 0.8rem;
				line-height: 0.8rem;
				color: rgba(248, 217, 193, 1);
				background-color: rgba(36, 44, 107, 1);;
				border-radius: 0.5rem;
			}
			/* 提交订单弹窗 */
			.p_submit-popup-wrapper {
				display: none;
				position: fixed;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, .3);
				z-index: 1000;
			}
			.p_submit-popup {
				box-sizing: border-box;
				position: absolute;
				top: 10%;
				left: 50%;
				width: 16.65rem;
				height: 25.55rem;
				background: url(/p_static1/img/record_alert-bg.png) no-repeat;
				background-size: 100% 100%;
				transform: translate(-50%, 0);
			}
			.p_submit-popup-header {
				margin-top: 1.75rem;
				height: 4.825rem;
			}
			.p_submit-popup-title {
				padding-top: 0.7rem;
				font-size: 0.8rem;
				line-height: 1.05rem;
				font-weight: 700;
				color: rgba(156, 84, 33, 1);
				text-align: center;
			}
			.p_submit-popup-content {
				box-sizing: border-box;
				position: relative;
				margin: 0 0.75rem;
				padding: 1.3rem 0.75rem 0;
				height: 17.7rem;
				overflow: hidden;
			}
			.p_submit-popup-product {
				box-sizing: border-box;
				display: flex;
				margin-bottom: 0.75rem;
				height: 4.5rem;
			}
			.p_submit-popup-product-img {
				flex: 0 0 6.5rem;
				height: 100%;
				border-radius: 0.5rem;
				background-color: rgba(235, 237, 255, 1);
			}
			.p_submit-popup-product-img img {
				width: 100%;
				height: 100%;
			}
			.p_submit-popup-product-msg {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				margin-left: 0.5rem;
			}
			.p_submit-popup-product-name {
				display: -webkit-box;
				-webkit-line-clamp: 3;
				-webkit-box-orient: vertical;
				overflow: hidden;
				height: 3.15rem;
				font-size: 0.7rem;
				line-height: 1.05rem;
				color: rgba(159, 161, 179, 1);
			}
			.p_submit-popup-num-wrapper {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.p_submit-popup-product-price {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(255, 112, 112, 1);
			}
			.p_submit-popup-product-num {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(36, 44, 107, 1);
			}
			.p_submit-popup-paragraph {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0.25rem 0;
			}
			.p_submit-popup-paragraph-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(139, 142, 166, 1);
			}
			.p_submit-popup-paragraph-num {
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(36, 44, 107, 1);
			}
			.p_submit-popup-btn-wrapper {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 0.95rem;
			}
			.p_submit-popup-btn {
				box-sizing: border-box;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 6.5rem;
				height: 2.5rem;
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: 700;
				border-radius: 0.5rem;
			}
			.p_submit-popup-btn.no {
				color: rgba(36, 44, 107, 1);
				border: 0.05rem solid rgba(36, 44, 107, 1);
			}
			.p_submit-popup-btn.yes {
				color: rgba(248, 217, 193, 1);
				background-color: rgba(36, 44, 107, 1);
			}
			.p_submit-popup-text-wrapper {
				margin-top: 1.25rem;
			}
			.p_submit-popup-text {
				padding: 0.25rem 0;
				font-size: 0.6rem;
				line-height: 0.7rem;
				color: rgba(159, 161, 179, 1);
			}
			.p_submit-popup-bgimg {
				position: absolute;
				right: -0.25rem;
				bottom: -0.325rem;
				width: 3.5rem;
				height: 3.5rem;
			}
			
			/* 底部导航栏 */
			.p_footer {
				position: fixed;
				left: 0;
				bottom: 0;
				box-sizing: border-box;
				display: flex;
				width: 100%;
				height: 3rem;
				background-color: #fff;
				border: 0.05rem solid rgba(237, 240, 255, 1);
				z-index: 99;
			}
			.p_footer-item {
				position: relative;
				box-sizing: border-box;
				padding: 0.625rem 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 20%;
				height: 100%;
			}
			.p_footer-item-img {
				width: 0.85rem;
				height: 0.85rem;
			}
			.p_footer-item-text {
				margin-top: 0.4rem;
				font-size: 0.5rem;
				line-height: 0.5rem;
				color: rgba(200, 203, 227, 1);
			}
			.p_footer-item-text.active {
				color: rgba(36, 44, 107, 1);
			}
			.p_footer-middle {
				position: absolute;
				left: 50%;
				top: 0;
				width: 4.3rem;
				height: 4.3rem;
				background: url(/p_static1/img/footer_img-middle.svg) no-repeat;
				background-size: 100% 100%;
				transform: translate(-50%, -40%);
			}
			.p_footer-middle-text {
				margin-top: 2.5rem;
				font-size: 0.5rem;
				line-height: 0.5rem;
				text-align: center;
				color: rgba(248, 217, 193, 1);
				transform: scale(.8);
			}
			/* 蓝色区域消息显示 */
			.p_blue_message {
				position: absolute;
				top: 9.5rem;
				left: 3.3rem;
				width: 11rem;
				height: 1.5rem;
				display: flex;
				align-items: center;
				z-index: 10;
				overflow: hidden;
			}
			.p_blue_message_text {
				color: #FF6600;
				font-size: 0.9rem;
				font-weight: bold;
				white-space: nowrap;
			}
		</style>
		<style>
			.zon_p {
				position: absolute;
				top: 9.325rem;
				left: 0;
			}
			.p_pp {
				padding-left: 2rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
			}
			.J_hhh {
				position: absolute;
				top: 3.25rem;
				left: 0.75rem;
				width: 14.8rem!important;
				overflow: hidden!important;
			}
			.box {
				width: 100%!important;
				height: 3.31rem!important;
				overflow: hidden!important;
			}
			.box .groups {
				height: 2887.5px !important;
				float:left !important;
				width: 4.865rem !important;
				text-align: center !important;
				transition-property: transform !important;
				transition-duration: 3s !important;
				transition-timing-function: ease !important;
			}
			.prize-item {
				width: 4.76rem !important;
				height: 4.55rem !important;
				font-size: 1.38rem !important;
				line-height: 4.415rem !important;
			}
			.prize-item > img {
				width: 3.52rem !important;
				height: 3.52rem !important;
				padding: 0.31rem !important;
				border-radius: 0.195rem !important;
			}
		</style>
	</head>
	<body>
		<!-- 标题 -->
		<!-- <div class="p_title"><img src="/p_static1/img/task_text-1.svg" ></div> -->
		<!-- 等级图片 -->
		<div class="p_level-img">
			<img src="/p_static1/img/V{$Think.get.type|default=1}.svg">
			<div class="p_level-text">{$cate.name}</div>
		</div>
		<div class="p_detail-text">{$cate.cate_info} {$Think.lang.commission}{$cate.commission_percent|default='2.4%'}</div>
		<!-- 规则按钮 -->
		<div class="p_rule-btn" id="rule-btn">{:lang('Rule description')}</div>
		
		<!-- 获取单 机器 -->
		<div class="p_machine">
			
			<!-- 图片滚动 -->
			<div class="p_picture-wrapper">
				<div class="p_picture-list">
					<div class="p_picture-item"><img src="/p_static1/img/wenhao.png" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
				</div>
				<div class="p_picture-list">
					<div class="p_picture-item"><img src="/p_static1/img/wenhao.png" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
				</div>
				<div class="p_picture-list">
					<div class="p_picture-item"><img src="/p_static1/img/wenhao.png" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
					<div class="p_picture-item"><img src="<?=$goods_pic_list[mt_rand(1, count($goods_pic_list) - 1)]?>" ></div>
				</div>
			</div>	
			
			<div class="zon_p">
				<?php foreach($ll as $key=>$vo): ?>
				<div class="p_pp" style="<?=$key < 1 ? 'display: flex' : 'display: none';?>">
					<div></div>
					<?php if ($vo['type'] > 50): ?>
					<div style="color:orange;">{$Think.lang.Matchsuccessfully}</div>
					<?php else: ?>
					<div style="color:red;">{$Think.lang.Submittedsuccessfully}</div>
					<?php endif; ?>
					<div style="margin-left: 0.6rem;color: rgba(232, 235, 255, 1);">{:date('m-d H:i:s')}</div>
				</div>
				<?php endforeach; ?>
			</div>
			<!-- <div class="p_machine-msg">
				<div class="p_machine-msg-text">897***19 match ok</div>
				<div class="p_machine-msg-time">07-23 10:19:02</div>
			</div> -->
			<!--<a class="p_machine-btn start" href="/index/ctrl/vip.html">{:lang('充值')}</a>-->
			<div class="p_machine-btn start">{:lang('online')}：<span id="p_number"></span> </div>
			<a class="p_machine-btn recharge" id="autoStart">{$Think.lang.Automaticmatching}</a>
		</div>
		
		<!-- My team -->
		<a class="p_team" href="/index/ctrl/junior">
			<div class="p_team-title">{$Think.lang.myteam}</div>
			<div class="p_team-num"><!--{$yes_team_num}元--></div>
		</a>
		
		<!-- 今日成果 -->
		<div class="p_content">
			<div class="p_content-line1">
				<div class="p_total-assets">
					<div class="p_total-assets-title">{$Think.lang.Account_balance}</div>
					<div class="p_total-assets-num">{$price}</div>
				</div>
				<div class="p_today-commisson">
					<div class="p_today-commisson-title">{$Think.lang.Commissiongrabbedtoday}</div>
					<div class="p_today-commisson-num">{$day_deal}</div>
				</div>
			</div>
			<div class="p_content-line2">
				<div class="p_line2-item">
					<div class="p_line2-item-title">{$Think.lang.Accountfrozenamount}</div>
					<div class="p_line2-item-num">{$lock_deal}</div>
				</div>
				<div class="p_line2-item">
					<div class="p_line2-item-title">{$Think.lang.Yesterdayearnings}</div>
					<div class="p_line2-item-num">{$yes_user_yongjin}</div>
				</div>
				<div class="p_line2-item">
					<div class="p_line2-item-title">{$Think.lang.TodayRemainingOrders}</div>
					<div class="p_line2-item-num">{$remaining_orders}</div>
				</div>
			</div>
		</div>
		
		<!-- 升级图片 -->
		<!--<a class="p_upgrade" href="/index/ctrl/vip.html">-->
		<!--	<div class="p_upgrade-title">RECHARGE <br> UPGRADE</div>-->
		<!--	<div class="p_upgrade-subtitle">Enjoy more</div>-->
		<!--	<img class="p_upgrade-yellow" src="/p_static1/img/arrowright-circel-yellow.svg" >-->
		<!--</a>-->
		
		<!-- 规则弹窗 -->
		<div class="p_pop-up-wrapper" id="rule-pop-up">
			<div class="p_pop-up">
				<div class="p_pop-up-title">
					<div class="p_pop-up-title-img"></div>
					<div>{:lang('Noticias importantes')}</div>
				</div>
				<div class="p_pop-up-content">
					<div class="p_pop-up-text">
						VIP level order grabbing rules<br>
						VIP 0 users do not have the qualifications to grab orders<br>
						VIP 1 users can grab orders up to 5 orders per day<br>
						VIP 2 users can grab orders up to 20 orders per day<br>
						VIP 3 users can grab orders up to 50 orders per day<br>
						VIP 4 users can grab orders up to 200 orders per day<br>
						VIP 5 users can grab orders up to 500 orders per day<br>
						VIP 6 users can grab orders up to 1000 orders per day<br>
						Once the order is completed, the profit will be paid out immediately, along with the principal. The amount of each order is automatically matched by the system. If the matching order is cancelled, the user will be restricted from grabbing orders next time<br>
						The amount of each order is automatically matched according to the user's VIP level range<br>
						Invite friends to become partners to enjoy a large number of commission benefits<br>
						If you have any questions, please contact online customer service<br>
						If the matching order is not submitted within 10,000 minutes, the system will freeze the order amount for 0 hours, and the system will automatically unfreeze it after the expiration date.
					</div>
				</div>
				<div class="p_pop-up-btn" id="pop-up-btn">{:lang('VEO')}</div>
			</div>
		</div>
		
		<!-- 提交订单弹窗 -->
		<div class="p_submit-popup-wrapper" id="popup">
			<div class="p_submit-popup">
				<div class="p_submit-popup-header">
					<div class="p_submit-popup-title">{:lang('felicitar')} <br> {:lang('Coincidir con éxito')}</div>
				</div>
				<div class="p_submit-popup-content">
					<div class="p_submit-popup-product">
						<div class="p_submit-popup-product-img">
							<img id="oimg" src="/static_new6/img/wenhao.png" >
						</div>
						<div class="p_submit-popup-product-msg">
							<div class="p_submit-popup-product-name" id="otitle">{$Think.lang.Productinformationacquisition}</div>
							<div class="p_submit-popup-num-wrapper">
								<div class="p_submit-popup-product-price" id="oprice">???</div>
								<div class="p_submit-popup-product-num">x <span id="onum">?</span></div>
							</div>
						</div>
					</div>
					<div class="p_submit-popup-paragraph">
						<div class="p_submit-popup-paragraph-title">{$Think.lang.Ordertotal}</div>
						<div class="p_submit-popup-paragraph-num" id="ototal"> ???</div>
					</div>
					<div class="p_submit-popup-paragraph">
						<div class="p_submit-popup-paragraph-title">{$Think.lang.commission}</div>
						<div class="p_submit-popup-paragraph-num"> <span id="yongjin">???</span></div>
					</div>
					<div class="p_submit-popup-paragraph">
						<div class="p_submit-popup-paragraph-title">{$Think.lang.Refundamount}</div>
						<div class="p_submit-popup-paragraph-num red">  <span id="yuji">???</span></div>
					</div>
					<div class="p_submit-popup-btn-wrapper">
						<div class="p_submit-popup-btn no tabs_btn1">{$Think.lang.Notsubmit}</div>
						<div class="p_submit-popup-btn yes tabs_btn2">{$Think.lang.Submitnow}</div>
					</div>
					<div class="p_submit-popup-text-wrapper">
						<div class="p_submit-popup-text">{$Think.lang.Collectiontime}：<span id="otime">2020-03-17T17:11:41</span></div>
						<div class="p_submit-popup-text">{$Think.lang.Ordernumber}：<span id="oid">202003171711414080</div>					
					</div>
					<img class="p_submit-popup-bgimg" src="/p_static1/img/record_match.png">
				</div>
				
			</div>
		</div>
		
		<!-- 底部导航栏 -->
		<div class="p_footer">
			<a class="p_footer-item" href="{:url(\'index/home\')}">
				<img src="/p_static1/img/footer_img-1_active.png" class="p_footer-item-img">
				<div class="p_footer-item-text active">{:lang('Home')}</div>
			</a>
			<a class="p_footer-item" href="{:url(\'order/index\')}">
				<img src="/p_static1/img/footer_img-2.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Registro')}</div>
			</a>
			<?php
				$level = session('level') ? session('level') : 0;
				// 安全处理level值，确保为数字
				$level = is_numeric($level) ? (int)$level : 0;
				$level = $level + 1;
				$url = '/index/rot_order/index.html?type=' . $level;
			?>
			<a class="p_footer-item" href="<?=$url?>">
				<div class="p_footer-middle">
					<div class="p_footer-middle-text">{:lang('Apero')}</div>
				</div>
			</a>
			<a class="p_footer-item" href="{:url(\'support/index\')}">
				<img src="/p_static1/img/footer_img-3.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Servicio')}</div>
			</a>
			<a class="p_footer-item" href="{:url(\'my/index\')}">
				<img src="/p_static1/img/footer_img-4.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Mi')}</div>
			</a>
		</div>
		
		<script type="text/javascript" src="/static_new6/js/manifest.3ad1d5771e9b13dbdad2.js"></script>
		
		<span style="display: none;">
		    <audio id="audio" src="/static_new/img/hongbao.mp3" controls="controls"></audio>
		</span>
		
		<script>
			var onlineNum = document.querySelector('#p_number');
			
			// 获取指定范围的随机整数
			function getRandomInteger(max, min) {
				return min + Math.floor(Math.random()*(max - min + 1))
			}
			
			function init() {
				let random = getRandomInteger(60000, 80000);
				onlineNum.innerHTML = random;
				
				// 每五秒变换
				setInterval(() => {
					let random = getRandomInteger(60000, 80000);
					onlineNum.innerHTML = random;
				}, 5000)
			}
			
			init();
			
		</script>
		
		<script>
		    var has_member_address = "{$has_member_address}";
		    setInterval(function () {
				// 如果是第一个把位置放到最后
		        $('.p_pp').each(function (i) {
		            if (i == 0) {
		                var dis = $(this).css('display');
		                $(this).remove();
		                $('.zon_p').append(
		                    '<div class="p_pp" style="display:none;justify-content:space-between;">' + $(this).html() + '</div>'
		                );
		            }
		        });
		
				// 然后显示后面一个
		        $('.p_pp').each(function () {
		            if ($(this).css('display') == 'none') {
		                $(this).css('display', 'flex')
		                //  alert($(this).html())
		                return false;
		            }
		        });
		    }, 2500);
		
		    var cid = "{$Think.get.type|default=1}";
		    var oid = '';
		    var add_id = '';
		    // var countdown = 5,tt1 = 2483;
		    // var countdown = 5;
		    var audio = document.getElementById("audio");
		    $(function () {
		
		        $('#autoStart').click(function () {
		            // if (has_member_address != 1) {
		            //     $(document).dialog({infoText: "{$Think.lang.Youhavenotsetthedeliveryaddress}", autoClose: 2000});
		            //     setTimeout(function () {
		            //         window.location.href = '/index/my/edit_address.html';
		            //     }, 2000);
		            //     return false;
		            // }
		            if ( $('#autoStart').attr('id') == 'autoStart' ){
		                // countdown=5;  // 随机偏移次数
		                $('#autoStart').text('{$Think.lang.Matching}');
		                start()
		            }
		
		        })
		    });
		
		    function palySong(wi) {
		        audio.load();
		        stopSong();
		        audio.play();
		        if (wi == 0) {
		            audio.pause();
		        }
		    }
		
		    function stopSong() {
		        audio.pause();
		    }
		
		    function qdSuccess(oid)
		    {
		        $('#popup').show();
		        $.ajax({
		            url: "/index/order/order_info",
		            type: "POST",
		            dataType: "JSON",
		            data: { id: oid },
		            success: function(res) {
		                console.log(res)
		                var data = res.data;
		                if (res.code == 0) {
		                    $('#otime').html(data.addtime)
		                    $('#oid').html(data.oid)
		                    $('#otitle').html(data.goods_name)
		                    $('#oimg').attr('src',data.goods_pic)
		                    $('#oprice').html((data.num * "{:lang('duna')}").toFixed(2))
		                    $('#onum').html(data.goods_count)
		                    $('#ototal').html('{$Think.lang.yuan} '+(data.num * "{:lang('duna')}").toFixed(2))
		                    $('#yongjin').html(''+(data.commission * "{:lang('duna')}").toFixed(2))
		                    var yuji = ( (data.commission * 1 +  data.num * 1 ) * "{:lang('duna')}");
		                    yuji = yuji.toFixed(2);
		                    $('#yuji').html(yuji)
		                    // var grabSuccess = document.getElementsByClassName("grabSuccess");
		                    // // var w_h = window.screen.height;
		                    // var w_h = (document.documentElement.clientHeight == 0) ? document.body.clientHeight : document.documentElement.clientHeight;
		                    // var m_h = (w_h - grabSuccess[0].scrollHeight - document.getElementsByClassName("close")[0].scrollHeight) / 2;
		                    // // $(grabSuccess).scrollTop($(grabSuccess)[0].scrollHeight);
		                    // if (m_h > 0){
		                    //     $(grabSuccess).attr('style','width: 100%;margin: '+m_h+'px auto 0;')
		                    // } else {
		                    //     $(grabSuccess).attr('style','width: 100%;margin: 0;')
		                    // }
		                }
		            },
		            error: function(err) { console.log(err) }
		        })
		    }
		
		    var num_min = "{$num_min}";
		//     function start() {
		//         // $(".J_hhh").css("height", "165px").css("padding-top", "0px");
		//         // $(".J_hhh").css("height", "165px");
		//         console.log(countdown)
		//         if (countdown <= 0) {
		//             //$('#orderDetail .van-overlay').show();
		// //------------------------------------------------------------------
		//             loading = $(document).dialog({
		//                 type: 'notice',
		//                 infoIcon: '/static_new/img/loading.gif',
		//                 infoText: '{$Think.lang.Matching}',
		//                 autoClose: 0
		//             });
		//             $.ajax({
		//                 url: "{:url('submit_order')}" + '?cid='+cid+'&m=' + Math.random(),
		//                 timeout: 0,
		//                 type: 'POST',
		
		//                 error: function (XMLHttpRequest, textStatus, errorThrown) {
		//                     setTimeout(function () {
		//                         $(".J_hhh").css("height", "125px").css("padding-top", "15px");
		//                     }, 2200);
		//                     $('#autoStart').text('{$Think.lang.Automaticmatching}');
		//                     loading.close();
		//                     $(document).dialog({infoText: "Unknown error,XMLHttpRequest.status: "+ XMLHttpRequest.status +" textStatus: " + textStatus + " errorThrown: " + errorThrown, autoClose: 2000});
		//                     //$(document).dialog({infoText: "{$Think.lang.Thecurrentorderisfullpleaseryagainlater}！", autoClose: 2000});
		//                     console.log(XMLHttpRequest);
		//                     console.log(textStatus);
		//                     console.log(errorThrown);
		//                 },
		//                 success: function (data) {
		//                     setTimeout(function () {
		//                         // $(".J_hhh").css("height", "125px").css("padding-top", "15px");
		//                     }, 2200);
		//                     $('#autoStart').text('{$Think.lang.Automaticmatching}');
		//                     if (typeof(data) == "string"){
		//                         var temp = eval('(' + data + ')');;
		//                         data = temp;
		//                     }
		//                     if (data.code == 1) {
		//                         loading.close();
		//                         $(document).dialog({infoText: data.info, autoClose: 4000});
		//                         //nextclick();
		//                     } else if (data.code == 0) {
		//                         loading.close();
		//                         palySong(1);
		//                         sessionStorage.setItem('oid', data.oid);
		//                         $(document).dialog({infoText: data.info});
		//                         qdSuccess(data.oid);
		//                         oid = data.oid;
		//                         add_id = data.add_id;
		//                     } else {
		//                         loading.close();
		//                         if (data.info) {
		//                             $(document).dialog({infoText: data.info, autoClose: 2000});
		//                         } else {
		//                             // $(document).dialog({infoText: "当前订单爆满，请稍等再试！", autoClose: 2000});
		//                             $(document).dialog({infoText: "{$Think.lang.Thelater}！", autoClose: 2000});
		//                         }
		//                     }
		//                 }
		//             });
		
		// //------------------------------------------------------------------
		
		//             tt1 = 2483 * -1;
		//             $('.animation-ease').eq(0).css('transform','translate3d(0px, '+tt1+'px, 0px)');
		//             $('.animation-ease').eq(1).css('transform','translate3d(0px, '+tt1+'px, 0px)');
		//             $('.animation-ease').eq(2).css('transform','translate3d(0px, '+tt1+'px, 0px)');
		//         }else{
		//             tt1 = sum(1,2000) * -1;
		//             $('.animation-ease').eq(0).css('transform','translate3d(0px, '+tt1+'px, 0px)')
		//             tt2 = sum(1,2000) * -1;
		//             $('.animation-ease').eq(1).css('transform','translate3d(0px, '+tt2+'px, 0px)')
		//             tt3 = sum(1,2000) * -1;
		//             $('.animation-ease').eq(2).css('transform','translate3d(0px, '+tt3+'px, 0px)')
		
		//             countdown--;
		//             setTimeout(function () {
		//                 start()
		//             }, 1000);
		//         }
		
		//     }
		
		    // function sum (m,n){
		    //     var num = Math.floor(Math.random()*(m - n) + n);
		    //     return num;
		    // }
			
			// 生成某个范围的随机数
			function randomSum(min, max) {
				var num = Math.floor(Math.random()*(max - min) + min);
				return num;
			}
			
			let countDown = 5; // 随机偏移次数
			let pictureItemNum = $(".p_picture-item").length; // 所有抽奖图片元素个数
			let pictureItemNumEveryList = pictureItemNum / 3; // 每列的抽奖图片元素个数
			let pictureItemHeight = $('.p_picture-item').eq(0).height(); // 抽奖图片元素的高度
			let pictureItemMaxOffset = (pictureItemNumEveryList-1) * pictureItemHeight; // 最大偏移量（可显示每列最后一个元素）
			// console.log(pictureItemMaxOffset);
			
			function start() {
				if (countDown <= 0) { // 回归到初始位置
				
					loading = $(document).dialog({
					    type: 'notice',
					    infoIcon: '/static_new/img/loading.gif',
					    infoText: '{$Think.lang.Matching}',
					    autoClose: 0
					});
					$.ajax({
					    url: "{:url('submit_order')}" + '?cid='+cid+'&m=' + Math.random(),
					    timeout: 0,
					    type: 'POST',
							
					    error: function (XMLHttpRequest, textStatus, errorThrown) {
					        setTimeout(function () {
					            // $(".J_hhh").css("height", "125px").css("padding-top", "15px");
					        }, 2200);
					        $('#autoStart').text('{$Think.lang.Automaticmatching}');
					        loading.close();
					        $(document).dialog({infoText: "Unknown error,XMLHttpRequest.status: "+ XMLHttpRequest.status +" textStatus: " + textStatus + " errorThrown: " + errorThrown, autoClose: 2000});
					        //$(document).dialog({infoText: "{$Think.lang.Thecurrentorderisfullpleaseryagainlater}！", autoClose: 2000});
					        console.log(XMLHttpRequest);
					        console.log(textStatus);
					        console.log(errorThrown);
					    },
					    success: function (data) {
					        setTimeout(function () {
					            // $(".J_hhh").css("height", "125px").css("padding-top", "15px");
					        }, 2200);
					        $('#autoStart').text('{$Think.lang.Automaticmatching}');
					        if (typeof(data) == "string"){
					            var temp = eval('(' + data + ')');;
					            data = temp;
					        }
					        if (data.code == 1) {
					            loading.close();
					            $(document).dialog({infoText: data.info, autoClose: 4000});
					            // 检查是否需要跳转到实名认证页面
					            if (data.need_jump == 1 && data.url) {
					                setTimeout(function() {
					                    window.location.href = data.url;
					                }, 1500);
					            }
					            //nextclick();
					        } else if (data.code == 0) {
					            loading.close();
					            palySong(1);
					            sessionStorage.setItem('oid', data.oid);
					            $(document).dialog({infoText: data.info});
					            qdSuccess(data.oid);
					            oid = data.oid;
					            add_id = data.add_id;
					        } else {
					            loading.close();
					            if (data.info) {
					                $(document).dialog({infoText: data.info, autoClose: 2000});
					            } else {
					                // $(document).dialog({infoText: "当前订单爆满，请稍等再试！", autoClose: 2000});
					                $(document).dialog({infoText: "{$Think.lang.Thelater}！", autoClose: 2000});
					            }
					        }
					    }
					});
				
					// $('.p_picture-list').eq(0).css('transform', 'translate(0px, -1840px)');
					// $('.p_picture-list').eq(1).css('transform', 'translate(0px, -1840px)');
					// $('.p_picture-list').eq(2).css('transform', 'translate(0px, -1840px)');
					
					$('.p_picture-list').eq(0).css('transform', `translate(0px, 0px)`);
					$('.p_picture-list').eq(1).css('transform', `translate(0px, 0px)`);
					$('.p_picture-list').eq(2).css('transform', `translate(0px, 0px)`);
				} else {
					// let tt1 = randomSum(1, 1840) * -1;
					// $('.p_picture-list').eq(0).css('transform', 'translate(0px, '+tt1+'px)');
					// let tt2 = randomSum(1, 1840) * -1;
					// $('.p_picture-list').eq(1).css('transform', 'translate(0px, '+tt2+'px)');
					// let tt3 = randomSum(1, 1840) * -1;
					// $('.p_picture-list').eq(2).css('transform', 'translate(0px, '+tt3+'px)');
					
					let tt1 = randomSum(1, pictureItemMaxOffset) * -1;
					$('.p_picture-list').eq(0).css('transform', 'translate(0px, '+tt1+'px)');
					let tt2 = randomSum(1, pictureItemMaxOffset) * -1;
					$('.p_picture-list').eq(1).css('transform', 'translate(0px, '+tt2+'px)');
					let tt3 = randomSum(1, pictureItemMaxOffset) * -1;
					$('.p_picture-list').eq(2).css('transform', 'translate(0px, '+tt3+'px)');
					
					countDown--;
					setTimeout(function() {
						start()
					}, 1000);
				}				
			}
		
		    $('.tabs_btn1').click(function () {
		        $(document).dialog({
		            type: 'confirm',
		            titleText: "{$Think.lang.Thelater}",
		            autoClose: 0,
		             buttonTextConfirm:'{$Think.lang.ok}',
		         buttonTextCancel:'{$Think.lang.no}',
		            onClickConfirmBtn: function () {
		                $('#popup').hide();
		                //window.location.href="{:url('user/logout')}";
		            },
		            onClickCancelBtn: function () {
		
		            }
		        });
		    });
		
		//     $('.close').click(function () {
		//         $(document).dialog({
		//             type: 'confirm',
		//             titleText: "{$Think.lang.Dominutes}",
		//             autoClose: 0,
		//             onClickConfirmBtn: function () {
		//                 $('#orderDetail .van-overlay').hide();
		//                 //window.location.href="{:url('user/logout')}";
		//             },
		//             onClickCancelBtn: function () {
		
		//             }
		//         });
		//     });
		
		    var zhujiTime = "{:config('deal_zhuji_time')}";
		    var shopTime = "{:config('deal_shop_time')}";
		
		    zhujiTime = zhujiTime *1000;
		    shopTime = shopTime *1000;
		    //提交
		    $('.tabs_btn2').click(function () {
		        //--------------------------------
		        var i = 0;
		        layer.open({
		            type: 2
		            , content: '{$Think.lang.Ordersubmission}',
		            time: zhujiTime,
		            shadeClose: false,
		        });
		
		        //--------------------------------
		        var i = 0;
		        layer.open({
		            type: 2
		            , content: '{$Think.lang.Ordersubmission}',
		            time: zhujiTime,
		            shadeClose: false,
		        });
		        var timer = setInterval(function() {
		            i++;
		            if (i == 1) {
		                layer.open({
		                    type: 2
		                    , content: '{$Think.lang.Theremotehostisbeingallocated}',
		                    time: zhujiTime,
		                    shadeClose: false,
		                })
		            } else if (i == 2) {
		                layer.open({
		                    type: 2
		                    , content: '{$Think.lang.Waitforthemerchantsystemtorespond}',
		                    time: shopTime,
		                    shadeClose: false,
		                })
		                var ajaxT = setTimeout(function(){
		                    $.ajax({
		                        url: "/index/order/do_order",
		                        type: "POST",
		                        dataType: "JSON",
		                        data: { oid:oid, add_id:add_id },
		                        success: function(res) {
		                            console.log(res)
		                            if (res.code == 0) {
		                                 layer.closeAll();
		                                $(document).dialog({
		                                    infoText: "{$Think.lang.Submittedsuccessfully}!",
		                                    autoClose: 2000
		                                });
		                                clearInterval(timer);
		                                var linkTime = setTimeout(function() {
		                                    location.reload()
		                                }, 1800);
		                            } else {
		                                layer.closeAll();
		                                $(document).dialog({
		                                    infoText: res.info,
		                                    autoClose: 2000
		                                });
		                            }
		                            sumbit = true;
		                        },
		                        error: function(err) { console.log(err); sumbit = true; }
		                    })
		                },shopTime)
		            }
		        }, zhujiTime)
		
		
		    });
		
		
		</script>
		<!-- <link rel="stylesheet" href="/static_new/css/pop.css">
		<div id="redEnvelopesPop"><div class="result-text"><span id="text_no_red_envelopes">{$Think.lang.NoRedEnvelopes}</span><p id="text_red_envelopes_tip">{$Think.lang.envelopes_tip}</p></div><div class="invite-btn"><button id="invite_btn">{$Think.lang.Invitefriends}</button></div></div>
		<script charset="utf-8" src="/static_new/js/redEnvelopes.js"></script> -->
		<script>
			// 显示隐藏规则弹窗
			$('#rule-btn').click(function() {
				$('#rule-pop-up').show();
			});
			$('#pop-up-btn').click(function() {
				$('#rule-pop-up').hide();
			});
		</script>
	</body>
</html>
