<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <link rel="stylesheet" href="/p_static1/css/base.css">
    <title>service</title>
    
    <link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
    <script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
    <script charset="utf-8" src="/static_new/js/common.js"></script>
    
    <style type="text/css" title="fading circle style">
        .circle-color-9 > div::before {
            background-color: #ccc;
        }
    </style>
    
    <style>
        body {
            padding-top: 0;
            padding-bottom: 5rem;
        }
        /* 头部 */
        .p_header {
            box-sizing: border-box;
            padding: 1.625rem 0.75rem 0;
            width: 100%;
            height: 6.725rem;
            background: url(/p_static1/img/service_bg-2.png) no-repeat;
            background-size: 100% 100%;
        }
        .p_title {
            font-size: 0.9rem;
            line-height: 0.9rem;
            font-weight: 700;
            color: rgba(248, 217, 193, 1);
        }
        .p_subtitle {
            margin-top: 0.5rem;
            padding-right: 3rem;
            font-size: 0.6rem;
            line-height: 0.8rem;
            color: rgba(248, 217, 193, 0.7);
        }
        /* 客服 */
        .p_service {
            box-sizing: border-box;
            margin: 2.2rem 0.75rem 0;
            padding: 0.675rem 1.5rem 0;
            height: 6.5rem;
            background: url(/p_static1/img/service_bg-1.png) no-repeat;
            background-size: 100% 100%;
            cursor: pointer;
        }
        .p_service-title {
            font-size: 0.8rem;
            line-height: 0.95rem;
            font-weight: 700;
            color: rgba(36, 44, 107, 1);
        }
        .p_servie-phone {
            display: flex;
            align-items: center;
            margin-top: 0.75rem;
            font-size: 0.7rem;
            line-height: 0.7rem;
            color: rgba(36, 44, 107, 1);
            text-decoration: underline;
        }
        .p_servie-phone img {
            margin-right: 0.1rem;
            width: 0.65rem;
            height: 0.65rem;
        }
        .p_servie-time {
            display: flex;
            align-items: center;
            margin-top: 0.8rem;
            font-size: 0.7rem;
            line-height: 0.7rem;
            color: rgba(36, 44, 107, 1);
        }
        .p_servie-time img {
            margin-right: 0.1rem;
            width: 0.65rem;
            height: 0.65rem;
        }
        /* 底部导航栏 */
        .p_footer {
            position: fixed;
            left: 0;
            bottom: 0;
            box-sizing: border-box;
            display: flex;
            width: 100%;
            height: 3rem;
            background-color: #fff;
            border: 0.05rem solid rgba(237, 240, 255, 1);
            z-index: 99;
        }
        .p_footer-item {
            position: relative;
            box-sizing: border-box;
            padding: 0.625rem 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 20%;
            height: 100%;
        }
        .p_footer-item-img {
            width: 0.85rem;
            height: 0.85rem;
        }
        .p_footer-item-text {
            margin-top: 0.4rem;
            font-size: 0.5rem;
            line-height: 0.5rem;
            color: rgba(200, 203, 227, 1);
        }
        .p_footer-item-text.active {
            color: rgba(36, 44, 107, 1);
        }
        .p_footer-middle {
            position: absolute;
            left: 50%;
            top: 0;
            width: 4.3rem;
            height: 4.3rem;
            background: url(/p_static1/img/footer_img-middle.svg) no-repeat;
            background-size: 100% 100%;
            transform: translate(-50%, -40%);
        }
        .p_footer-middle-text {
            margin-top: 2.5rem;
            font-size: 0.5rem;
            line-height: 0.5rem;
            text-align: center;
            color: rgba(248, 217, 193, 1);
            transform: scale(.8);
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="p_header">
        <div class="p_title">{$Think.lang.CustomerService}</div>
        <div class="p_subtitle">{$Think.lang.Ifservice}</div>
    </div>
    
    <!-- 客服 -->
    <div class="p_service-wrapper">
        {volist name='list' id='v' key='k'}
        <?php if($v['id']>2) continue; ?>
        
        <div class="p_service" onclick="handleServiceClick('{$v.url}', '{$v.username}')">
            <div class="p_service-title">{$v.username}</div>
            {if $v.tel and $v.tel != ''}
            <div class="p_servie-phone">
                <img src="/p_static1/img/service_phone.svg">
                <div>{$v.tel}</div>
            </div>
            {/if}
            {if $v.qq}
            <div class="p_servie-phone">
                <img src="/p_static1/img/service_phone.svg">
                <div>{$v.qq}</div>
            </div>
            {/if}
            {if $v.wechat}
            <div class="p_servie-phone">
                <img src="/p_static1/img/service_phone.svg">
                <div>{$v.wechat}</div>
            </div>
            {/if}
            <div class="p_servie-time">
                <img src="/p_static1/img/service_time.svg">
                <div>{$v.btime}-{$v.etime}</div>
            </div>              
        </div>
        {/volist}
    </div>
    
    <!-- 底部导航栏 -->
    <div class="p_footer">
        <a class="p_footer-item" href="/index/index/home">
            <img src="/p_static1/img/footer_img-1_active.png" class="p_footer-item-img">
            <div class="p_footer-item-text active">{:lang('Home')}</div>
        </a>
        <a class="p_footer-item" href="/index/order/index">
            <img src="/p_static1/img/footer_img-2.svg" class="p_footer-item-img">
            <div class="p_footer-item-text">{:lang('Registro')}</div>
        </a>
        <?php
            				$level = session('level') ? session('level') : 0;
				// 安全处理level值，确保为数字
				$level = is_numeric($level) ? (int)$level : 0;
				$level = $level + 1;
            $url = '/index/rot_order/index.html?type=' . $level;
        ?>
        <a class="p_footer-item" href="<?php echo $url; ?>">
            <div class="p_footer-middle">
                <div class="p_footer-middle-text">{:lang('Apero')}</div>
            </div>
        </a>
        <a class="p_footer-item" href="/index/support/index">
            <img src="/p_static1/img/footer_img-3.svg" class="p_footer-item-img">
            <div class="p_footer-item-text">{:lang('Servicio')}</div>
        </a>
        <a class="p_footer-item" href="/index/my/index">
            <img src="/p_static1/img/footer_img-4.svg" class="p_footer-item-img">
            <div class="p_footer-item-text">{:lang('Mi')}</div>
        </a>
    </div>
    
    <script>
    function handleServiceClick(url, username) {
        if (!url || url.trim() === '' || url === 'https://' || url === 'http://') {
            alert('该客服暂时不可用，请稍后再试');
            return;
        }
        window.location.href = url;
    }
    </script>
</body>
</html> 