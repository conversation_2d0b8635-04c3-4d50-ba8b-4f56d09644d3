<!DOCTYPE html>
<html data-dpr="1" style="font-size: 37.5px;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1">
    <title>{$Think.lang.forget_password}</title>
    <link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
    <link rel="stylesheet" href="/static_new/css/public.css">

    <script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
    <script charset="utf-8" src="/static_new/js/dialog.min.js"></script>
    <script charset="utf-8" src="/static_new/js/common.js"></script>

    <style type="text/css">
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 动画效果定义 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0.8;
            }
        }

        /* 主体样式 */
        body, html {
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            color: #fff;
            background: #111;
        }

        /* 背景样式 - 与注册页面保持一致 */
        body {
            background: #1a1a2e;
            position: relative;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a2e;
            z-index: -2;
        }

        /* 网格覆盖层 */
        .grid-overlay {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
            background-size: 40px 40px;
            z-index: -1;
        }

        /* 主容器 */
        .main-container {
            width: 100%;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        /* 忘记密码卡片 */
        .forget-card {
            width: 100%;
            max-width: 420px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            padding: 40px 30px;
            overflow: hidden;
            position: relative;
            animation: fadeInUp 0.8s ease-out;
        }

        /* 卡片高光效果 */
        .forget-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(130deg,
                                      rgba(255, 255, 255, 0) 0%,
                                      rgba(255, 255, 255, 0.1) 50%,
                                      rgba(255, 255, 255, 0) 100%);
            z-index: -1;
        }

        /* 返回按钮 */
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 10;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .back-btn img {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1);
        }

        /* 标题样式 */
        .forget-title {
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
            letter-spacing: 1px;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        /* 表单样式 */
        .forget-form {
            position: relative;
            z-index: 1;
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
            animation: fadeInUp 0.8s ease-out 0.3s both;
        }

        .input-group:nth-child(2) {
            animation-delay: 0.4s;
        }

        .input-group:nth-child(3) {
            animation-delay: 0.5s;
        }

        .input-group:nth-child(4) {
            animation-delay: 0.6s;
        }

        .input-group:nth-child(5) {
            animation-delay: 0.7s;
        }

        /* 输入框样式 */
        .form-input {
            width: 100%;
            height: 56px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0 15px;
            color: #fff;
            font-size: 16px;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* 密码输入框容器 */
        .password-input-container {
            position: relative;
            width: 100%;
        }

        .password-input {
            width: 100%;
            height: 56px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0 50px 0 15px;
            color: #fff;
            font-size: 16px;
            transition: all 0.3s ease;
            outline: none;
        }

        .password-input:focus {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .password-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* 手机号输入容器 */
        .phone-input-container {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .phone-input-container:focus-within {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        /* 国家选择器 */
        .country-selector {
            display: flex;
            align-items: center;
            padding: 0 15px;
            cursor: pointer;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            min-width: 80px;
        }

        .country-selector:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .country-flag {
            width: 24px;
            height: 18px;
            margin-right: 8px;
            border-radius: 2px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .country-code {
            color: #fff;
            font-size: 14px;
            font-weight: 500;
        }

        /* 手机号输入框 */
        .phone-input {
            flex: 1;
            height: 56px;
            background: transparent;
            border: none;
            padding: 0 15px;
            color: #fff;
            font-size: 16px;
            outline: none;
        }

        .phone-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* 国家选择模态窗口 */
        .country-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: none;
            flex-direction: column;
        }

        .country-modal-header {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.08);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .country-modal-back {
            font-size: 24px;
            color: #fff;
            cursor: pointer;
            margin-right: 15px;
            padding: 5px;
        }

        .country-modal-title {
            color: #fff;
            font-size: 18px;
            font-weight: 600;
        }

        .country-modal-search {
            position: relative;
            padding: 10px 15px;
            background-color: #1a1a2e;
        }

        .country-modal-search input {
            width: 100%;
            background-color: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #fff;
            padding: 10px 15px;
            padding-right: 40px;
            font-size: 16px;
            outline: none;
        }

        .country-modal-search input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .country-modal-search .search-icon {
            position: absolute;
            right: 25px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            font-size: 18px;
        }

        .country-modal-list {
            flex: 1;
            overflow-y: auto;
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .country-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
        }

        .country-item.selected {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .country-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .country-item .country-flag {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 15px;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .country-item .country-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .country-item .country-name {
            color: #fff;
            font-size: 16px;
        }

        .country-item .country-code {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
        }

        .country-modal-confirm {
            padding: 15px;
            margin: 15px;
            background-color: #3a7bd5;
            color: #fff;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
        }

        .country-modal-confirm:hover {
            background-color: #2968c4;
        }

        /* 自定义滚动条 */
        .country-modal-list::-webkit-scrollbar {
            width: 5px;
        }

        .country-modal-list::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .country-modal-list::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .no-results {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.5);
        }

        /* 眼睛图标 */
        .eye-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.6;
            transition: all 0.3s ease;
            filter: brightness(0) invert(1);
        }

        .eye-icon:hover {
            opacity: 1;
            transform: translateY(-50%) scale(1.1);
        }

        /* 按钮样式 */
        .btn {
            width: 100%;
            height: 56px;
            border-radius: 12px;
            border: none;
            outline: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        /* 按钮闪光效果 */
        .btn:after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s;
        }

        .btn:hover:after {
            left: 100%;
        }

        .btn:active {
            transform: scale(0.98);
        }

        .submit-btn {
            background: #3a7bd5;
            color: #fff;
            box-shadow: 0 4px 15px rgba(58, 123, 213, 0.3);
        }

        .submit-btn:hover {
            background: #2968c4;
            box-shadow: 0 7px 20px rgba(58, 123, 213, 0.4);
            transform: translateY(-2px);
        }

        .login-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            backdrop-filter: blur(5px);
            animation-delay: 0.9s;
        }

        .login-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        /* 隐藏原有样式 */
        .fa {
            display: none !important;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .forget-card {
                margin: 10px;
                padding: 30px 20px;
            }
            
            .forget-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 网格覆盖层 -->
    <div class="grid-overlay"></div>
    
    <!-- 主容器 -->
    <div class="main-container">
        <div class="forget-card">
            <!-- 返回按钮 -->
            <div class="back-btn" onclick="window.history.back(-1)">
                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQ1IDc5LjE2MzQ5OSwgMjAxOC8wOC8xMy0xNjo0MDoyMiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTkgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkJENDQwM0ZGMzM4RTExRUE5RUFEOTdFODk1MERGRTU0IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkJENDQwNDAwMzM4RTExRUE5RUFEOTdFODk1MERGRTU0Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QkQ0NDAzRkQzMzhFMTFFQTlFQUQ5N0U4OTUwREZFNTQiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QkQ0NDAzRkUzMzhFMTFFQTlFQUQ5N0U4OTUwREZFNTQiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7AgDVjAAAB7klEQVR42txX20rDQBBNVvFvrD8gPuuDF4yEoLSI1kqL39OHWqVYLKVUQX3xVfwC9We0KPGsTGQMu9tJmlAwMEySnT3nZLM7O+vHcezN81LenK8/AsIwXIOtlkVmwlessQL3BHvGfbME8ibDr0z7BW0EHRVIrrHazl8wGo1e4RqsrVuECMLoslcN4vq5/PQqQIcTuA496sYqOlznJD+A62seRn7uXAUUkIyE7tgnoMLJjSPgGIkQAGMh+Z7+FiLXfU9N5M48wEYiJqAhAUvIh4z80EbuHAEGWIPrEeAXbBeA95bYLbhb2AIjv3Lh+5JUnBLxCdsE8KOB/Aa2KCUXCzCImMC2ExFoW4d7yEqeSYBFxAZsCXZHXoPtg3woxfSz7oYpEe80kXOR5xJgEOHlJZ9lO57MrR6grx/Q13+Q6fsB2qJSBRgm4Q7ZhImolTIHXLlglmWocpIHPBHRfUBtOqYnHQklID9JpeLAlIrpXUAxYhFKQN5h5JFtH2AioiwilIA8WeeRZDummIjtoj3CkguwVEVj6cym2CoT0bGJUELyzCUZ9UlEeDYR6XPBWYq8nrceZCLqLhG/eYBq9RfWdgyAywLL8gv2aiWpjG2TsFUUOY2ExmpNzYTJiYXX7QWfjpYJ/22m7fhfnY6/BRgAYRculwNsxNUAAAAASUVORK5CYII=" alt="返回">
            </div>
            
            <!-- 标题 -->
            <h1 class="forget-title">{$Think.lang.forget_password}</h1>
            
            <!-- 表单 -->
            <form class="forget-form" id="forgetpwd-form" action="/index/user/do_forget" method="POST">
                <input type="hidden" name="__token__" value="{$Request.token}" />
                
                <!-- 手机号输入 -->
                <div class="input-group">
                    <div class="phone-input-container">
                        <div class="country-selector" id="country_code_selector">
                            <div class="country-flag" id="selected_flag"></div>
                            <div class="country-code" id="selected_code">+86</div>
                        </div>
                        <input type="text" name="tel" class="phone-input" placeholder="{$Think.lang.enter_phone_number}" required>
                        <input type="hidden" id="country_code" name="country_code" value="+86">
                    </div>
                </div>
                
                <!-- 用户名输入 -->
                <div class="input-group">
                    <input type="text" name="username" class="form-input" placeholder="{$Think.lang.enter_username}" required>
                </div>
                
                <!-- 新密码输入 -->
                <div class="input-group">
                    <div class="password-input-container">
                        <input type="password" name="pwd" class="password-input" placeholder="{$Think.lang.enter_new_password}" required>
                        <img class="eye-icon" src="data:image/png;base64,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" alt="显示密码">
                    </div>
                </div>
                
                <!-- 确认密码输入 -->
                <div class="input-group">
                    <div class="password-input-container">
                        <input type="password" name="pwd_re" class="password-input" placeholder="{$Think.lang.confirm_new_password}" required>
                        <img class="eye-icon" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDY3IDc5LjE1Nzc0NywgMjAxNS8wMy8zMC0yMzo0MDo0MiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkZGODBCNzE0NkFGNTExRTlCOTMzRTFGMEZEMjg1N0U2IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkZGODBCNzE1NkFGNTExRTlCOTMzRTFGMEZEMjg1N0U2Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RkY4MEI3MTI2QUY1MTFFOUI5MzNFMUYwRkQyODU3RTYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RkY4MEI3MTM2QUY1MTFFOUI5MzNFMUYwRkQyODU3RTYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5Zo3XTAAADHElEQVR42uyZXUhUQRTH7123QEqspChIn6y2CEKifKhAoodedmsfIiqKsmgpoi8Swoegb3opqaDWggqDYiULlkCwVl8kwofIwkKWMouKIslUrNW6/Q+chWG6e7mr3ZXsHPgzc2dmZ+Y3d+bM2V3TsixjPJjPGCcmIAIiIAIiIAIiIAIiIALy/4D47Qqj0doyJBegYugsdPHuy/jPsZjg5tIKX7+/YDeyVdBHaG8ksvOxKxDYFWgx52ugXuj6WIAAYj3PIQ8qga5BC9xurZna8+FwIFiSa4hwIDQDSTVDZJqbI8gJ7XkedBMwE3MFsWZ+GLvFuoXsQq3quGsQnIfLSJq14hVQPWDycuKFrOEbSFZqxa3Fg1012XqtrVC3VhaCGgAzzbvtFCyEYshu1Ko+QVvOv3lmZQWCt0IQO6ABG5gEBqvwAGI5kofQOq1qiOaCOb0a0T2CDzYh2Qb90KoWQTEMfGptIDTrbxxq9HUM2TuKt0wbuf3tmEvcqQ/T7net1QeC+kpRQW0Gj/Eaug1dnTzc11WXbPmVxf1AnrAS2gCV2jT7Au0CRL1a2HguPjIQhinT7hfVqJMeqA1qgZ5CndBXrd0UaC6/UdqaSyA6b6ZNn9RHBBB/XH52IH63rx8dPgHMKmQP0u0KFaoLAhXRGrBoO6Sgz5ySkeuezqmT5+vlBTuNMXtGFaI4wNAKHwEQucVlDk1povl8E2drbRinyvOgERCHkCzVirttHIIbG2K3qlo5xqj0FAQDzOGQYYJSnISC7PcvQQ+gF1CfTRcDXJeg2BTaRN1yMJi2AoosMNbsUUe/DhaApmpl1dgK7Ujb+bKkM0CTKOcIukjxQPugR9B7fOa7skBHeRHSRg6A3Po7r7ZWsxYFR3XXiOcUX1yt0KD6NizDTFCdCqGERHVqv+y1DK8Oez9Wbz+ycd7fTQ7N6V6YpG4Z07AoAPyQof0e6B5lUr78xvsdsZSXW4tgyD02uGia5DOR3or0Ft469PvNZb+5/arLsdpJ6Dkf8DO4+Tu9Gs+Uv94EREAEREAEREAEREAERED+NfstwAAy2fUNiniqRQAAAABJRU5ErkJggg==" alt="显示密码">
                    </div>
                </div>
                
                <!-- 提交按钮 -->
                <button type="submit" class="btn submit-btn">{$Think.lang.reset_password}</button>
                
                <!-- 登录链接 -->
                <button type="button" class="btn login-btn" onclick="window.location.href='/index/user/login'">{$Think.lang.already_have_account}</button>
            </form>
        </div>
    </div>

    <!-- 国家选择模态窗口 -->
    <div class="country-modal" id="country_modal" style="display: none;">
        <div class="country-modal-header">
            <div class="country-modal-back" id="country_modal_back">←</div>
            <div class="country-modal-title">Select Country/Region</div>
        </div>
        <div class="country-modal-search">
            <input type="text" id="country_search" placeholder="Search country or code">
            <div class="search-icon">🔍</div>
        </div>
        <div class="country-modal-list" id="country_list"></div>
        <button class="country-modal-confirm" id="country_confirm">Confirm</button>
    </div>

    <script type="application/javascript">
        $(function(){
            // 初始化国家选择器
            initCountrySelector();
            
            var loading = null;
            
            // 密码显示/隐藏切换
            $(".eye-icon").on('click', function() {
                var input = $(this).prev('input');
                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                } else {
                    input.attr('type', 'password');
                }
            });
            
            // 表单验证函数
            function check(){
                if($("input[name=tel]").val()==''){
                    $(document).dialog({infoText: '{$Think.lang.phone_number_required}'});
                    return false;
                }
                
                var myreg=/^[1][3,4,5,6,7,8,9][0-9]{9}$/;
                if (!myreg.test($("input[name=tel]").val())) {
                    $(document).dialog({infoText: '{$Think.lang.phone_number_invalid}'});
                    return false;
                }
                
                if($("input[name=username]").val()==''){
                    $(document).dialog({infoText: '{$Think.lang.username_required}'});
                    return false;
                }

                if($("input[name=pwd]").val()==''||$("input[name=pwd_re]").val()==''){
                    $(document).dialog({infoText: '{$Think.lang.password_required}'});
                    return false;
                }
                
                if($("input[name=pwd]").val()!==$("input[name=pwd_re]").val()){
                    $(document).dialog({infoText: '{$Think.lang.passwords_not_match}'});
                    return false;
                }
                
                var pwdValue = $("input[name=pwd]").val();
                if(pwdValue.length < 6) {
                    $(document).dialog({infoText: '{$Think.lang.password_min_length}'});
                    return false;
                }
                
                return true;
            }

            // 表单提交处理
            $("#forgetpwd-form").on('submit', function(e) {
                e.preventDefault();

                if(!check()) {
                    return false;
                }
                
                var formData = $(this).serialize();
                var formAction = $(this).attr('action');

                loading = $(document).dialog({
                    type : 'notice',
                    infoIcon: '/static_new/img/loading.gif',
                    infoText: '{$Think.lang.submitting}',
                    autoClose: 0
                });
                
                $.ajax({
                    url: formAction,
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if(loading) {
                            loading.close();
                        }
                        $(document).dialog({infoText: response.info});
                        
                        if(response.code == 0) {
                            $("input[name=pwd]").val('');
                            $("input[name=pwd_re]").val('');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        if(loading) {
                            loading.close();
                        }
                        $(document).dialog({infoText: '{$Think.lang.request_failed}: ' + textStatus});
                    }
                });
                
                return false;
            });
        });

        function initCountrySelector() {
            // 国家代码和国旗数据 - 按国家名称A-Z排序
            const countryData = [
                { code: "+86", country: "China", flag: "cn" },
                { code: "+1", country: "United States", flag: "us" },
                { code: "+44", country: "United Kingdom", flag: "gb" },
                { code: "+81", country: "Japan", flag: "jp" },
                { code: "+82", country: "South Korea", flag: "kr" },
                { code: "+65", country: "Singapore", flag: "sg" },
                { code: "+852", country: "Hong Kong", flag: "hk" },
                { code: "+886", country: "Taiwan", flag: "tw" },
                { code: "+60", country: "Malaysia", flag: "my" },
                { code: "+66", country: "Thailand", flag: "th" },
                { code: "+84", country: "Vietnam", flag: "vn" },
                { code: "+63", country: "Philippines", flag: "ph" },
                { code: "+62", country: "Indonesia", flag: "id" },
                { code: "+91", country: "India", flag: "in" },
                { code: "+61", country: "Australia", flag: "au" },
                { code: "+64", country: "New Zealand", flag: "nz" },
                { code: "+33", country: "France", flag: "fr" },
                { code: "+49", country: "Germany", flag: "de" },
                { code: "+39", country: "Italy", flag: "it" },
                { code: "+34", country: "Spain", flag: "es" },
                { code: "+7", country: "Russia", flag: "ru" },
                { code: "+55", country: "Brazil", flag: "br" },
                { code: "+52", country: "Mexico", flag: "mx" },
                { code: "+54", country: "Argentina", flag: "ar" },
                { code: "+27", country: "South Africa", flag: "za" },
                { code: "+20", country: "Egypt", flag: "eg" },
                { code: "+971", country: "United Arab Emirates", flag: "ae" },
                { code: "+966", country: "Saudi Arabia", flag: "sa" },
                { code: "+90", country: "Turkey", flag: "tr" }
            ];
            
            const countrySelector = document.getElementById('country_code_selector');
            const countryModal = document.getElementById('country_modal');
            const countryList = document.getElementById('country_list');
            const countrySearch = document.getElementById('country_search');
            const modalBack = document.getElementById('country_modal_back');
            const confirmBtn = document.getElementById('country_confirm');
            const selectedFlag = document.getElementById('selected_flag');
            const selectedCode = document.getElementById('selected_code');
            const hiddenInput = document.getElementById('country_code');
            
            let selectedCountry = countryData.find(c => c.code === "+86") || countryData[0];
            let tempSelectedCountry = null;
            
            // 渲染国家列表
            function renderCountryList(filter = '') {
                countryList.innerHTML = '';
                
                const filteredCountries = countryData.filter(country => 
                    country.country.toLowerCase().includes(filter.toLowerCase()) || 
                    country.code.includes(filter)
                );
                
                if (filteredCountries.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'no-results';
                    noResults.textContent = 'No matching countries found';
                    countryList.appendChild(noResults);
                    return;
                }
                
                filteredCountries.forEach(country => {
                    const item = document.createElement('div');
                    item.className = 'country-item';
                    if (tempSelectedCountry && tempSelectedCountry.code === country.code) {
                        item.classList.add('selected');
                    }
                    
                    const flagUrl = `https://flagcdn.com/w40/${country.flag}.png`;
                    
                    item.innerHTML = `
                        <div class="country-flag">
                            <img src="${flagUrl}" alt="${country.country}" style="width: 24px; height: 18px; border-radius: 2px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="display: none; width: 24px; height: 18px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 2px; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">${country.flag.toUpperCase()}</div>
                        </div>
                        <div class="country-info">
                            <div class="country-name">${country.country}</div>
                            <div class="country-code">${country.code}</div>
                        </div>
                    `;
                    
                    item.addEventListener('click', () => {
                        // 清除之前的选中状态
                        const selected = countryList.querySelector('.selected');
                        if (selected) {
                            selected.classList.remove('selected');
                        }
                        
                        // 设置当前选中状态
                        item.classList.add('selected');
                        tempSelectedCountry = country;
                    });
                    
                    countryList.appendChild(item);
                });
            }
            
            // 打开国家选择模态窗口
            countrySelector.addEventListener('click', () => {
                tempSelectedCountry = selectedCountry;
                countryModal.style.display = 'flex';
                renderCountryList();
                
                // 滚动到当前选中的国家
                setTimeout(() => {
                    const selected = countryList.querySelector('.selected');
                    if (selected) {
                        selected.scrollIntoView({ block: 'center', behavior: 'smooth' });
                    }
                }, 100);
            });
            
            // 关闭模态窗口
            modalBack.addEventListener('click', () => {
                countryModal.style.display = 'none';
                countrySearch.value = '';
            });
            
            // 搜索功能
            countrySearch.addEventListener('input', (e) => {
                renderCountryList(e.target.value);
            });
            
            // 确认选择
            confirmBtn.addEventListener('click', () => {
                if (tempSelectedCountry) {
                    selectedCountry = tempSelectedCountry;
                    
                    // 更新显示
                    const flagUrl = `https://flagcdn.com/w40/${selectedCountry.flag}.png`;
                    selectedFlag.innerHTML = `
                        <img src="${flagUrl}" alt="${selectedCountry.country}" style="width: 24px; height: 18px; border-radius: 2px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div style="display: none; width: 24px; height: 18px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 2px; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">${selectedCountry.flag.toUpperCase()}</div>
                    `;
                    selectedCode.textContent = selectedCountry.code;
                    
                    // 更新隐藏输入框的值
                    hiddenInput.value = selectedCountry.code;
                }
                
                // 关闭模态窗口
                countryModal.style.display = 'none';
                countrySearch.value = '';
            });
            
            // 初始化选中的国家显示
            const flagUrl = `https://flagcdn.com/w40/${selectedCountry.flag}.png`;
            selectedFlag.innerHTML = `
                <img src="${flagUrl}" alt="${selectedCountry.country}" style="width: 24px; height: 18px; border-radius: 2px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div style="display: none; width: 24px; height: 18px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 2px; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">${selectedCountry.flag.toUpperCase()}</div>
            `;
            selectedCode.textContent = selectedCountry.code;
            hiddenInput.value = selectedCountry.code;
        }
    </script>
</body>
</html>
