<!DOCTYPE html>
<html data-dpr="1" style="font-size: 37.5px;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1">
    <title>{$Think.lang.login}</title>

    <style>
        /* 基础样式 */
        * {margin: 0; padding: 0; box-sizing: border-box;}

        /* 动画效果定义 */
        @keyframes gradientAnimation {
            0% {background-position: 0% 50%}
            50% {background-position: 100% 50%}
            100% {background-position: 0% 50%}
        }

        @keyframes floatBubble {
            0% {transform: translateY(0) translateX(0);}
            50% {transform: translateY(-20px) translateX(10px);}
            100% {transform: translateY(0) translateX(0);}
        }

        @keyframes fadeInUp {
            from {opacity: 0; transform: translateY(20px);}
            to {opacity: 1; transform: translateY(0);}
        }

        @keyframes pulse {
            0% {transform: scale(1); opacity: 0.8;}
            50% {transform: scale(1.05); opacity: 1;}
            100% {transform: scale(1); opacity: 0.8;}
        }

        /* 主体样式 */
        body, html {
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            color: #fff;
            background: #111;
        }

        /* 背景渐变 */
        .bg-gradient {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(-45deg, #1a1a2e, #16213e, #0f3460, #1a1a2e);
            background-size: 400% 400%;
            animation: gradientAnimation 15s ease infinite;
            z-index: -2;
        }

        /* 气泡元素 */
        .bubble {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
            box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(1px);
            opacity: 0.3;
            z-index: -1;
        }

        /* 网格覆盖层 */
        .grid-overlay {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
            background-size: 40px 40px;
            z-index: -1;
        }

        /* 主容器 */
        .main-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        /* 登录卡片 */
        .login-card {
            width: 100%;
            max-width: 420px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            padding: 40px 30px;
            overflow: hidden;
            position: relative;
            animation: fadeInUp 0.8s ease-out;
        }

        /* 登录卡片高光效果 */
        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(130deg,
                                      rgba(255, 255, 255, 0) 0%,
                                      rgba(255, 255, 255, 0.1) 50%,
                                      rgba(255, 255, 255, 0) 100%);
            z-index: -1;
        }

        /* Logo区域 */
        .logo-container {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            position: relative;
        }

        .app-logo {
            width: 80px;
            height: 80px;
            animation: pulse 3s infinite ease-in-out;
        }

        /* 标题 */
        .login-title {
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
            letter-spacing: 1px;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        /* 表单样式 */
        .login-form {
            position: relative;
            z-index: 1;
        }

        .input-group {
            position: relative;
            margin-bottom: 25px;
            animation: fadeInUp 0.8s ease-out 0.3s both;
        }

        .input-group:nth-child(2) {
            animation-delay: 0.4s;
        }

        /* 电话输入容器 */
        .phone-input-container {
            display: flex;
            align-items: center;
            width: 100%;
            height: 56px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            overflow: visible;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .phone-input-container:focus-within {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        /* 国家代码选择器 */
        .country-selector {
            display: flex;
            align-items: center;
            padding: 0 15px;
            cursor: pointer;
            height: 100%;
            min-width: 80px;
            background: rgba(255, 255, 255, 0.05);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .country-selector:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .country-flag {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            transition: all 0.3s ease;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .country-code {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 500;
        }

        .dropdown-arrow {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin-left: 5px;
            transition: transform 0.3s ease;
        }

        .country-selector.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        /* 国家下拉列表样式 */
        .country-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
            z-index: 1000;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            margin-top: 5px;
        }

        .country-dropdown.active {
            max-height: 320px;
            opacity: 1;
            visibility: visible;
        }

        .country-list {
            max-height: 300px;
            overflow-y: auto;
            padding: 8px 0;
        }

        .country-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .country-item:last-child {
            border-bottom: none;
        }

        .country-item:hover {
            background: #f0f8ff;
            padding-left: 20px;
        }

        .country-item.selected {
            background: #e3f2fd;
            color: #1976d2;
            font-weight: 500;
        }

        .country-item .country-flag {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .country-item .country-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .country-item .country-name {
            color: #333;
            font-size: 14px;
            font-weight: 400;
        }

        .country-item .country-code {
            color: #666;
            font-size: 13px;
            font-weight: 500;
        }

        .country-item.selected .country-name {
            color: #1976d2;
        }

        .country-item.selected .country-code {
            color: #1976d2;
        }

        /* 滚动条样式 */
        .country-list::-webkit-scrollbar {
            width: 6px;
        }

        .country-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .country-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .country-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 电话和密码输入框 */
        .phone-input,
        .password-input {
            flex: 1;
            width: 100%;
            height: 56px;
            background: transparent;
            border: none;
            outline: none;
            padding: 0 15px;
            color: #fff;
            font-size: 16px;
        }

        .password-input {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding-left: 50px;
            padding-right: 50px;
            transition: all 0.3s ease;
        }

        .password-input:focus {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .phone-input::placeholder,
        .password-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* 输入框图标 */
        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.7);
            z-index: 2;
        }

        /* 切换密码可见性按钮 */
        .toggle-password {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: rgba(255, 255, 255, 0.6);
            transition: all 0.3s ease;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toggle-password:hover {
            color: rgba(255, 255, 255, 1);
        }

        /* 按钮样式 */
        .btn {
            width: 100%;
            height: 56px;
            border-radius: 12px;
            border: none;
            outline: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out 0.5s both;
        }

        /* 按钮闪光效果 */
        .btn:after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s;
        }

        .btn:hover:after {
            left: 100%;
        }

        .btn:active {
            transform: scale(0.98);
        }

        .login-btn {
            background: linear-gradient(90deg, #3a7bd5, #00d2ff);
            color: #fff;
            box-shadow: 0 4px 15px rgba(0, 210, 255, 0.3);
        }

        .login-btn:hover {
            box-shadow: 0 7px 20px rgba(0, 210, 255, 0.4);
            transform: translateY(-2px);
        }

        .register-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            backdrop-filter: blur(5px);
            animation-delay: 0.6s;
        }

        .register-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .service-btn {
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            backdrop-filter: blur(5px);
            animation-delay: 0.7s;
        }

        .service-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        /* 底部选项 */
        .options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        /* 自定义复选框 */
        .custom-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .checkmark {
            width: 18px;
            height: 18px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            display: inline-block;
            position: relative;
            transition: all 0.3s ease;
        }

        .custom-checkbox:hover .checkmark {
            background: rgba(255, 255, 255, 0.2);
        }

        .custom-checkbox input:checked ~ .checkmark {
            background: #3a7bd5;
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 3px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .custom-checkbox input:checked ~ .checkmark:after {
            display: block;
        }

        .remember-text {
            margin-left: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        /* 忘记密码链接 */
        .forget-password {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .forget-password:hover {
            color: #fff;
            text-decoration: underline;
        }

        /* 语言选择器 - 精简设计 */
        .language-selector {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 100;
            animation: fadeInUp 0.5s ease;
        }

        .language-dropdown {
            position: relative;
        }

        .language-current {
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 156, 255, 0.3);
            backdrop-filter: blur(10px);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            color: #ffffff;
            font-size: 12px;
            font-weight: 500;
            letter-spacing: 0.3px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            min-width: 80px;
            text-align: center;
        }

        .language-current:hover {
            background: rgba(0, 156, 255, 0.4);
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .language-current:before {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 5px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-2.29-2.333A17.9 17.9 0 0 1 8.027 13H4.062a8.008 8.008 0 0 0 5.648 6.667zM10.03 13c.151 2.439.848 4.73 1.97 6.752A15.905 15.905 0 0 0 13.97 13h-3.94zm9.908 0h-3.965a17.9 17.9 0 0 1-1.683 6.667A8.008 8.008 0 0 0 19.938 13zM4.062 11h3.965A17.9 17.9 0 0 1 9.71 4.333 8.008 8.008 0 0 0 4.062 11zm5.969 0h3.938A15.905 15.905 0 0 0 12 4.248 15.905 15.905 0 0 0 10.03 11zm4.259-6.667A17.9 17.9 0 0 1 15.973 11h3.965a8.008 8.008 0 0 0-5.648-6.667z' fill='rgba(255,255,255,0.9)'/%3E%3C/svg%3E");
            background-size: cover;
        }

        .language-current:after {
            content: '';
            width: 8px;
            height: 8px;
            margin-left: 5px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z' fill='rgba(255,255,255,0.9)'/%3E%3C/svg%3E");
            background-size: cover;
            transition: transform 0.3s;
        }

        .language-dropdown.active .language-current:after {
            transform: rotate(180deg);
        }

        .language-options {
            position: absolute;
            top: 110%;
            right: 0;
            background: rgba(25, 55, 105, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 4px;
            width: 120px;
            overflow: hidden;
            max-height: 0;
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
            z-index: 1000;
        }

        .language-dropdown.active .language-options {
            max-height: 250px;
            opacity: 1;
            visibility: visible;
        }

        .language-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            color: rgba(255, 255, 255, 0.9);
            cursor: pointer;
            transition: all 0.2s;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            font-weight: 400;
            font-size: 14px;
            font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .language-option:last-child {
            border-bottom: none;
        }

        .language-option:hover {
            background: rgba(0, 156, 255, 0.3);
            color: #fff;
            padding-left: 15px;
        }

        .language-option.selected {
            background: rgba(0, 156, 255, 0.4);
            color: #fff;
            font-weight: 500;
        }

        .language-option:before {
            content: '';
            width: 5px;
            height: 0;
            background: #00c6ff;
            position: absolute;
            left: 0;
            opacity: 0;
            transition: all 0.2s;
        }

        .language-option:hover:before,
        .language-option.selected:before {
            height: 100%;
            opacity: 1;
        }

        /* 加载器样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto 15px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #3a7bd5;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {transform: rotate(360deg);}
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .login-card {
                max-width: 100%;
                padding: 30px 20px;
            }

            .language-selector {
                top: 10px;
                right: 10px;
            }

            .btn, .phone-input-container, .password-input {
                height: 50px;
            }

            .country-dropdown.active {
                max-height: 250px;
            }

            .country-list {
                max-height: 230px;
            }

            .country-item {
                padding: 10px 14px;
            }

            .country-item .country-flag {
                width: 20px;
                height: 20px;
                margin-right: 10px;
                font-size: 10px;
            }
        }

        /* 语言选择器动画 */
        @keyframes fadeInOption {
            from {
                opacity: 0;
                transform: translateY(-5px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                transform: translateX(10px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 语言选择器 - 响应式调整 */
        @media (max-width: 480px) {
            .language-selector {
                top: 10px;
                right: 10px;
            }

            .language-current {
                padding: 5px 10px;
                font-size: 11px;
                min-width: 70px;
            }

            .language-options {
                width: 100px;
            }

            .language-option {
                padding: 6px 10px;
                font-size: 12px;
            }
        }

        /* 国家选择模态窗口样式 */
        .country-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 10000;
            display: none;
            flex-direction: column;
            animation: fadeIn 0.3s ease;
        }

        .country-modal-header {
            display: flex;
            align-items: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .country-modal-back {
            font-size: 24px;
            color: #fff;
            cursor: pointer;
            margin-right: 15px;
            transition: all 0.3s ease;
        }

        .country-modal-back:hover {
            color: #3a7bd5;
        }

        .country-modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
        }

        .country-modal-search {
            position: relative;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
        }

        .country-modal-search input {
            width: 100%;
            height: 50px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 0 50px 0 20px;
            color: #fff;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .country-modal-search input:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .country-modal-search input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .search-icon {
            position: absolute;
            right: 35px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            font-size: 18px;
        }

        .country-modal-list {
            flex: 1;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 10px 0;
        }

        .country-modal-list .country-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .country-modal-list .country-item:hover {
            background: #f0f8ff;
        }

        .country-modal-list .country-item.selected {
            background: #e3f2fd;
        }

        .country-modal-list .country-flag {
            width: 32px;
            height: 24px;
            margin-right: 15px;
            border-radius: 4px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .country-modal-list .country-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .country-modal-list .country-name {
            color: #333;
            font-size: 16px;
            font-weight: 400;
        }

        .country-modal-list .country-code {
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        .country-modal-list .country-item.selected .country-name,
        .country-modal-list .country-item.selected .country-code {
            color: #3a7bd5;
            font-weight: 600;
        }

        .country-modal-confirm {
            width: 100%;
            height: 60px;
            background: #3a7bd5;
            color: #fff;
            border: none;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .country-modal-confirm:hover {
            background: #2968c4;
        }

        .country-modal-confirm:active {
            transform: scale(0.98);
        }

        /* 自定义滚动条 */
        .country-modal-list::-webkit-scrollbar {
            width: 5px;
        }

        .country-modal-list::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .country-modal-list::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .no-results {
            text-align: center;
            padding: 20px;
            color: rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
<div class="bg-gradient"></div>
<div class="grid-overlay"></div>

<!-- 动态气泡背景 -->
<div id="bubbles-container"></div>

<div class="main-container">
    <div class="login-card">
        <!-- Logo区域 -->
        <div class="logo-container">
            <img src="/image/logo.png" alt="Logo" class="app-logo">
        </div>

        <h1 class="login-title">{$Think.lang.account_login|default="账户登录"}</h1>

        <!-- 登录表单 -->
        <form class="login-form">
            <!-- 电话输入 -->
            <div class="input-group">
                <div class="phone-input-container">
                    <div class="country-selector" id="country-selector">
                        <div class="country-flag" id="selected-flag" style="background-color: #de2910;">🇨🇳</div>
                        <div class="country-code" id="selected-code">+86</div>
                    </div>
                    <input type="text" name="tel" placeholder="{$Think.lang.LoginPhone}" class="phone-input">
                    <input type="hidden" id="country-code-input" value="+86">
                </div>
            </div>

            <!-- 密码输入 -->
            <div class="input-group">
                <div class="input-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <input type="password" name="pwd" placeholder="{$Think.lang.LoginPassword}" class="password-input">
                <div class="toggle-password" id="toggle-password">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
        </form>

        <!-- 按钮区域 -->
        <button class="btn login-btn" id="login-btn">{$Think.lang.login}</button>
        <button class="btn register-btn" onclick="location.href='/index/user/register'">{$Think.lang.register}</button>
        <button class="btn service-btn" onclick="location.href='/index/user/kefu'">{$Think.lang.ContactService}</button>

        <!-- 底部选项 -->
        <div class="options">
            <label class="custom-checkbox">
                <input type="checkbox" id="remember-pwd" name="jizhu" checked>
                <span class="checkmark"></span>
                <span class="remember-text">{$Think.lang.Remember}</span>
            </label>
            <a href="/index/user/forget" class="forget-password">
                {$Think.lang.password_forget}
            </a>
        </div>
    </div>
</div>

<!-- 语言选择器 -->
<div class="language-selector">
    <div class="language-dropdown" id="language-dropdown">
        <div class="language-current" id="current-language">{$Think.lang.current_language|default="English"}</div>
        <div class="language-options" id="language-options">
            <div class="language-option" data-lang="en-us" {if $lang=='en-us' || empty($lang)}data-selected="true"{/if}>English</div>
            <div class="language-option" data-lang="mi" {if $lang=='mi'}data-selected="true"{/if}>Maori</div>
            <div class="language-option" data-lang="thai" {if $lang=='thai'}data-selected="true"{/if}>Thai</div>
            <div class="language-option" data-lang="baxi" {if $lang=='baxi'}data-selected="true"{/if}>Brasil</div>
            <div class="language-option" data-lang="moxige" {if $lang=='moxige'}data-selected="true"{/if}>México</div>
            <div class="language-option" data-lang="tuerqi" {if $lang=='tuerqi'}data-selected="true"{/if}>Turkey</div>
            <div class="language-option" data-lang="arabic" {if $lang=='arabic'}data-selected="true"{/if}>العربية</div>
        </div>
    </div>
</div>

<!-- 加载指示器 -->
<div id="loading-indicator" class="loading-overlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <div id="loading-text">{$Think.lang.Loading}...</div>
    </div>
</div>

<!-- 国家选择模态窗口 -->
<div class="country-modal" id="country-modal" style="display: none;">
    <div class="country-modal-header">
        <div class="country-modal-back" id="country-modal-back">←</div>
        <div class="country-modal-title">{$Think.lang.SelectCountryRegion|default="选择国家/地区"}</div>
    </div>
    <div class="country-modal-search">
        <input type="text" id="country-search" placeholder="{$Think.lang.SearchCountryCode|default="搜索国家或区号"}">
        <div class="search-icon">🔍</div>
    </div>
    <div class="country-modal-list" id="country-list"></div>
    <button class="country-modal-confirm" id="country-confirm">{$Think.lang.Confirm|default="确认"}</button>
</div>

<!-- 代码继续... -->
<script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 按顺序初始化各个功能
        initializeBackground();
        initializeLanguageSelector();
        initializePasswordToggle();
        initializeCountrySelector();
        initializeLoginForm();
    });

    // 初始化背景效果
    function initializeBackground() {
        const container = document.getElementById('bubbles-container');
        if (!container) return;
        
        const bubbleCount = window.innerWidth < 768 ? 8 : 12;
        for (let i = 0; i < bubbleCount; i++) {
            const size = Math.random() * 100 + 50;
            const bubble = document.createElement('div');
            bubble.classList.add('bubble');
            bubble.style.width = `${size}px`;
            bubble.style.height = `${size}px`;
            bubble.style.left = `${Math.random() * 100}%`;
            bubble.style.top = `${Math.random() * 100}%`;
            bubble.style.animation = `floatBubble ${Math.random() * 10 + 15}s infinite ease-in-out`;
            container.appendChild(bubble);
        }
    }

    // 初始化语言选择器
    function initializeLanguageSelector() {
        const dropdown = document.getElementById('language-dropdown');
        const currentLanguage = document.getElementById('current-language');
        const options = document.querySelectorAll('.language-option');

        if (!dropdown || !currentLanguage) return;

        // 设置初始选中语言
        let foundSelected = false;
        options.forEach(option => {
            if (option.hasAttribute('data-selected')) {
                currentLanguage.textContent = option.textContent;
                option.classList.add('selected');
                foundSelected = true;
            }
        });

        if (!foundSelected) {
            const englishOption = document.querySelector('.language-option[data-lang="en-us"]');
            if (englishOption) {
                englishOption.classList.add('selected');
                currentLanguage.textContent = englishOption.textContent;
            }
        }

        // 下拉菜单切换
        dropdown.addEventListener('click', function(e) {
            e.stopPropagation();
            this.classList.toggle('active');
        });

        // 语言选择
        options.forEach(option => {
            option.addEventListener('click', function(e) {
                e.stopPropagation();
                const lang = this.getAttribute('data-lang');

                if (this.classList.contains('selected')) {
                    dropdown.classList.remove('active');
                    return;
                }

                currentLanguage.textContent = this.textContent;
                options.forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');

                showLoading('{$Think.lang.Loading}...');

                fetch('/index/cutLangss/cutlangs', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `lang=${lang}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code == 1) {
                        document.body.style.opacity = '0';
                        document.body.style.transition = 'opacity 0.3s ease';
                        setTimeout(() => window.location.href = window.location.pathname, 300);
                    } else {
                        hideLoading();
                        showNotification('{$Think.lang.error}');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showNotification('{$Think.lang.error}');
                });
            });
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', () => dropdown.classList.remove('active'));
    }

    // 初始化密码可见性切换
    function initializePasswordToggle() {
        const togglePassword = document.getElementById('toggle-password');
        const passwordInput = document.querySelector('input[name="pwd"]');

        if (!togglePassword || !passwordInput) return;

        togglePassword.addEventListener('click', function() {
            const isPassword = passwordInput.getAttribute('type') === 'password';
            passwordInput.setAttribute('type', isPassword ? 'text' : 'password');

            this.innerHTML = isPassword ? 
                `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 3.96914 7.65663 6.06 6.06M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1752 15.0074 10.8016 14.8565C10.4281 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.572 9.14351 13.1984C8.99262 12.8248 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4859 9.58525 10.1546 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M1 1L23 23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>` :
                `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`;
        });
    }

    // 初始化国家选择器
    function initializeCountrySelector() {
        // 国家数据 - 与注册页面保持一致，按国家名称A-Z排序
        const countryData = [
            { code: "+93", country: "Afghanistan", flag: "af" },
            { code: "+355", country: "Albania", flag: "al" },
            { code: "+213", country: "Algeria", flag: "dz" },
            { code: "+376", country: "Andorra", flag: "ad" },
            { code: "+244", country: "Angola", flag: "ao" },
            { code: "+54", country: "Argentina", flag: "ar" },
            { code: "+374", country: "Armenia", flag: "am" },
            { code: "+61", country: "Australia", flag: "au" },
            { code: "+43", country: "Austria", flag: "at" },
            { code: "+994", country: "Azerbaijan", flag: "az" },
            { code: "+973", country: "Bahrain", flag: "bh" },
            { code: "+880", country: "Bangladesh", flag: "bd" },
            { code: "+375", country: "Belarus", flag: "by" },
            { code: "+32", country: "Belgium", flag: "be" },
            { code: "+501", country: "Belize", flag: "bz" },
            { code: "+229", country: "Benin", flag: "bj" },
            { code: "+975", country: "Bhutan", flag: "bt" },
            { code: "+591", country: "Bolivia", flag: "bo" },
            { code: "+387", country: "Bosnia and Herzegovina", flag: "ba" },
            { code: "+267", country: "Botswana", flag: "bw" },
            { code: "+55", country: "Brazil", flag: "br" },
            { code: "+673", country: "Brunei", flag: "bn" },
            { code: "+359", country: "Bulgaria", flag: "bg" },
            { code: "+226", country: "Burkina Faso", flag: "bf" },
            { code: "+257", country: "Burundi", flag: "bi" },
            { code: "+855", country: "Cambodia", flag: "kh" },
            { code: "+237", country: "Cameroon", flag: "cm" },
            { code: "+1", country: "Canada", flag: "ca" },
            { code: "+238", country: "Cape Verde", flag: "cv" },
            { code: "+236", country: "Central African Republic", flag: "cf" },
            { code: "+235", country: "Chad", flag: "td" },
            { code: "+56", country: "Chile", flag: "cl" },
            { code: "+86", country: "China", flag: "cn" },
            { code: "+57", country: "Colombia", flag: "co" },
            { code: "+269", country: "Comoros", flag: "km" },
            { code: "+242", country: "Congo", flag: "cg" },
            { code: "+506", country: "Costa Rica", flag: "cr" },
            { code: "+385", country: "Croatia", flag: "hr" },
            { code: "+53", country: "Cuba", flag: "cu" },
            { code: "+357", country: "Cyprus", flag: "cy" },
            { code: "+420", country: "Czech Republic", flag: "cz" },
            { code: "+45", country: "Denmark", flag: "dk" },
            { code: "+253", country: "Djibouti", flag: "dj" },
            { code: "+1", country: "Dominican Republic", flag: "do" },
            { code: "+593", country: "Ecuador", flag: "ec" },
            { code: "+20", country: "Egypt", flag: "eg" },
            { code: "+503", country: "El Salvador", flag: "sv" },
            { code: "+240", country: "Equatorial Guinea", flag: "gq" },
            { code: "+291", country: "Eritrea", flag: "er" },
            { code: "+372", country: "Estonia", flag: "ee" },
            { code: "+251", country: "Ethiopia", flag: "et" },
            { code: "+679", country: "Fiji", flag: "fj" },
            { code: "+358", country: "Finland", flag: "fi" },
            { code: "+33", country: "France", flag: "fr" },
            { code: "+241", country: "Gabon", flag: "ga" },
            { code: "+220", country: "Gambia", flag: "gm" },
            { code: "+995", country: "Georgia", flag: "ge" },
            { code: "+49", country: "Germany", flag: "de" },
            { code: "+233", country: "Ghana", flag: "gh" },
            { code: "+30", country: "Greece", flag: "gr" },
            { code: "+502", country: "Guatemala", flag: "gt" },
            { code: "+224", country: "Guinea", flag: "gn" },
            { code: "+245", country: "Guinea-Bissau", flag: "gw" },
            { code: "+592", country: "Guyana", flag: "gy" },
            { code: "+509", country: "Haiti", flag: "ht" },
            { code: "+504", country: "Honduras", flag: "hn" },
            { code: "+852", country: "Hong Kong", flag: "hk" },
            { code: "+36", country: "Hungary", flag: "hu" },
            { code: "+354", country: "Iceland", flag: "is" },
            { code: "+91", country: "India", flag: "in" },
            { code: "+62", country: "Indonesia", flag: "id" },
            { code: "+964", country: "Iraq", flag: "iq" },
            { code: "+353", country: "Ireland", flag: "ie" },
            { code: "+972", country: "Israel", flag: "il" },
            { code: "+39", country: "Italy", flag: "it" },
            { code: "+1", country: "Jamaica", flag: "jm" },
            { code: "+81", country: "Japan", flag: "jp" },
            { code: "+962", country: "Jordan", flag: "jo" },
            { code: "+7", country: "Kazakhstan", flag: "kz" },
            { code: "+254", country: "Kenya", flag: "ke" },
            { code: "+965", country: "Kuwait", flag: "kw" },
            { code: "+996", country: "Kyrgyzstan", flag: "kg" },
            { code: "+856", country: "Laos", flag: "la" },
            { code: "+371", country: "Latvia", flag: "lv" },
            { code: "+961", country: "Lebanon", flag: "lb" },
            { code: "+266", country: "Lesotho", flag: "ls" },
            { code: "+231", country: "Liberia", flag: "lr" },
            { code: "+218", country: "Libya", flag: "ly" },
            { code: "+423", country: "Liechtenstein", flag: "li" },
            { code: "+370", country: "Lithuania", flag: "lt" },
            { code: "+352", country: "Luxembourg", flag: "lu" },
            { code: "+853", country: "Macau", flag: "mo" },
            { code: "+389", country: "Macedonia", flag: "mk" },
            { code: "+261", country: "Madagascar", flag: "mg" },
            { code: "+265", country: "Malawi", flag: "mw" },
            { code: "+60", country: "Malaysia", flag: "my" },
            { code: "+960", country: "Maldives", flag: "mv" },
            { code: "+223", country: "Mali", flag: "ml" },
            { code: "+356", country: "Malta", flag: "mt" },
            { code: "+222", country: "Mauritania", flag: "mr" },
            { code: "+230", country: "Mauritius", flag: "mu" },
            { code: "+52", country: "Mexico", flag: "mx" },
            { code: "+373", country: "Moldova", flag: "md" },
            { code: "+377", country: "Monaco", flag: "mc" },
            { code: "+976", country: "Mongolia", flag: "mn" },
            { code: "+382", country: "Montenegro", flag: "me" },
            { code: "+212", country: "Morocco", flag: "ma" },
            { code: "+258", country: "Mozambique", flag: "mz" },
            { code: "+95", country: "Myanmar", flag: "mm" },
            { code: "+264", country: "Namibia", flag: "na" },
            { code: "+977", country: "Nepal", flag: "np" },
            { code: "+31", country: "Netherlands", flag: "nl" },
            { code: "+64", country: "New Zealand", flag: "nz" },
            { code: "+505", country: "Nicaragua", flag: "ni" },
            { code: "+227", country: "Niger", flag: "ne" },
            { code: "+234", country: "Nigeria", flag: "ng" },
            { code: "+47", country: "Norway", flag: "no" },
            { code: "+968", country: "Oman", flag: "om" },
            { code: "+92", country: "Pakistan", flag: "pk" },
            { code: "+970", country: "Palestine", flag: "ps" },
            { code: "+507", country: "Panama", flag: "pa" },
            { code: "+675", country: "Papua New Guinea", flag: "pg" },
            { code: "+595", country: "Paraguay", flag: "py" },
            { code: "+51", country: "Peru", flag: "pe" },
            { code: "+63", country: "Philippines", flag: "ph" },
            { code: "+48", country: "Poland", flag: "pl" },
            { code: "+351", country: "Portugal", flag: "pt" },
            { code: "+974", country: "Qatar", flag: "qa" },
            { code: "+40", country: "Romania", flag: "ro" },
            { code: "+7", country: "Russia", flag: "ru" },
            { code: "+250", country: "Rwanda", flag: "rw" },
            { code: "+966", country: "Saudi Arabia", flag: "sa" },
            { code: "+221", country: "Senegal", flag: "sn" },
            { code: "+381", country: "Serbia", flag: "rs" },
            { code: "+232", country: "Sierra Leone", flag: "sl" },
            { code: "+65", country: "Singapore", flag: "sg" },
            { code: "+421", country: "Slovakia", flag: "sk" },
            { code: "+386", country: "Slovenia", flag: "si" },
            { code: "+252", country: "Somalia", flag: "so" },
            { code: "+27", country: "South Africa", flag: "za" },
            { code: "+82", country: "South Korea", flag: "kr" },
            { code: "+211", country: "South Sudan", flag: "ss" },
            { code: "+34", country: "Spain", flag: "es" },
            { code: "+94", country: "Sri Lanka", flag: "lk" },
            { code: "+249", country: "Sudan", flag: "sd" },
            { code: "+597", country: "Suriname", flag: "sr" },
            { code: "+268", country: "Swaziland", flag: "sz" },
            { code: "+46", country: "Sweden", flag: "se" },
            { code: "+41", country: "Switzerland", flag: "ch" },
            { code: "+963", country: "Syria", flag: "sy" },
            { code: "+886", country: "Taiwan", flag: "tw" },
            { code: "+992", country: "Tajikistan", flag: "tj" },
            { code: "+255", country: "Tanzania", flag: "tz" },
            { code: "+66", country: "Thailand", flag: "th" },
            { code: "+228", country: "Togo", flag: "tg" },
            { code: "+216", country: "Tunisia", flag: "tn" },
            { code: "+90", country: "Turkey", flag: "tr" },
            { code: "+993", country: "Turkmenistan", flag: "tm" },
            { code: "+256", country: "Uganda", flag: "ug" },
            { code: "+380", country: "Ukraine", flag: "ua" },
            { code: "+971", country: "United Arab Emirates", flag: "ae" },
            { code: "+44", country: "United Kingdom", flag: "gb" },
            { code: "+1", country: "United States", flag: "us" },
            { code: "+598", country: "Uruguay", flag: "uy" },
            { code: "+998", country: "Uzbekistan", flag: "uz" },
            { code: "+58", country: "Venezuela", flag: "ve" },
            { code: "+84", country: "Vietnam", flag: "vn" },
            { code: "+967", country: "Yemen", flag: "ye" },
            { code: "+260", country: "Zambia", flag: "zm" },
            { code: "+263", country: "Zimbabwe", flag: "zw" }
        ];
        
        const countrySelector = document.getElementById('country-selector');
        const countryModal = document.getElementById('country-modal');
        const countryList = document.getElementById('country-list');
        const countrySearch = document.getElementById('country-search');
        const modalBack = document.getElementById('country-modal-back');
        const confirmBtn = document.getElementById('country-confirm');
        const selectedFlag = document.getElementById('selected-flag');
        const selectedCode = document.getElementById('selected-code');
        const hiddenInput = document.getElementById('country-code-input');
        
        if (!countrySelector || !countryModal || !countryList) {
            return;
        }
        
        let selectedCountry = countryData.find(c => c.code === "+86") || countryData[0];
        let tempSelectedCountry = null;
        
        // 渲染国家列表
        function renderCountryList(filter = '') {
            countryList.innerHTML = '';
            
            const filteredCountries = countryData.filter(country => 
                country.country.toLowerCase().includes(filter.toLowerCase()) || 
                country.code.includes(filter)
            );
            
            if (filteredCountries.length === 0) {
                const noResults = document.createElement('div');
                noResults.className = 'no-results';
                noResults.textContent = '{$Think.lang.NoMatchingCountries|default="未找到匹配的国家"}';
                countryList.appendChild(noResults);
                return;
            }
            
            filteredCountries.forEach(country => {
                const item = document.createElement('div');
                item.className = 'country-item';
                if (tempSelectedCountry && tempSelectedCountry.code === country.code) {
                    item.classList.add('selected');
                }
                
                const flagUrl = `https://flagcdn.com/w40/${country.flag}.png`;
                
                item.innerHTML = `
                    <div class="country-flag">
                        <img src="${flagUrl}" alt="${country.country}" style="width: 32px; height: 24px; border-radius: 4px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div style="display: none; width: 32px; height: 24px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 4px; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">${country.flag.toUpperCase()}</div>
                    </div>
                    <div class="country-info">
                        <div class="country-name">${country.country}</div>
                        <div class="country-code">${country.code}</div>
                    </div>
                `;
                
                item.addEventListener('click', () => {
                    // 清除之前的选中状态
                    const selected = countryList.querySelector('.selected');
                    if (selected) {
                        selected.classList.remove('selected');
                    }
                    
                    // 设置当前选中状态
                    item.classList.add('selected');
                    tempSelectedCountry = country;
                });
                
                countryList.appendChild(item);
            });
        }
        
        // 打开国家选择模态窗口
        countrySelector.addEventListener('click', () => {
            tempSelectedCountry = selectedCountry;
            countryModal.style.display = 'flex';
            renderCountryList();
            
            // 滚动到当前选中的国家
            setTimeout(() => {
                const selected = countryList.querySelector('.selected');
                if (selected) {
                    selected.scrollIntoView({ block: 'center', behavior: 'smooth' });
                }
            }, 100);
        });
        
        // 关闭模态窗口
        modalBack.addEventListener('click', () => {
            countryModal.style.display = 'none';
            countrySearch.value = '';
        });
        
        // 搜索功能
        countrySearch.addEventListener('input', (e) => {
            renderCountryList(e.target.value);
        });
        
        // 确认选择
        confirmBtn.addEventListener('click', () => {
            if (tempSelectedCountry) {
                selectedCountry = tempSelectedCountry;
                
                // 更新显示
                const flagUrl = `https://flagcdn.com/w40/${selectedCountry.flag}.png`;
                selectedFlag.innerHTML = `
                    <img src="${flagUrl}" alt="${selectedCountry.country}" style="width: 24px; height: 18px; border-radius: 2px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div style="display: none; width: 24px; height: 18px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 2px; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">${selectedCountry.flag.toUpperCase()}</div>
                `;
                selectedCode.textContent = selectedCountry.code;
                
                // 更新隐藏输入框的值
                hiddenInput.value = selectedCountry.code;
            }
            
            // 关闭模态窗口
            countryModal.style.display = 'none';
            countrySearch.value = '';
        });
        
        // 初始化选中的国家显示
        const flagUrl = `https://flagcdn.com/w40/${selectedCountry.flag}.png`;
        selectedFlag.innerHTML = `
            <img src="${flagUrl}" alt="${selectedCountry.country}" style="width: 24px; height: 18px; border-radius: 2px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <div style="display: none; width: 24px; height: 18px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 2px; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">${selectedCountry.flag.toUpperCase()}</div>
        `;
        selectedCode.textContent = selectedCountry.code;
        hiddenInput.value = selectedCountry.code;
        
        // IP检测功能 - 保持原有功能
        setTimeout(function() {
            detectCountryByIP(countryData, selectedCountry, function(detectedCountry) {
                if (detectedCountry && detectedCountry.code !== selectedCountry.code) {
                    selectedCountry = detectedCountry;
                    
                    // 更新显示
                    const flagUrl = `https://flagcdn.com/w40/${selectedCountry.flag}.png`;
                    selectedFlag.innerHTML = `
                        <img src="${flagUrl}" alt="${selectedCountry.country}" style="width: 24px; height: 18px; border-radius: 2px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div style="display: none; width: 24px; height: 18px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 2px; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">${selectedCountry.flag.toUpperCase()}</div>
                    `;
                    selectedCode.textContent = selectedCountry.code;
                    hiddenInput.value = selectedCountry.code;
                }
            });
        }, 1000);
    }

    // IP检测功能 - 更新为支持新的数据结构
    function detectCountryByIP(countryData, currentCountry, callback) {
        fetch('https://ipapi.co/json/')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            if (data && data.country_calling_code) {
                const detectedCode = data.country_calling_code;
                const detectedCountry = countryData.find(country => country.code === detectedCode);
                
                if (detectedCountry && callback) {
                    callback(detectedCountry);
                }
            }
        })
        .catch(error => {
            console.log('IP检测失败，使用默认国家:', error);
        });
    }

    // 初始化登录表单
    function initializeLoginForm() {
        const loginBtn = document.getElementById('login-btn');
        if (!loginBtn) return;

        loginBtn.addEventListener('click', function() {
            const tel = document.querySelector('input[name="tel"]').value.trim();
            const pwd = document.querySelector('input[name="pwd"]').value;
            const jizhu = document.getElementById('remember-pwd').checked ? 1 : 0;
            const countryCode = document.getElementById('country-code-input').value.replace('+', '');

            if (!tel || !pwd) {
                showNotification('{$Think.lang.Pleaseaccountpassword}');
                return;
            }

            this.style.transform = 'scale(0.95)';
            setTimeout(() => this.style.transform = 'none', 150);

            showLoading('{$Think.lang.Loading}');

            fetch('{:url("do_login")}', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `tel=${encodeURIComponent(tel)}&pwd=${encodeURIComponent(pwd)}&jizhu=${jizhu}&country_code=${countryCode}`
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.code == 0) {
                    showNotification(data.info);
                    setTimeout(() => location.href = "{:url('index/home')}", 1500);
                } else {
                    showNotification(data.info || '{$Think.lang.error}');
                }
            })
            .catch(error => {
                hideLoading();
                showNotification('{$Think.lang.networkerror}');
            });
        });

        // 回车键提交
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !document.querySelector('.language-dropdown.active')) {
                loginBtn.click();
            }
        });
    }

    // 显示加载指示器
    function showLoading(message) {
        const loadingIndicator = document.getElementById('loading-indicator');
        const loadingText = document.getElementById('loading-text');
        if (loadingIndicator && loadingText) {
            loadingText.textContent = message;
            loadingIndicator.style.display = 'flex';
        }
    }

    // 隐藏加载指示器
    function hideLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
    }

    // 显示通知
    function showNotification(message, duration = 2000) {
        let notification = document.getElementById('notification');

        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification';
            notification.style.cssText = `
                position: fixed; bottom: 20px; left: 50%; z-index: 2000;
                transform: translateX(-50%) translateY(100px);
                background: rgba(0, 0, 0, 0.7); color: #fff;
                padding: 12px 20px; border-radius: 30px; font-size: 14px;
                transition: all 0.3s ease; backdrop-filter: blur(5px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            `;
            document.body.appendChild(notification);
        }

        notification.textContent = message;
        setTimeout(() => notification.style.transform = 'translateX(-50%) translateY(0)', 10);
        setTimeout(() => notification.style.transform = 'translateX(-50%) translateY(100px)', duration);
    }
</script>
</body>
</html>