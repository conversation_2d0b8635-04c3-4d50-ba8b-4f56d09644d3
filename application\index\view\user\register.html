<!DOCTYPE html>
<!-- saved from url=(0052)http://qiang6-www.baomiche.com/#/Register?code=79053 -->
<html data-dpr="1" style="font-size: 37.5px;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1">
    <title>{$Think.lang.register}</title>
    <link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
    <link rel="stylesheet" href="/static_new/css/public.css">

    <script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
    <script charset="utf-8" src="/static_new/js/dialog.min.js"></script>
    <script charset="utf-8" src="/static_new/js/common.js"></script>

    <style type="text/css">
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 动画效果定义 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0.8;
            }
        }

        /* 主体样式 */
        body, html {
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            color: #fff;
            background: #111;
        }

        /* 背景样式 - 与登录页面保持一致 */
        body {
            background: #1a1a2e;
            position: relative;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a2e;
            z-index: -2;
        }

        /* 网格覆盖层 */
        .grid-overlay {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
            background-size: 40px 40px;
            z-index: -1;
        }

        /* 主容器 */
        .main-container {
            width: 100%;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        /* 注册卡片 */
        .register-card {
            width: 100%;
            max-width: 420px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            padding: 40px 30px;
            overflow: hidden;
            position: relative;
            animation: fadeInUp 0.8s ease-out;
        }

        /* 注册卡片高光效果 */
        .register-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(130deg,
                                      rgba(255, 255, 255, 0) 0%,
                                      rgba(255, 255, 255, 0.1) 50%,
                                      rgba(255, 255, 255, 0) 100%);
            z-index: -1;
        }

        /* 标题样式 */
        .register-title {
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
            letter-spacing: 1px;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        /* 表单样式 */
        .register-form {
            position: relative;
            z-index: 1;
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
            animation: fadeInUp 0.8s ease-out 0.3s both;
        }

        .input-group:nth-child(2) {
            animation-delay: 0.4s;
        }

        .input-group:nth-child(3) {
            animation-delay: 0.5s;
        }

        .input-group:nth-child(4) {
            animation-delay: 0.6s;
        }

        .input-group:nth-child(5) {
            animation-delay: 0.7s;
        }

        .input-group:nth-child(6) {
            animation-delay: 0.8s;
        }

        /* 电话输入容器 */
        .phone-input-container {
            display: flex;
            align-items: center;
            width: 100%;
            height: 56px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            overflow: visible;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .phone-input-container:focus-within {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        /* 国家代码选择器 */
        .country-selector {
            display: flex;
            align-items: center;
            padding: 0 15px;
            cursor: pointer;
            height: 100%;
            min-width: 80px;
            background: rgba(255, 255, 255, 0.05);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .country-selector:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .country-flag {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            transition: all 0.3s ease;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .country-code {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 500;
        }

        /* 输入框样式 */
        .form-input {
            width: 100%;
            height: 56px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0 15px;
            color: #fff;
            font-size: 16px;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .phone-input {
            flex: 1;
            background: transparent;
            border: none;
            outline: none;
            padding: 0 15px;
            color: #fff;
            font-size: 16px;
        }

        .phone-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* 按钮样式 */
        .btn {
            width: 100%;
            height: 56px;
            border-radius: 12px;
            border: none;
            outline: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out 0.9s both;
        }

        /* 按钮闪光效果 */
        .btn:after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s;
        }

        .btn:hover:after {
            left: 100%;
        }

        .btn:active {
            transform: scale(0.98);
        }

        .register-btn {
            background: #3a7bd5;
            color: #fff;
            box-shadow: 0 4px 15px rgba(58, 123, 213, 0.3);
        }

        .register-btn:hover {
            background: #2968c4;
            box-shadow: 0 7px 20px rgba(58, 123, 213, 0.4);
            transform: translateY(-2px);
        }

        .login-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            backdrop-filter: blur(5px);
            animation-delay: 1s;
        }

        .login-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        /* 复选框样式 */
        .checkbox-container {
            display: flex;
            align-items: center;
            margin: 20px 0;
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        .custom-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .checkmark {
            width: 18px;
            height: 18px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            display: inline-block;
            position: relative;
            transition: all 0.3s ease;
        }

        .custom-checkbox:hover .checkmark {
            background: rgba(255, 255, 255, 0.2);
        }

        .custom-checkbox input:checked ~ .checkmark {
            background: #3a7bd5;
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 3px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .custom-checkbox input:checked ~ .checkmark:after {
            display: block;
        }

        .checkbox-text {
            margin-left: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        /* 隐藏原有样式 */
        .fa {
            display: none !important;
        }

        .form-buttom,
        .form-buttom2 {
            display: none !important;
        }

        .wxtip {
            background: rgba(0,0,0,0.8);
            text-align: center;
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 999999992;
            display: none;
        }

        .imgs {
            background: url('/static_new6/img/gw_live_weixin.png') no-repeat;
            background-size: contain;
            z-index: 999999;
            min-height: 100%;
            width: 100%;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .register-card {
                margin: 10px;
                padding: 30px 20px;
            }
            
            .register-title {
                font-size: 20px;
            }
            
            .form-input,
            .phone-input-container,
            .btn {
                height: 50px;
            }
        }

        /* 覆盖原有的TikTok风格 */
        .tiktok-icon,
        .icon1, .icon2, .icon3, .icon4 {
            display: none !important;
        }

        [data-v-79134734].box,
        [data-v-79134734].main,
        [data-v-79134734].header {
            background: transparent !important;
            box-shadow: none !important;
            border-radius: 0 !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        [data-v-79134734] .Maintitle h3 {
            display: none !important;
        }

        /* 国家选择模态窗口样式 */
        .country-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #1a1a2e;
            z-index: 1000;
            display: none;
            flex-direction: column;
            color: #fff;
            overflow: hidden;
        }
        
        .country-modal-header {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .country-modal-back {
            font-size: 24px;
            color: #fff;
            margin-right: 15px;
            cursor: pointer;
        }
        
        .country-modal-title {
            flex: 1;
            text-align: center;
            font-size: 18px;
            font-weight: 500;
        }
        
        .country-modal-search {
            position: relative;
            padding: 10px 15px;
            background-color: #1a1a2e;
        }
        
        .country-modal-search input {
            width: 100%;
            background-color: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #fff;
            padding: 10px 15px;
            padding-right: 40px;
            font-size: 16px;
            outline: none;
        }
        
        .country-modal-search input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .country-modal-search .search-icon {
            position: absolute;
            right: 25px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            font-size: 18px;
        }
        
        .country-modal-list {
            flex: 1;
            overflow-y: auto;
            padding: 0;
            margin: 0;
            list-style: none;
        }
        
        .country-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
        }
        
        .country-item.selected {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .country-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        .country-item .country-flag {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 15px;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .country-item .country-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .country-item .country-name {
            color: #fff;
            font-size: 16px;
        }
        
        .country-item .country-code {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
        }
        
        .country-modal-confirm {
            padding: 15px;
            margin: 15px;
            background-color: #3a7bd5;
            color: #fff;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
        }
        
        .country-modal-confirm:hover {
            background-color: #2968c4;
        }
        
        /* 自定义滚动条 */
        .country-modal-list::-webkit-scrollbar {
            width: 5px;
        }
        
        .country-modal-list::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .country-modal-list::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
        
        .no-results {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>

<body>
    <!-- 网格覆盖层 -->
    <div class="grid-overlay"></div>
    
    <!-- 主容器 -->
    <div class="main-container">
        <!-- 注册卡片 -->
        <div class="register-card">
            <!-- 标题 -->
            <h1 class="register-title">{$Think.lang.register}</h1>
            
            <!-- 注册表单 -->
            <form class="register-form" id="registerForm">
                <!-- 电话号码输入 -->
                <div class="input-group">
                    <div class="phone-input-container">
                        <div class="country-selector" id="country_code_selector">
                            <div class="country-flag" id="selected_flag"></div>
                            <div class="country-code" id="selected_code">+86</div>
                        </div>
                        <input type="text" name="tel" placeholder="{$Think.lang.phone}" class="phone-input">
                        <input type="hidden" id="country_code" name="country_code" value="+86">
                    </div>
                </div>
                
                <!-- 用户名输入 -->
                <div class="input-group">
                    <input type="text" name="user_name" placeholder="{$Think.lang.username} (6-12 {$Think.lang.characters})" class="form-input">
                </div>
                
                <!-- 登录密码输入 -->
                <div class="input-group">
                    <input type="password" name="pwd" placeholder="{$Think.lang.password} (6-12 {$Think.lang.characters})" class="form-input">
                </div>
                
                <!-- 确认密码输入 -->
                <div class="input-group">
                    <input type="password" name="deposit_pwd" placeholder="{$Think.lang.confirm_password}" class="form-input">
                </div>
                
                <!-- 提款密码输入 -->
                <div class="input-group">
                    <input type="password" name="pwd2" placeholder="{$Think.lang.six_digit_withdrawal_password}" class="form-input">
                </div>
                
                <!-- 邀请码输入 -->
                <div class="input-group">
                    <input type="text" name="invite_code" value="{$invite_code}" <?php echo $invite_code ? 'readonly':'';  ?> placeholder="{$Think.lang.invitation_code}" class="form-input">
                </div>
                
                <!-- 同意条款复选框 -->
                <div class="checkbox-container">
                    <label class="custom-checkbox">
                        <input type="checkbox" id="agree_terms" name="agree_terms">
                        <span class="checkmark"></span>
                        <span class="checkbox-text">{$Think.lang.agree_to_terms}</span>
                    </label>
                </div>
                
                <!-- 注册按钮 -->
                <button type="button" class="btn register-btn" id="register-btn">{$Think.lang.register}</button>
                
                <!-- 登录链接 -->
                <button type="button" class="btn login-btn" id="login-btn">{$Think.lang.already_have_account}</button>
            </form>
        </div>
    </div>

    <!-- 微信提示 -->
    <div class="wxtip" id="JweixinTip">
        <div class="imgs"></div>
    </div>

    <!-- 国家选择模态窗口 -->
    <div class="country-modal" id="country_modal" style="display: none;">
        <div class="country-modal-header">
            <div class="country-modal-back" id="country_modal_back">←</div>
            <div class="country-modal-title">Select Country/Region</div>
        </div>
        <div class="country-modal-search">
            <input type="text" id="country_search" placeholder="Search country or code">
            <div class="search-icon">🔍</div>
        </div>
        <div class="country-modal-list" id="country_list"></div>
        <button class="country-modal-confirm" id="country_confirm">Confirm</button>
    </div>

    <script type="application/javascript">
        $(function(){
            // 初始化国家选择器
            initCountrySelector();
            
            var flag = true;
            var loading = null;
            
            /*检查表单*/
            function check(){
                if(!check_phone()) return false;
                
                if($("input[name=pwd]").val()==''){
                    $(document).dialog({infoText: 'Please enter login password'});
                    return false;
                }
                
                if($("input[name=deposit_pwd]").val()==''){
                    $(document).dialog({infoText: 'Please confirm password'});
                    return false;
                }
                
                if($("input[name=deposit_pwd]").val() !== $("input[name=pwd]").val()){
                    $(document).dialog({infoText: 'The passwords do not match'});
                    return false;
                }
                
                if($("input[name=pwd2]").val()==''){
                    $(document).dialog({infoText: 'Please enter withdrawal password'});
                    return false;
                }
                
                if($("input[name=invite_code]").val()==''){
                    $(document).dialog({infoText: 'Please enter invitation code'});
                    return false;
                }
                
                // 检查用户是否同意条款
                if(!$("#agree_terms").prop('checked')){
                    $(document).dialog({infoText: 'Please agree to the terms and conditions'});
                    return false;
                }
                
                return true;
            }

            /*手机号码验证*/
            function check_phone() {
                if($("input[name=tel]").val()==''){
                    $(document).dialog({infoText: 'Please enter your phone number'});
                    return false;
                }
                
                if($("input[name=user_name]").val()==''){
                    $(document).dialog({infoText: 'Please enter your username'});
                    return false;
                }
                
                var myreg=/^([0-9|A-Z|a-z]|[\u4E00-\u9FA5\uF900-\uFA2D]){2,12}$/;
                if (!myreg.test($("input[name=user_name]").val())) {
                    $(document).dialog({infoText: 'Username format incorrect'});
                    return false;
                }
                
                return true;
            }
            
            /*提交*/
            $("#register-btn").on('click',function(){
                if(check()){
                    // 显示提交动画
                    $(this).addClass('submitting');
                    
                    // 获取表单数据
                    const formData = $("#registerForm").serialize();
                    
                    $.ajax({
                        url:"{:url('do_register')}",
                        data: formData,
                        type:'POST',
                        beforeSend:function(){
                            loading = $(document).dialog({
                                type : 'notice',
                                infoIcon: '/static_new/img/loading.gif',
                                infoText: 'Processing...',
                                autoClose: 0
                            });
                        },
                        success:function(data){
                            loading.close();
                            if(data.code==0){
                                $(document).dialog({infoText: 'Registration successful!'});
                                setTimeout(function(){
                                    location.href = "{:url('user/login')}"
                                },1500);
                            }else{
                                $(document).dialog({infoText: data.info});
                                $("#register-btn").removeClass('submitting');
                            }
                        },
                        error: function(xhr, status, error) {
                            loading.close();
                            $(document).dialog({infoText: "Registration failed, please try again later"});
                            console.error("Submit failed:", error);
                            $("#register-btn").removeClass('submitting');
                        }
                    });
                }
                return false;
            });
            
            /*登录按钮点击*/
            $("#login-btn").on('click', function(){
                window.location.href = "{:url('user/login')}";
            });
        });

        function initCountrySelector() {
            // 国家代码和国旗数据 - 按国家名称A-Z排序
            const countryData = [
                { code: "+93", country: "Afghanistan", flag: "af" },
                { code: "+355", country: "Albania", flag: "al" },
                { code: "+213", country: "Algeria", flag: "dz" },
                { code: "+376", country: "Andorra", flag: "ad" },
                { code: "+244", country: "Angola", flag: "ao" },
                { code: "+54", country: "Argentina", flag: "ar" },
                { code: "+374", country: "Armenia", flag: "am" },
                { code: "+61", country: "Australia", flag: "au" },
                { code: "+43", country: "Austria", flag: "at" },
                { code: "+994", country: "Azerbaijan", flag: "az" },
                { code: "+973", country: "Bahrain", flag: "bh" },
                { code: "+880", country: "Bangladesh", flag: "bd" },
                { code: "+375", country: "Belarus", flag: "by" },
                { code: "+32", country: "Belgium", flag: "be" },
                { code: "+501", country: "Belize", flag: "bz" },
                { code: "+229", country: "Benin", flag: "bj" },
                { code: "+975", country: "Bhutan", flag: "bt" },
                { code: "+591", country: "Bolivia", flag: "bo" },
                { code: "+387", country: "Bosnia and Herzegovina", flag: "ba" },
                { code: "+267", country: "Botswana", flag: "bw" },
                { code: "+55", country: "Brazil", flag: "br" },
                { code: "+673", country: "Brunei", flag: "bn" },
                { code: "+359", country: "Bulgaria", flag: "bg" },
                { code: "+226", country: "Burkina Faso", flag: "bf" },
                { code: "+257", country: "Burundi", flag: "bi" },
                { code: "+855", country: "Cambodia", flag: "kh" },
                { code: "+237", country: "Cameroon", flag: "cm" },
                { code: "+1", country: "Canada", flag: "ca" },
                { code: "+238", country: "Cape Verde", flag: "cv" },
                { code: "+236", country: "Central African Republic", flag: "cf" },
                { code: "+235", country: "Chad", flag: "td" },
                { code: "+56", country: "Chile", flag: "cl" },
                { code: "+86", country: "China", flag: "cn" },
                { code: "+57", country: "Colombia", flag: "co" },
                { code: "+269", country: "Comoros", flag: "km" },
                { code: "+242", country: "Congo", flag: "cg" },
                { code: "+506", country: "Costa Rica", flag: "cr" },
                { code: "+385", country: "Croatia", flag: "hr" },
                { code: "+53", country: "Cuba", flag: "cu" },
                { code: "+357", country: "Cyprus", flag: "cy" },
                { code: "+420", country: "Czech Republic", flag: "cz" },
                { code: "+45", country: "Denmark", flag: "dk" },
                { code: "+253", country: "Djibouti", flag: "dj" },
                { code: "+1", country: "Dominican Republic", flag: "do" },
                { code: "+593", country: "Ecuador", flag: "ec" },
                { code: "+20", country: "Egypt", flag: "eg" },
                { code: "+503", country: "El Salvador", flag: "sv" },
                { code: "+240", country: "Equatorial Guinea", flag: "gq" },
                { code: "+291", country: "Eritrea", flag: "er" },
                { code: "+372", country: "Estonia", flag: "ee" },
                { code: "+251", country: "Ethiopia", flag: "et" },
                { code: "+679", country: "Fiji", flag: "fj" },
                { code: "+358", country: "Finland", flag: "fi" },
                { code: "+33", country: "France", flag: "fr" },
                { code: "+241", country: "Gabon", flag: "ga" },
                { code: "+220", country: "Gambia", flag: "gm" },
                { code: "+995", country: "Georgia", flag: "ge" },
                { code: "+49", country: "Germany", flag: "de" },
                { code: "+233", country: "Ghana", flag: "gh" },
                { code: "+30", country: "Greece", flag: "gr" },
                { code: "+502", country: "Guatemala", flag: "gt" },
                { code: "+224", country: "Guinea", flag: "gn" },
                { code: "+245", country: "Guinea-Bissau", flag: "gw" },
                { code: "+592", country: "Guyana", flag: "gy" },
                { code: "+509", country: "Haiti", flag: "ht" },
                { code: "+504", country: "Honduras", flag: "hn" },
                { code: "+852", country: "Hong Kong", flag: "hk" },
                { code: "+36", country: "Hungary", flag: "hu" },
                { code: "+354", country: "Iceland", flag: "is" },
                { code: "+91", country: "India", flag: "in" },
                { code: "+62", country: "Indonesia", flag: "id" },
                { code: "+964", country: "Iraq", flag: "iq" },
                { code: "+353", country: "Ireland", flag: "ie" },
                { code: "+972", country: "Israel", flag: "il" },
                { code: "+39", country: "Italy", flag: "it" },
                { code: "+1", country: "Jamaica", flag: "jm" },
                { code: "+81", country: "Japan", flag: "jp" },
                { code: "+962", country: "Jordan", flag: "jo" },
                { code: "+7", country: "Kazakhstan", flag: "kz" },
                { code: "+254", country: "Kenya", flag: "ke" },
                { code: "+965", country: "Kuwait", flag: "kw" },
                { code: "+996", country: "Kyrgyzstan", flag: "kg" },
                { code: "+856", country: "Laos", flag: "la" },
                { code: "+371", country: "Latvia", flag: "lv" },
                { code: "+961", country: "Lebanon", flag: "lb" },
                { code: "+266", country: "Lesotho", flag: "ls" },
                { code: "+231", country: "Liberia", flag: "lr" },
                { code: "+218", country: "Libya", flag: "ly" },
                { code: "+423", country: "Liechtenstein", flag: "li" },
                { code: "+370", country: "Lithuania", flag: "lt" },
                { code: "+352", country: "Luxembourg", flag: "lu" },
                { code: "+853", country: "Macau", flag: "mo" },
                { code: "+389", country: "Macedonia", flag: "mk" },
                { code: "+261", country: "Madagascar", flag: "mg" },
                { code: "+265", country: "Malawi", flag: "mw" },
                { code: "+60", country: "Malaysia", flag: "my" },
                { code: "+960", country: "Maldives", flag: "mv" },
                { code: "+223", country: "Mali", flag: "ml" },
                { code: "+356", country: "Malta", flag: "mt" },
                { code: "+222", country: "Mauritania", flag: "mr" },
                { code: "+230", country: "Mauritius", flag: "mu" },
                { code: "+52", country: "Mexico", flag: "mx" },
                { code: "+373", country: "Moldova", flag: "md" },
                { code: "+377", country: "Monaco", flag: "mc" },
                { code: "+976", country: "Mongolia", flag: "mn" },
                { code: "+382", country: "Montenegro", flag: "me" },
                { code: "+212", country: "Morocco", flag: "ma" },
                { code: "+258", country: "Mozambique", flag: "mz" },
                { code: "+95", country: "Myanmar", flag: "mm" },
                { code: "+264", country: "Namibia", flag: "na" },
                { code: "+977", country: "Nepal", flag: "np" },
                { code: "+31", country: "Netherlands", flag: "nl" },
                { code: "+64", country: "New Zealand", flag: "nz" },
                { code: "+505", country: "Nicaragua", flag: "ni" },
                { code: "+227", country: "Niger", flag: "ne" },
                { code: "+234", country: "Nigeria", flag: "ng" },
                { code: "+47", country: "Norway", flag: "no" },
                { code: "+968", country: "Oman", flag: "om" },
                { code: "+92", country: "Pakistan", flag: "pk" },
                { code: "+970", country: "Palestine", flag: "ps" },
                { code: "+507", country: "Panama", flag: "pa" },
                { code: "+675", country: "Papua New Guinea", flag: "pg" },
                { code: "+595", country: "Paraguay", flag: "py" },
                { code: "+51", country: "Peru", flag: "pe" },
                { code: "+63", country: "Philippines", flag: "ph" },
                { code: "+48", country: "Poland", flag: "pl" },
                { code: "+351", country: "Portugal", flag: "pt" },
                { code: "+974", country: "Qatar", flag: "qa" },
                { code: "+40", country: "Romania", flag: "ro" },
                { code: "+7", country: "Russia", flag: "ru" },
                { code: "+250", country: "Rwanda", flag: "rw" },
                { code: "+966", country: "Saudi Arabia", flag: "sa" },
                { code: "+221", country: "Senegal", flag: "sn" },
                { code: "+381", country: "Serbia", flag: "rs" },
                { code: "+232", country: "Sierra Leone", flag: "sl" },
                { code: "+65", country: "Singapore", flag: "sg" },
                { code: "+421", country: "Slovakia", flag: "sk" },
                { code: "+386", country: "Slovenia", flag: "si" },
                { code: "+252", country: "Somalia", flag: "so" },
                { code: "+27", country: "South Africa", flag: "za" },
                { code: "+82", country: "South Korea", flag: "kr" },
                { code: "+211", country: "South Sudan", flag: "ss" },
                { code: "+34", country: "Spain", flag: "es" },
                { code: "+94", country: "Sri Lanka", flag: "lk" },
                { code: "+249", country: "Sudan", flag: "sd" },
                { code: "+597", country: "Suriname", flag: "sr" },
                { code: "+268", country: "Swaziland", flag: "sz" },
                { code: "+46", country: "Sweden", flag: "se" },
                { code: "+41", country: "Switzerland", flag: "ch" },
                { code: "+963", country: "Syria", flag: "sy" },
                { code: "+886", country: "Taiwan", flag: "tw" },
                { code: "+992", country: "Tajikistan", flag: "tj" },
                { code: "+255", country: "Tanzania", flag: "tz" },
                { code: "+66", country: "Thailand", flag: "th" },
                { code: "+228", country: "Togo", flag: "tg" },
                { code: "+216", country: "Tunisia", flag: "tn" },
                { code: "+90", country: "Turkey", flag: "tr" },
                { code: "+993", country: "Turkmenistan", flag: "tm" },
                { code: "+256", country: "Uganda", flag: "ug" },
                { code: "+380", country: "Ukraine", flag: "ua" },
                { code: "+971", country: "United Arab Emirates", flag: "ae" },
                { code: "+44", country: "United Kingdom", flag: "gb" },
                { code: "+1", country: "United States", flag: "us" },
                { code: "+598", country: "Uruguay", flag: "uy" },
                { code: "+998", country: "Uzbekistan", flag: "uz" },
                { code: "+58", country: "Venezuela", flag: "ve" },
                { code: "+84", country: "Vietnam", flag: "vn" },
                { code: "+967", country: "Yemen", flag: "ye" },
                { code: "+260", country: "Zambia", flag: "zm" },
                { code: "+263", country: "Zimbabwe", flag: "zw" }
            ];
            
            const countrySelector = document.getElementById('country_code_selector');
            const countryModal = document.getElementById('country_modal');
            const countryList = document.getElementById('country_list');
            const countrySearch = document.getElementById('country_search');
            const modalBack = document.getElementById('country_modal_back');
            const confirmBtn = document.getElementById('country_confirm');
            const selectedFlag = document.getElementById('selected_flag');
            const selectedCode = document.getElementById('selected_code');
            const hiddenInput = document.getElementById('country_code');
            
            let selectedCountry = countryData.find(c => c.code === "+86") || countryData[0];
            let tempSelectedCountry = null;
            
            // 渲染国家列表
            function renderCountryList(filter = '') {
                countryList.innerHTML = '';
                
                const filteredCountries = countryData.filter(country => 
                    country.country.toLowerCase().includes(filter.toLowerCase()) || 
                    country.code.includes(filter)
                );
                
                if (filteredCountries.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'no-results';
                    noResults.textContent = 'No matching countries found';
                    countryList.appendChild(noResults);
                    return;
                }
                
                filteredCountries.forEach(country => {
                    const item = document.createElement('div');
                    item.className = 'country-item';
                    if (tempSelectedCountry && tempSelectedCountry.code === country.code) {
                        item.classList.add('selected');
                    }
                    
                    const flagUrl = `https://flagcdn.com/w40/${country.flag}.png`;
                    
                    item.innerHTML = `
                        <div class="country-flag">
                            <img src="${flagUrl}" alt="${country.country}" style="width: 24px; height: 18px; border-radius: 2px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="display: none; width: 24px; height: 18px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 2px; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">${country.flag.toUpperCase()}</div>
                        </div>
                        <div class="country-info">
                            <div class="country-name">${country.country}</div>
                            <div class="country-code">${country.code}</div>
                        </div>
                    `;
                    
                    item.addEventListener('click', () => {
                        // 清除之前的选中状态
                        const selected = countryList.querySelector('.selected');
                        if (selected) {
                            selected.classList.remove('selected');
                        }
                        
                        // 设置当前选中状态
                        item.classList.add('selected');
                        tempSelectedCountry = country;
                    });
                    
                    countryList.appendChild(item);
                });
            }
            
            // 打开国家选择模态窗口
            countrySelector.addEventListener('click', () => {
                tempSelectedCountry = selectedCountry;
                countryModal.style.display = 'flex';
                renderCountryList();
                
                // 滚动到当前选中的国家
                setTimeout(() => {
                    const selected = countryList.querySelector('.selected');
                    if (selected) {
                        selected.scrollIntoView({ block: 'center', behavior: 'smooth' });
                    }
                }, 100);
            });
            
            // 关闭模态窗口
            modalBack.addEventListener('click', () => {
                countryModal.style.display = 'none';
                countrySearch.value = '';
            });
            
            // 搜索功能
            countrySearch.addEventListener('input', (e) => {
                renderCountryList(e.target.value);
            });
            
            // 确认选择
            confirmBtn.addEventListener('click', () => {
                if (tempSelectedCountry) {
                    selectedCountry = tempSelectedCountry;
                    
                    // 更新显示
                    const flagUrl = `https://flagcdn.com/w40/${selectedCountry.flag}.png`;
                    selectedFlag.innerHTML = `
                        <img src="${flagUrl}" alt="${selectedCountry.country}" style="width: 24px; height: 18px; border-radius: 2px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div style="display: none; width: 24px; height: 18px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 2px; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">${selectedCountry.flag.toUpperCase()}</div>
                    `;
                    selectedCode.textContent = selectedCountry.code;
                    
                    // 更新隐藏输入框的值
                    hiddenInput.value = selectedCountry.code;
                }
                
                // 关闭模态窗口
                countryModal.style.display = 'none';
                countrySearch.value = '';
            });
            
            // 初始化选中的国家显示
            const flagUrl = `https://flagcdn.com/w40/${selectedCountry.flag}.png`;
            selectedFlag.innerHTML = `
                <img src="${flagUrl}" alt="${selectedCountry.country}" style="width: 24px; height: 18px; border-radius: 2px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div style="display: none; width: 24px; height: 18px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; border-radius: 2px; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">${selectedCountry.flag.toUpperCase()}</div>
            `;
            selectedCode.textContent = selectedCountry.code;
            hiddenInput.value = selectedCountry.code;
        }

    </script>

    <script>
        $(function () {
            var u = navigator.userAgent, app = navigator.appVersion;
            var ua = navigator.userAgent.toLowerCase();
            var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
            var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端

            if(ua.match(/MicroMessenger/i)=="micromessenger") {
                $('#JweixinTip').show();
                document.getElementById('JweixinTip').style.display='block';
            }
        });
    </script>
</body>
</html>