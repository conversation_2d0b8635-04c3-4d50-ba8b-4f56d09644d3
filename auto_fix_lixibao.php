<?php
// 自动修复利息宝收益脚本 - 无需交互
// 直接补发昨天的收益

echo "=== 利息宝收益自动修复 ===\n";
echo "修复时间: " . date('Y-m-d H:i:s') . "\n";

// 数据库配置
$host = '127.0.0.1';
$port = 3306;
$database = 'g5_vt1685_site';
$username = 'g5_vt1685_site';
$password = 'g5_vt1685_site';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n";
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

// 计算昨天的时间范围
$yesterday_start = strtotime(date('Y-m-d', strtotime('-1 day')));
$yesterday_end = $yesterday_start + 86400;
$yesterday_date = date('Y-m-d', strtotime('-1 day'));

echo "修复日期: $yesterday_date\n";

// 检查昨天是否已有收益记录
$stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime BETWEEN $yesterday_start AND $yesterday_end");
$existing_income = $stmt->fetchColumn();

if ($existing_income > 0) {
    echo "⚠️  昨天已有 $existing_income 条收益记录，跳过修复\n";
    exit;
}

// 查找需要补发收益的用户
$stmt = $pdo->query("
    SELECT DISTINCT l.uid, u.username,
           COUNT(l.id) as investment_count,
           SUM(l.num) as total_investment
    FROM xy_lixibao l
    LEFT JOIN xy_users u ON l.uid = u.id
    WHERE l.type = 1 AND l.status = 1 
    AND l.addtime <= $yesterday_end 
    AND l.endtime > $yesterday_start
    GROUP BY l.uid
    ORDER BY l.uid
");

$users_to_fix = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "找到 " . count($users_to_fix) . " 个用户需要补发收益\n";

if (empty($users_to_fix)) {
    echo "没有找到需要补发收益的用户\n";
    exit;
}

// 获取产品信息
$stmt = $pdo->query("SELECT * FROM xy_lixibao_list ORDER BY id");
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);
$product_info = [];
foreach ($products as $product) {
    $product_info[$product['id']] = [
        'name' => isset($product['title']) ? $product['title'] : (isset($product['name']) ? $product['name'] : '利息宝'),
        'rate' => isset($product['rate']) ? $product['rate'] : (isset($product['bili']) ? $product['bili'] : 0.05)
    ];
}

// 默认产品信息
$default_product = [
    'name' => '利息宝',
    'rate' => 0.05 // 5%
];

// 开始修复
$total_fixed = 0;
$total_income = 0;

echo "开始补发收益...\n";

foreach ($users_to_fix as $user) {
    $uid = $user['uid'];
    $username = $user['username'];
    
    // 获取该用户昨天的投资详情
    $stmt = $pdo->prepare("
        SELECT l.*, ll.rate, ll.bili, ll.title, ll.name
        FROM xy_lixibao l
        LEFT JOIN xy_lixibao_list ll ON l.product_id = ll.id
        WHERE l.uid = ? AND l.type = 1 AND l.status = 1 
        AND l.addtime <= ? AND l.endtime > ?
    ");
    $stmt->execute([$uid, $yesterday_end, $yesterday_start]);
    $user_investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $user_total_income = 0;
    
    // 按产品计算收益
    $product_amounts = [];
    foreach ($user_investments as $investment) {
        $product_id = $investment['product_id'] ? $investment['product_id'] : 1;
        
        if (!isset($product_amounts[$product_id])) {
            $product_amounts[$product_id] = 0;
        }
        $product_amounts[$product_id] += $investment['num'];
    }
    
    // 计算每个产品的收益
    foreach ($product_amounts as $product_id => $amount) {
        $product = isset($product_info[$product_id]) ? $product_info[$product_id] : $default_product;
        $rate = $product['rate'];
        $product_income = $amount * $rate;
        $user_total_income += $product_income;
        
        // 插入利息宝收益记录
        $stmt = $pdo->prepare("
            INSERT INTO xy_lixibao (uid, num, addtime, type, status, yuji_num, real_num, is_sy, sid, shouxu, bili, day, update_time)
            VALUES (?, ?, ?, 3, 1, ?, ?, 1, ?, 0, ?, 1, ?)
        ");
        $stmt->execute([
            $uid,
            $product_income,
            $yesterday_start + 3600, // 昨天凌晨1点
            $product_income,
            $product_income,
            $product_id,
            $rate,
            $yesterday_start + 3600
        ]);
    }
    
    if ($user_total_income > 0) {
        // 更新用户余额
        $stmt = $pdo->prepare("UPDATE xy_users SET balance = balance + ? WHERE id = ?");
        $stmt->execute([$user_total_income, $uid]);
        
        // 插入余额变动记录
        $oid = 'LXB' . date('YmdHis') . sprintf('%04d', $uid);
        $stmt = $pdo->prepare("
            INSERT INTO xy_balance_log (uid, oid, num, type, status, addtime, remark)
            VALUES (?, ?, ?, 23, 1, ?, ?)
        ");
        $stmt->execute([
            $uid,
            $oid,
            $user_total_income,
            $yesterday_start + 3600,
            "利息宝收益补发 - $yesterday_date"
        ]);
        
        echo "✅ 用户 $username (ID: $uid) 补发收益: ¥$user_total_income\n";
        $total_fixed++;
        $total_income += $user_total_income;
    }
}

// 输出修复结果
echo "\n=== 修复完成 ===\n";
echo "修复用户数: $total_fixed\n";
echo "总补发金额: ¥$total_income\n";
echo "修复日期: $yesterday_date\n";

// 保存日志
$log_content = "利息宝收益自动修复日志\n";
$log_content .= "修复时间: " . date('Y-m-d H:i:s') . "\n";
$log_content .= "修复日期: $yesterday_date\n";
$log_content .= "修复用户数: $total_fixed\n";
$log_content .= "总补发金额: ¥$total_income\n";

file_put_contents('lixibao_auto_fix.log', $log_content, FILE_APPEND);
echo "修复日志已保存\n";

echo "修复完成！\n";
?> 