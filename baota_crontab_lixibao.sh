#!/bin/bash
#
# 宝塔计划任务专用脚本 - 利息宝每日收益计算
# 使用方法：在宝塔面板计划任务中选择Shell脚本，将此脚本内容复制进去
#

# 设置日志文件路径
LOG_FILE="/tmp/lixibao_cron.log"
RESULT_FILE="/tmp/lixibao_result.html"

# 获取当前时间
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 记录开始执行
echo "[$DATE] ========== 开始执行利息宝收益计算 ==========" >> $LOG_FILE

# URL地址
URL="https://tiktokpro.org/index/crontab/lixibao_js"

# 执行HTTP请求 - 方法1：curl命令
echo "[$DATE] 使用curl命令请求: $URL" >> $LOG_FILE

curl -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" \
     -H "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8" \
     -H "Accept-Language: zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3" \
     -H "Accept-Encoding: gzip, deflate" \
     -H "Cache-Control: no-cache" \
     -H "Pragma: no-cache" \
     -s -w "HTTP状态码: %{http_code}, 总时间: %{time_total}s\n" \
     -o "$RESULT_FILE" \
     "$URL"

# 检查curl执行结果
CURL_EXIT_CODE=$?
if [ $CURL_EXIT_CODE -eq 0 ]; then
    echo "[$DATE] curl命令执行成功" >> $LOG_FILE
    
    # 检查返回内容
    if [ -f "$RESULT_FILE" ]; then
        # 提取关键信息
        PROCESSED_USERS=$(grep -o '处理用户数[：:][[:space:]]*[0-9]*' "$RESULT_FILE" | grep -o '[0-9]*$')
        TOTAL_INCOME=$(grep -o '总收益金额[：:][[:space:]]*[0-9.]*' "$RESULT_FILE" | grep -o '[0-9.]*$')
        
        if [ ! -z "$PROCESSED_USERS" ]; then
            echo "[$DATE] ✅ 执行成功 - 处理用户数: $PROCESSED_USERS, 总收益: $TOTAL_INCOME" >> $LOG_FILE
        else
            echo "[$DATE] ⚠️ 响应格式异常，无法提取处理结果" >> $LOG_FILE
            echo "[$DATE] 响应内容前200字符:" >> $LOG_FILE
            head -c 200 "$RESULT_FILE" >> $LOG_FILE 2>/dev/null
            echo "" >> $LOG_FILE
        fi
        
        # 检查是否有错误信息
        if grep -q "错误\|失败\|异常\|error\|Error\|ERROR" "$RESULT_FILE"; then
            echo "[$DATE] ❌ 响应中包含错误信息:" >> $LOG_FILE
            grep -i "错误\|失败\|异常\|error" "$RESULT_FILE" >> $LOG_FILE 2>/dev/null
        fi
        
        # 保存完整响应（用于调试）
        echo "[$DATE] 完整响应已保存到: $RESULT_FILE" >> $LOG_FILE
        
    else
        echo "[$DATE] ❌ 响应文件未生成" >> $LOG_FILE
    fi
    
else
    echo "[$DATE] ❌ curl命令执行失败，退出码: $CURL_EXIT_CODE" >> $LOG_FILE
    
    # 如果curl失败，尝试使用wget
    echo "[$DATE] 尝试使用wget命令..." >> $LOG_FILE
    wget -q -O "$RESULT_FILE" \
         --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \
         --header="Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" \
         --timeout=30 \
         "$URL"
         
    WGET_EXIT_CODE=$?
    if [ $WGET_EXIT_CODE -eq 0 ]; then
        echo "[$DATE] ✅ wget命令执行成功" >> $LOG_FILE
        
        # 同样检查返回内容
        if [ -f "$RESULT_FILE" ]; then
            PROCESSED_USERS=$(grep -o '处理用户数[：:][[:space:]]*[0-9]*' "$RESULT_FILE" | grep -o '[0-9]*$')
            if [ ! -z "$PROCESSED_USERS" ]; then
                echo "[$DATE] wget结果 - 处理用户数: $PROCESSED_USERS" >> $LOG_FILE
            fi
        fi
    else
        echo "[$DATE] ❌ wget命令也执行失败，退出码: $WGET_EXIT_CODE" >> $LOG_FILE
    fi
fi

# 记录结束时间
END_DATE=$(date '+%Y-%m-%d %H:%M:%S')
echo "[$END_DATE] ========== 执行完成 ==========" >> $LOG_FILE
echo "" >> $LOG_FILE

# 清理旧的日志文件（保留最近7天）
find /tmp -name "lixibao_cron.log*" -mtime +7 -delete 2>/dev/null

# 如果日志文件太大，进行轮转
if [ -f "$LOG_FILE" ] && [ $(stat -c%s "$LOG_FILE" 2>/dev/null || echo 0) -gt 1048576 ]; then
    mv "$LOG_FILE" "${LOG_FILE}.$(date +%Y%m%d_%H%M%S)"
    echo "[$DATE] 日志文件已轮转" > "$LOG_FILE"
fi

# 可选：发送结果到指定邮箱或webhook（如果需要的话）
# 这里可以添加邮件通知或其他通知方式

exit 0 