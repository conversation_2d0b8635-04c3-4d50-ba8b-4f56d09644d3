<?php
/**
 * 独立补发收益脚本
 * 直接运行：php bufa_shouyi.php
 * 或通过浏览器访问：http://您的域名/bufa_shouyi.php?start_date=2025-05-19&end_date=2025-05-22
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

require __DIR__ . '/thinkphp/base.php';

use think\Db;

// 辅助函数：生成订单号
function getSn($prefix = '') {
    return $prefix . date('ymdHis') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
}

// 辅助函数：写入日志
function writeLog($message) {
    $log_file = __DIR__ . '/runtime/log/bufa_shouyi.log';
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $message\n", FILE_APPEND);
    echo date('Y-m-d H:i:s') . " - $message<br>\n";
}

try {
    // 获取参数
    $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-7 days'));
    $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
    
    writeLog("开始补发收益：{$start_date} 到 {$end_date}");
    
    // 转换日期为时间戳
    $start_timestamp = strtotime($start_date . ' 00:00:00');
    $end_timestamp = strtotime($end_date . ' 23:59:59');
    
    if ($start_timestamp >= $end_timestamp) {
        writeLog("错误：开始日期必须小于结束日期");
        exit;
    }
    
    // 生成需要检查的日期列表
    $check_dates = [];
    $current = $start_timestamp;
    while ($current <= $end_timestamp) {
        $check_dates[] = [
            'date' => date('Y-m-d', $current),
            'start' => $current,
            'end' => $current + 86399 // 当天23:59:59
        ];
        $current += 86400; // 增加一天
    }
    
    writeLog("需要检查的日期数量：" . count($check_dates));
    
    // 获取在检查期间内有投资记录的所有用户
    $users_with_investments = Db::name('xy_lixibao')
        ->where('type', 1) // 转入类型
        ->where('is_qu', 0) // 未取出
        ->where('addtime', '<=', $end_timestamp) // 投资时间在检查期间之前或期间内
        ->where('endtime', '>=', $start_timestamp) // 到期时间在检查期间之后或期间内
        ->field('uid')
        ->group('uid')
        ->select();
        
    writeLog("找到需要检查的用户数量：" . count($users_with_investments));
    
    $processed_users = 0;
    $missing_days = 0;
    $total_bufa_income = 0;
    
    foreach ($users_with_investments as $user_item) {
        $uid = $user_item['uid'];
        $username = Db::name('xy_users')->where('id', $uid)->value('username');
        
        writeLog("开始处理用户：$username (ID: $uid)");
        
        $user_missing_days = 0;
        $user_total_income = 0;
        
        foreach ($check_dates as $date_info) {
            $check_date = $date_info['date'];
            $day_start = $date_info['start'];
            $day_end = $date_info['end'];
            
            // 检查该用户在这一天是否已经有收益记录
            $existing_income = Db::name('xy_balance_log')
                ->where('uid', $uid)
                ->where('type', 23) // 收益类型
                ->where('status', 1)
                ->where('addtime', 'between', [$day_start, $day_end])
                ->find();
                
            if ($existing_income) {
                // 已有收益记录，跳过
                continue;
            }
            
            // 获取该用户在这一天的有效投资记录
            $user_investments = Db::name('xy_lixibao')
                ->where('uid', $uid)
                ->where('type', 1) // 转入类型
                ->where('is_qu', 0) // 未取出
                ->where('addtime', '<=', $day_end) // 投资时间在这一天之前或当天
                ->where('endtime', '>', $day_start) // 到期时间在这一天之后
                ->select();
                
            if (empty($user_investments)) {
                // 该用户在这一天没有有效投资，跳过
                continue;
            }
            
            // 按产品分组计算收益
            $product_amounts = [];
            $product_rates = [];
            $product_names = [];
            
            foreach ($user_investments as $investment) {
                $product_id = $investment['sid'];
                
                if (!isset($product_amounts[$product_id])) {
                    $product_amounts[$product_id] = 0;
                    
                    // 获取产品信息
                    $product_info = Db::name('xy_lixibao_list')
                        ->where('id', $product_id)
                        ->field('name, bili')
                        ->find();
                        
                    if ($product_info) {
                        $product_names[$product_id] = $product_info['name'];
                        $product_rates[$product_id] = $product_info['bili'];
                    } else {
                        $product_names[$product_id] = "产品#$product_id";
                        $product_rates[$product_id] = 0.05; // 默认5%
                    }
                }
                
                $product_amounts[$product_id] += $investment['num'];
            }
            
            // 计算这一天的总收益
            $day_total_income = 0;
            $day_details = [];
            
            foreach ($product_amounts as $product_id => $amount) {
                if ($amount <= 0) continue;
                
                $rate = $product_rates[$product_id];
                $product_income = $amount * $rate;
                $day_total_income += $product_income;
                
                $day_details[] = [
                    'product_id' => $product_id,
                    'product_name' => $product_names[$product_id],
                    'amount' => $amount,
                    'rate' => $rate,
                    'income' => $product_income
                ];
            }
            
            if ($day_total_income > 0) {
                // 需要补发收益
                $user_missing_days++;
                $user_total_income += $day_total_income;
                
                // 使用当天的时间戳（中午12点）作为补发时间
                $bufa_time = $day_start + 43200; // 12:00:00
                
                try {
                    // 开始事务
                    Db::startTrans();
                    
                    // 更新用户余额
                    Db::name('xy_users')->where('id', $uid)->setInc('balance', $day_total_income);
                    
                    // 为每个产品添加收益记录到xy_lixibao表
                    foreach ($day_details as $detail) {
                        Db::name('xy_lixibao')->insert([
                            'uid'         => $uid,
                            'num'         => $detail['income'],
                            'addtime'     => $bufa_time,
                            'type'        => 3,
                            'status'      => 1,
                            'yuji_num'    => $detail['income'],
                            'real_num'    => $detail['income'],
                            'is_sy'       => 1,
                            'sid'         => $detail['product_id'],
                            'shouxu'      => 0,
                            'bili'        => $detail['rate'],
                            'day'         => 1,
                            'update_time' => $bufa_time,
                        ]);
                    }
                    
                    // 添加余额变动记录
                    $oid = getSn('FIX');
                    Db::name('xy_balance_log')->insert([
                        'uid'       => $uid,
                        'oid'       => $oid,
                        'num'       => $day_total_income,
                        'type'      => 23,
                        'status'    => 1,
                        'addtime'   => $bufa_time
                    ]);
                    
                    // 提交事务
                    Db::commit();
                    
                    writeLog("补发收益成功：用户 $username (ID: $uid), 日期: $check_date, 金额: $day_total_income");
                    
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    $error_msg = "补发收益失败: 用户 $username (ID: $uid), 日期: $check_date, 错误: " . $e->getMessage();
                    writeLog($error_msg);
                }
            }
        }
        
        if ($user_missing_days > 0) {
            $processed_users++;
            $missing_days += $user_missing_days;
            $total_bufa_income += $user_total_income;
            
            writeLog("用户 $username 处理完成：缺失天数 $user_missing_days，补发金额 $user_total_income");
        }
    }
    
    writeLog("补发收益完成");
    writeLog("处理用户数：$processed_users");
    writeLog("补发天数：$missing_days");
    writeLog("补发总金额：$total_bufa_income");
    
    // 输出HTML结果
    echo "<h2>补发收益完成</h2>";
    echo "<p>检查期间：$start_date 到 $end_date</p>";
    echo "<p>处理用户数：$processed_users</p>";
    echo "<p>补发天数：$missing_days</p>";
    echo "<p>补发总金额：$total_bufa_income</p>";
    
} catch (\Exception $e) {
    writeLog("执行出错：" . $e->getMessage());
    echo "<h2>执行出错</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?> 