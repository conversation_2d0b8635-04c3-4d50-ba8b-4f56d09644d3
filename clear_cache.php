<?php
/**
 * 缓存清理脚本
 * 用于清除ThinkPHP的缓存文件
 */

// 显示所有错误
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>缓存清理工具</h1>";
echo "<p>开始清理缓存文件...</p>";

// 定义需要清理的目录
$cache_dirs = [
    'runtime/cache/' => '编译缓存',
    'runtime/temp/' => '临时文件',
    'runtime/log/' => '日志文件（可选）'
];

$total_deleted = 0;
$total_size = 0;

foreach ($cache_dirs as $dir => $description) {
    echo "<h2>清理 {$description} ({$dir})</h2>";
    
    if (!is_dir($dir)) {
        echo "<p style='color:orange'>⚠️ 目录不存在: {$dir}</p>";
        continue;
    }
    
    // 获取目录中的所有文件
    $files = glob($dir . '*');
    $dir_count = 0;
    $dir_size = 0;
    
    if (empty($files)) {
        echo "<p style='color:green'>✅ 目录已经是空的</p>";
        continue;
    }
    
    foreach ($files as $file) {
        if (is_file($file)) {
            $file_size = filesize($file);
            $dir_size += $file_size;
            
            // 对于日志文件，只删除7天前的文件
            if (strpos($dir, 'log') !== false) {
                $file_time = filemtime($file);
                $seven_days_ago = time() - (7 * 24 * 60 * 60);
                
                if ($file_time > $seven_days_ago) {
                    echo "<p style='color:blue'>保留最近的日志文件: " . basename($file) . "</p>";
                    continue;
                }
            }
            
            if (unlink($file)) {
                $dir_count++;
                echo "<p style='color:green'>删除: " . basename($file) . " (" . formatBytes($file_size) . ")</p>";
            } else {
                echo "<p style='color:red'>删除失败: " . basename($file) . "</p>";
            }
        } elseif (is_dir($file)) {
            // 递归删除子目录
            $sub_deleted = deleteDirectory($file);
            if ($sub_deleted > 0) {
                $dir_count += $sub_deleted;
                echo "<p style='color:green'>删除目录: " . basename($file) . " ({$sub_deleted} 个文件)</p>";
            }
        }
    }
    
    $total_deleted += $dir_count;
    $total_size += $dir_size;
    
    echo "<p><strong>本目录删除: {$dir_count} 个文件，释放空间: " . formatBytes($dir_size) . "</strong></p>";
}

echo "<h2>清理完成</h2>";
echo "<div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50;'>";
echo "<p><strong>总计删除:</strong> {$total_deleted} 个文件</p>";
echo "<p><strong>释放空间:</strong> " . formatBytes($total_size) . "</p>";
echo "</div>";

echo "<h2>建议操作</h2>";
echo "<div style='background-color: #fff3e0; padding: 15px; border-left: 4px solid #ff9800;'>";
echo "<ol>";
echo "<li>现在可以访问 <a href='/index/ctrl/recharge_admin' target='_blank'>/index/ctrl/recharge_admin</a> 测试充值记录页面</li>";
echo "<li>如果仍有问题，请重启Web服务器（Apache/Nginx）</li>";
echo "<li>检查PHP错误日志以获取更多信息</li>";
echo "</ol>";
echo "</div>";

/**
 * 格式化字节大小
 */
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * 递归删除目录
 */
function deleteDirectory($dir) {
    $deleted = 0;
    
    if (!is_dir($dir)) {
        return 0;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        
        if (is_dir($path)) {
            $deleted += deleteDirectory($path);
        } else {
            if (unlink($path)) {
                $deleted++;
            }
        }
    }
    
    // 删除空目录
    if (rmdir($dir)) {
        // 目录本身不计入文件数
    }
    
    return $deleted;
}

echo "<p style='margin-top: 30px;'><a href='test_recharge_admin_final.php'>→ 运行最终测试脚本</a></p>";
?>
