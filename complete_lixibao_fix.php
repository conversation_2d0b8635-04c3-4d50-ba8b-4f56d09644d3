<?php
/**
 * 完整的利息宝收益修复脚本
 * 功能：
 * 1. 自动检测缺失的收益记录并补发
 * 2. 正确计算每日收益直到投资到期
 * 3. 可用作改进的定时任务
 * 
 * 使用方法：
 * - 直接运行：php complete_lixibao_fix.php
 * - 浏览器访问：http://您的域名/complete_lixibao_fix.php
 * - 指定日期范围：?start_date=2025-05-19&end_date=2025-05-22&mode=backfill
 * - 今日收益计算：?mode=daily
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

require __DIR__ . '/thinkphp/base.php';

use think\Db;

// 辅助函数：生成订单号
function getSn($prefix = '') {
    return $prefix . date('ymdHis') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
}

// 辅助函数：写入日志
function writeLog($message, $log_type = 'main') {
    $log_file = __DIR__ . "/runtime/log/lixibao_complete_fix_{$log_type}.log";
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $message\n", FILE_APPEND);
    echo date('Y-m-d H:i:s') . " - $message<br>\n";
    flush();
}

try {
    // 获取参数
    $mode = isset($_GET['mode']) ? $_GET['mode'] : 'auto';
    $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
    $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
    
    writeLog("开始执行利息宝收益修复，模式: $mode");
    
    $current_time = time();
    $today_date = date('Y-m-d', $current_time);
    
    // 根据模式确定处理范围
    if ($mode === 'daily') {
        // 仅处理今日收益
        $process_dates = [date('Y-m-d', $current_time)];
        writeLog("模式：每日收益计算");
    } elseif ($mode === 'backfill' && $start_date && $end_date) {
        // 补发指定日期范围的收益
        $start_timestamp = strtotime($start_date);
        $end_timestamp = strtotime($end_date);
        
        if ($start_timestamp >= $end_timestamp) {
            throw new Exception("开始日期必须小于结束日期");
        }
        
        $process_dates = [];
        $current = $start_timestamp;
        while ($current <= $end_timestamp) {
            $process_dates[] = date('Y-m-d', $current);
            $current += 86400;
        }
        writeLog("模式：补发收益，日期范围: $start_date 到 $end_date，共 " . count($process_dates) . " 天");
    } else {
        // 自动模式：检查最近7天是否有缺失的收益
        $process_dates = [];
        for ($i = 6; $i >= 0; $i--) {
            $process_dates[] = date('Y-m-d', strtotime("-$i days"));
        }
        writeLog("模式：自动检查最近7天");
    }
    
    $total_processed_users = 0;
    $total_processed_days = 0;
    $total_income = 0;
    
    foreach ($process_dates as $process_date) {
        $day_start = strtotime($process_date . ' 00:00:00');
        $day_end = strtotime($process_date . ' 23:59:59');
        
        writeLog("=== 处理日期: $process_date ===");
        
        // 如果是未来日期，跳过
        if ($day_start > $current_time) {
            writeLog("跳过未来日期: $process_date");
            continue;
        }
        
        // 获取在该日期有活跃投资的用户
        $users_with_investments = Db::name('xy_lixibao')
            ->alias('xl')
            ->leftJoin('xy_users u', 'u.id=xl.uid')
            ->where('xl.type', 1) // 转入类型
            ->where('xl.is_qu', 0) // 未取出
            ->where('xl.addtime', '<=', $day_end) // 投资时间在该日期之前或当天
            ->where('xl.endtime', '>', $day_start) // 到期时间在该日期之后
            ->field('u.id, u.username')
            ->group('u.id')
            ->select();
        
        writeLog("找到在 $process_date 有活跃投资的用户: " . count($users_with_investments) . " 个");
        
        $day_processed_users = 0;
        $day_income = 0;
        
        foreach ($users_with_investments as $user_item) {
            $uid = $user_item['id'];
            $username = $user_item['username'];
            
            // 检查该用户在该日期是否已经有收益记录
            $existing_income = Db::name('xy_balance_log')
                ->where('uid', $uid)
                ->where('type', 23) // 收益类型
                ->where('status', 1)
                ->where('addtime', '>=', $day_start)
                ->where('addtime', '<=', $day_end)
                ->find();
            
            if ($existing_income) {
                // 该日期已有收益记录，跳过
                continue;
            }
            
            // 获取该用户在该日期的有效投资记录
            $user_investments = Db::name('xy_lixibao')
                ->where('uid', $uid)
                ->where('type', 1) // 转入类型
                ->where('is_qu', 0) // 未取出
                ->where('addtime', '<=', $day_end) // 投资时间在该日期之前或当天
                ->where('endtime', '>', $day_start) // 到期时间在该日期之后
                ->select();
            
            if (empty($user_investments)) {
                // 该用户在该日期没有有效投资，跳过
                continue;
            }
            
            writeLog("处理用户: $username (ID: $uid)，在 $process_date 有 " . count($user_investments) . " 条有效投资");
            
            // 按产品分组计算收益
            $product_amounts = [];
            $product_rates = [];
            $product_names = [];
            
            foreach ($user_investments as $investment) {
                $product_id = $investment['sid'];
                
                if (!isset($product_amounts[$product_id])) {
                    $product_amounts[$product_id] = 0;
                    
                    // 获取产品信息
                    $product_info = Db::name('xy_lixibao_list')
                        ->where('id', $product_id)
                        ->field('name, bili')
                        ->find();
                        
                    if ($product_info) {
                        $product_names[$product_id] = $product_info['name'];
                        $product_rates[$product_id] = $product_info['bili'];
                    } else {
                        $product_names[$product_id] = "产品#$product_id";
                        $product_rates[$product_id] = 0.05; // 默认5%
                    }
                }
                
                $product_amounts[$product_id] += $investment['num'];
            }
            
            // 计算该日期的总收益
            $day_total_income = 0;
            $day_details = [];
            
            foreach ($product_amounts as $product_id => $amount) {
                if ($amount <= 0) continue;
                
                $rate = $product_rates[$product_id];
                $product_income = $amount * $rate;
                $day_total_income += $product_income;
                
                $day_details[] = [
                    'product_id' => $product_id,
                    'product_name' => $product_names[$product_id],
                    'amount' => $amount,
                    'rate' => $rate,
                    'income' => $product_income
                ];
            }
            
            if ($day_total_income > 0) {
                // 需要发放收益
                $income_time = $day_start + 43200; // 使用当天中午12点的时间戳
                
                try {
                    // 开始事务
                    Db::startTrans();
                    
                    // 更新用户余额
                    Db::name('xy_users')->where('id', $uid)->setInc('balance', $day_total_income);
                    
                    // 为每个产品添加收益记录到xy_lixibao表
                    foreach ($day_details as $detail) {
                        Db::name('xy_lixibao')->insert([
                            'uid'         => $uid,
                            'num'         => $detail['income'],
                            'addtime'     => $income_time,
                            'type'        => 3,
                            'status'      => 1,
                            'yuji_num'    => $detail['income'],
                            'real_num'    => $detail['income'],
                            'is_sy'       => 1,
                            'sid'         => $detail['product_id'],
                            'shouxu'      => 0,
                            'bili'        => $detail['rate'],
                            'day'         => 1,
                            'update_time' => $income_time,
                        ]);
                    }
                    
                    // 添加余额变动记录
                    $oid = getSn('FIX');
                    Db::name('xy_balance_log')->insert([
                        'uid'       => $uid,
                        'oid'       => $oid,
                        'num'       => $day_total_income,
                        'type'      => 23,
                        'status'    => 1,
                        'addtime'   => $income_time
                    ]);
                    
                    // 提交事务
                    Db::commit();
                    
                    writeLog("成功发放收益: 用户 $username (ID: $uid), 日期: $process_date, 金额: $day_total_income");
                    
                    $day_processed_users++;
                    $day_income += $day_total_income;
                    
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    writeLog("发放收益失败: 用户 $username (ID: $uid), 日期: $process_date, 错误: " . $e->getMessage());
                }
            }
        }
        
        if ($day_processed_users > 0) {
            $total_processed_days++;
            $total_processed_users += $day_processed_users;
            $total_income += $day_income;
            
            writeLog("$process_date 处理完成: 用户数 $day_processed_users，收益金额 $day_income");
        } else {
            writeLog("$process_date 无需处理收益");
        }
    }
    
    writeLog("=== 修复完成 ===");
    writeLog("处理天数: $total_processed_days");
    writeLog("受益用户数: $total_processed_users");
    writeLog("发放总收益: $total_income");
    
    // 输出HTML结果
    echo "<h2>利息宝收益修复完成</h2>";
    echo "<div style='background-color:#d4edda; padding:15px; margin:10px 0; border:1px solid #c3e6cb;'>";
    echo "<h3>执行结果:</h3>";
    echo "<p><strong>模式:</strong> $mode</p>";
    echo "<p><strong>处理天数:</strong> $total_processed_days</p>";
    echo "<p><strong>受益用户数:</strong> $total_processed_users</p>";
    echo "<p><strong>发放总收益:</strong> $total_income 元</p>";
    echo "</div>";
    
    if ($total_processed_users > 0) {
        echo "<div style='background-color:#d1ecf1; padding:15px; margin:10px 0; border:1px solid #bee5eb;'>";
        echo "<h3>修复详情:</h3>";
        echo "<p>系统已成功为 $total_processed_users 个用户补发了缺失的收益。</p>";
        echo "<p>这些收益按照每个产品的投资金额和利率正确计算。</p>";
        echo "<p>所有操作都已记录在日志中，可查看详细信息。</p>";
        echo "</div>";
    }
    
    // 显示问题原因和解决方案
    echo "<div style='background-color:#fff3cd; padding:15px; margin:10px 0; border:1px solid #ffeaa7;'>";
    echo "<h3>问题分析:</h3>";
    echo "<p><strong>原因:</strong> 系统的定时任务可能未正常运行，或者存在逻辑错误导致收益计算中断。</p>";
    echo "<p><strong>表现:</strong> 用户投资15天期限的产品，应该每天都有收益，但只计算了1天就停止。</p>";
    echo "<p><strong>解决:</strong> 本脚本已修复缺失的收益记录，确保投资期间每天都有正确的收益。</p>";
    echo "</div>";
    
    echo "<div style='background-color:#f8f9fa; padding:15px; margin:10px 0; border:1px solid #dee2e6;'>";
    echo "<h3>使用说明:</h3>";
    echo "<ul>";
    echo "<li><strong>每日运行:</strong> <a href='?mode=daily' target='_blank'>complete_lixibao_fix.php?mode=daily</a></li>";
    echo "<li><strong>补发指定日期:</strong> complete_lixibao_fix.php?mode=backfill&start_date=2025-05-19&end_date=2025-05-22</li>";
    echo "<li><strong>自动检查:</strong> <a href='?mode=auto' target='_blank'>complete_lixibao_fix.php?mode=auto</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background-color:#e9ecef; padding:15px; margin:10px 0; border:1px solid #ced4da;'>";
    echo "<h3>建议的定时任务:</h3>";
    echo "<pre>";
    echo "# 每天凌晨1点执行收益计算\n";
    echo "0 1 * * * cd " . __DIR__ . " && php complete_lixibao_fix.php?mode=daily >> " . __DIR__ . "/logs/daily_cron.log 2>&1\n\n";
    echo "# 每周日凌晨2点执行自动检查和补发\n";
    echo "0 2 * * 0 cd " . __DIR__ . " && php complete_lixibao_fix.php?mode=auto >> " . __DIR__ . "/logs/weekly_check.log 2>&1\n";
    echo "</pre>";
    echo "</div>";
    
    echo "<p><a href='debug_lixibao_issue.php' target='_blank'>查看详细调试信息</a></p>";
    
} catch (\Exception $e) {
    writeLog("执行出错：" . $e->getMessage());
    echo "<h2>执行出错</h2>";
    echo "<div style='background-color:#f8d7da; padding:15px; margin:10px 0; border:1px solid #f5c6cb;'>";
    echo "<p><strong>错误信息:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<p>生成时间: " . date('Y-m-d H:i:s') . "</p>";
?> 