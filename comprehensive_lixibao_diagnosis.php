<?php
// 加载ThinkPHP框架
require_once __DIR__ . '/thinkphp/base.php';

// 利息宝全面诊断脚本
class LixibaoComprehensiveDiagnosis
{
    private $db;
    
    public function __construct()
    {
        $this->db = \think\Db::connect();
        $this->log("=== 利息宝全面诊断开始 ===");
        $this->log("诊断时间: " . date('Y-m-d H:i:s'));
    }
    
    /**
     * 运行全面诊断
     */
    public function runFullDiagnosis()
    {
        $this->checkDatabaseTables();
        $this->checkInvestmentData();
        $this->checkIncomeData();
        $this->checkProductData();
        $this->checkCronJobs();
        $this->checkRecentActivity();
        $this->checkDataConsistency();
        $this->generateRecommendations();
    }
    
    /**
     * 检查数据库表结构
     */
    private function checkDatabaseTables()
    {
        $this->log("\n=== 1. 数据库表结构检查 ===");
        
        $requiredTables = [
            'xy_lixibao' => '利息宝投资记录表',
            'xy_balance_log' => '余额变动记录表',
            'xy_lixibao_list' => '利息宝产品列表表',
            'xy_member' => '用户表'
        ];
        
        foreach ($requiredTables as $table => $desc) {
            try {
                $result = $this->db->query("SHOW TABLES LIKE '{$table}'");
                if (empty($result)) {
                    $this->log("❌ 表 {$table} ({$desc}) 不存在！");
                } else {
                    $this->log("✅ 表 {$table} ({$desc}) 存在");
                    
                    // 检查表结构
                    $fields = $this->db->query("DESCRIBE {$table}");
                    $this->log("   字段数量: " . count($fields));
                    
                    if ($table == 'xy_lixibao') {
                        $this->checkLixibaoTableStructure($fields);
                    } elseif ($table == 'xy_balance_log') {
                        $this->checkBalanceLogTableStructure($fields);
                    }
                }
            } catch (Exception $e) {
                $this->log("❌ 检查表 {$table} 时出错: " . $e->getMessage());
            }
        }
    }
    
    /**
     * 检查利息宝表结构
     */
    private function checkLixibaoTableStructure($fields)
    {
        $requiredFields = ['id', 'uid', 'num', 'type', 'addtime', 'endtime', 'status', 'product_id'];
        $fieldNames = array_column($fields, 'Field');
        
        foreach ($requiredFields as $field) {
            if (in_array($field, $fieldNames)) {
                $this->log("   ✅ 字段 {$field} 存在");
            } else {
                $this->log("   ❌ 字段 {$field} 缺失！");
            }
        }
    }
    
    /**
     * 检查余额记录表结构
     */
    private function checkBalanceLogTableStructure($fields)
    {
        $requiredFields = ['id', 'uid', 'num', 'type', 'addtime', 'remark'];
        $fieldNames = array_column($fields, 'Field');
        
        foreach ($requiredFields as $field) {
            if (in_array($field, $fieldNames)) {
                $this->log("   ✅ 字段 {$field} 存在");
            } else {
                $this->log("   ❌ 字段 {$field} 缺失！");
            }
        }
    }
    
    /**
     * 检查投资数据
     */
    private function checkInvestmentData()
    {
        $this->log("\n=== 2. 投资数据检查 ===");
        
        try {
            // 统计总投资记录
            $totalInvestments = $this->db->name('lixibao')->count();
            $this->log("总投资记录数: {$totalInvestments}");
            
            // 统计各类型投资
            $investmentTypes = $this->db->name('lixibao')
                ->field('type, count(*) as count, sum(num) as total_amount')
                ->group('type')
                ->select();
            
            foreach ($investmentTypes as $type) {
                $typeDesc = $this->getInvestmentTypeDesc($type['type']);
                $this->log("类型 {$type['type']} ({$typeDesc}): {$type['count']} 条记录, 总金额: ¥{$type['total_amount']}");
            }
            
            // 检查活跃投资
            $activeInvestments = $this->db->name('lixibao')
                ->where('type', 1)
                ->where('status', 1)
                ->where('endtime', '>', time())
                ->count();
            $this->log("当前活跃投资数: {$activeInvestments}");
            
            // 检查最近的投资
            $recentInvestments = $this->db->name('lixibao')
                ->where('type', 1)
                ->where('addtime', '>', strtotime('-7 days'))
                ->count();
            $this->log("最近7天新增投资: {$recentInvestments}");
            
        } catch (Exception $e) {
            $this->log("❌ 检查投资数据时出错: " . $e->getMessage());
        }
    }
    
    /**
     * 检查收益数据
     */
    private function checkIncomeData()
    {
        $this->log("\n=== 3. 收益数据检查 ===");
        
        try {
            // 统计收益记录
            $totalIncomeRecords = $this->db->name('balance_log')
                ->where('type', 23)
                ->count();
            $this->log("总收益记录数: {$totalIncomeRecords}");
            
            // 统计收益金额
            $totalIncomeAmount = $this->db->name('balance_log')
                ->where('type', 23)
                ->sum('num');
            $this->log("累计收益金额: ¥{$totalIncomeAmount}");
            
            // 检查最近的收益记录
            $recentIncomeRecords = $this->db->name('balance_log')
                ->where('type', 23)
                ->where('addtime', '>', strtotime('-3 days'))
                ->count();
            $this->log("最近3天收益记录数: {$recentIncomeRecords}");
            
            // 检查昨天的收益记录
            $yesterdayStart = strtotime(date('Y-m-d', strtotime('-1 day')));
            $yesterdayEnd = $yesterdayStart + 86400;
            
            $yesterdayIncome = $this->db->name('balance_log')
                ->where('type', 23)
                ->where('addtime', 'between', [$yesterdayStart, $yesterdayEnd])
                ->count();
            $this->log("昨天收益记录数: {$yesterdayIncome}");
            
            if ($yesterdayIncome == 0) {
                $this->log("⚠️  昨天没有收益记录！这可能是问题所在。");
            }
            
        } catch (Exception $e) {
            $this->log("❌ 检查收益数据时出错: " . $e->getMessage());
        }
    }
    
    /**
     * 检查产品数据
     */
    private function checkProductData()
    {
        $this->log("\n=== 4. 产品数据检查 ===");
        
        try {
            $products = $this->db->name('lixibao_list')->select();
            $this->log("产品总数: " . count($products));
            
            foreach ($products as $product) {
                $this->log("产品ID: {$product['id']}, 名称: {$product['title']}, 利率: {$product['rate']}%, 天数: {$product['day']}");
            }
            
        } catch (Exception $e) {
            $this->log("❌ 检查产品数据时出错: " . $e->getMessage());
        }
    }
    
    /**
     * 检查定时任务状态
     */
    private function checkCronJobs()
    {
        $this->log("\n=== 5. 定时任务检查 ===");
        
        // 检查相关脚本文件是否存在
        $scriptFiles = [
            'fix_lixibao_income_daily.php',
            'complete_lixibao_fix.php',
            'quick_fix_lixibao.php'
        ];
        
        foreach ($scriptFiles as $file) {
            if (file_exists($file)) {
                $this->log("✅ 脚本文件 {$file} 存在");
            } else {
                $this->log("❌ 脚本文件 {$file} 不存在");
            }
        }
        
        // 检查日志文件
        $logFiles = [
            'lixibao_daily.log',
            'lixibao_fix.log'
        ];
        
        foreach ($logFiles as $logFile) {
            if (file_exists($logFile)) {
                $size = filesize($logFile);
                $lastModified = date('Y-m-d H:i:s', filemtime($logFile));
                $this->log("✅ 日志文件 {$logFile} 存在 (大小: {$size} bytes, 最后修改: {$lastModified})");
            } else {
                $this->log("⚠️  日志文件 {$logFile} 不存在");
            }
        }
    }
    
    /**
     * 检查最近活动
     */
    private function checkRecentActivity()
    {
        $this->log("\n=== 6. 最近活动检查 ===");
        
        try {
            // 检查需要生成收益的投资
            $activeInvestments = $this->db->name('lixibao')
                ->alias('l')
                ->join('lixibao_list ll', 'l.product_id = ll.id')
                ->where('l.type', 1)
                ->where('l.status', 1)
                ->where('l.endtime', '>', time())
                ->field('l.*, ll.rate, ll.day, ll.title')
                ->select();
            
            $this->log("当前需要计算收益的投资数: " . count($activeInvestments));
            
            foreach ($activeInvestments as $investment) {
                $this->log("投资ID: {$investment['id']}, 用户ID: {$investment['uid']}, 金额: ¥{$investment['num']}, 产品: {$investment['title']}");
                
                // 检查这笔投资的收益记录
                $incomeCount = $this->db->name('balance_log')
                    ->where('uid', $investment['uid'])
                    ->where('type', 23)
                    ->where('remark', 'like', '%利息宝%')
                    ->where('addtime', '>=', $investment['addtime'])
                    ->count();
                
                $this->log("  - 已生成收益记录数: {$incomeCount}");
                
                // 计算应该生成的收益天数
                $daysPassed = floor((time() - $investment['addtime']) / 86400);
                $this->log("  - 已过天数: {$daysPassed}");
                
                if ($incomeCount < $daysPassed) {
                    $this->log("  ⚠️  收益记录不足！应该有 {$daysPassed} 条，实际有 {$incomeCount} 条");
                }
            }
            
        } catch (Exception $e) {
            $this->log("❌ 检查最近活动时出错: " . $e->getMessage());
        }
    }
    
    /**
     * 检查数据一致性
     */
    private function checkDataConsistency()
    {
        $this->log("\n=== 7. 数据一致性检查 ===");
        
        try {
            // 检查是否有孤立的收益记录
            $orphanIncome = $this->db->query("
                SELECT COUNT(*) as count FROM xy_balance_log bl 
                WHERE bl.type = 23 
                AND bl.remark LIKE '%利息宝%'
                AND NOT EXISTS (
                    SELECT 1 FROM xy_lixibao l 
                    WHERE l.uid = bl.uid 
                    AND l.type = 1 
                    AND l.addtime <= bl.addtime
                )
            ");
            
            if ($orphanIncome[0]['count'] > 0) {
                $this->log("⚠️  发现 {$orphanIncome[0]['count']} 条孤立的收益记录");
            } else {
                $this->log("✅ 没有孤立的收益记录");
            }
            
            // 检查是否有重复的收益记录
            $duplicateIncome = $this->db->query("
                SELECT uid, DATE(FROM_UNIXTIME(addtime)) as income_date, COUNT(*) as count
                FROM xy_balance_log 
                WHERE type = 23 AND remark LIKE '%利息宝%'
                GROUP BY uid, DATE(FROM_UNIXTIME(addtime))
                HAVING COUNT(*) > 1
            ");
            
            if (count($duplicateIncome) > 0) {
                $this->log("⚠️  发现重复的收益记录:");
                foreach ($duplicateIncome as $dup) {
                    $this->log("  用户ID: {$dup['uid']}, 日期: {$dup['income_date']}, 重复次数: {$dup['count']}");
                }
            } else {
                $this->log("✅ 没有重复的收益记录");
            }
            
        } catch (Exception $e) {
            $this->log("❌ 检查数据一致性时出错: " . $e->getMessage());
        }
    }
    
    /**
     * 生成修复建议
     */
    private function generateRecommendations()
    {
        $this->log("\n=== 8. 修复建议 ===");
        
        // 检查昨天是否有收益记录
        $yesterdayStart = strtotime(date('Y-m-d', strtotime('-1 day')));
        $yesterdayEnd = $yesterdayStart + 86400;
        
        $yesterdayIncome = $this->db->name('balance_log')
            ->where('type', 23)
            ->where('addtime', 'between', [$yesterdayStart, $yesterdayEnd])
            ->count();
        
        if ($yesterdayIncome == 0) {
            $this->log("🔧 建议1: 立即运行补发昨天收益的脚本");
            $this->log("   命令: php quick_fix_lixibao.php");
        }
        
        // 检查是否有活跃投资但没有最近收益
        $activeInvestments = $this->db->name('lixibao')
            ->where('type', 1)
            ->where('status', 1)
            ->where('endtime', '>', time())
            ->count();
        
        $recentIncome = $this->db->name('balance_log')
            ->where('type', 23)
            ->where('addtime', '>', strtotime('-24 hours'))
            ->count();
        
        if ($activeInvestments > 0 && $recentIncome == 0) {
            $this->log("🔧 建议2: 有活跃投资但没有最近收益，建议检查定时任务");
            $this->log("   命令: crontab -l | grep lixibao");
        }
        
        $this->log("🔧 建议3: 设置正确的定时任务");
        $this->log("   每天凌晨1点执行: 0 1 * * * /usr/bin/php " . __DIR__ . "/fix_lixibao_income_daily.php");
        
        $this->log("🔧 建议4: 启用详细日志记录");
        $this->log("   在脚本中添加详细的日志输出，便于调试");
    }
    
    /**
     * 获取投资类型描述
     */
    private function getInvestmentTypeDesc($type)
    {
        $types = [
            1 => '投资',
            2 => '赎回',
            3 => '收益'
        ];
        return isset($types[$type]) ? $types[$type] : '未知';
    }
    
    /**
     * 记录日志
     */
    private function log($message)
    {
        $logMessage = date('Y-m-d H:i:s') . " - " . $message . "\n";
        echo $logMessage;
        
        // 同时写入日志文件
        file_put_contents('lixibao_diagnosis.log', $logMessage, FILE_APPEND);
    }
}

// 运行诊断
try {
    $diagnosis = new LixibaoComprehensiveDiagnosis();
    $diagnosis->runFullDiagnosis();
    echo "\n诊断完成！详细日志已保存到 lixibao_diagnosis.log\n";
} catch (Exception $e) {
    echo "诊断过程中出错: " . $e->getMessage() . "\n";
}
?> 