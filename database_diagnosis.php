<?php
// 数据库直接诊断脚本
// 不依赖框架，直接连接数据库进行诊断

echo "=== 利息宝数据库诊断开始 ===\n";
echo "诊断时间: " . date('Y-m-d H:i:s') . "\n\n";

// 数据库配置 - 从配置文件读取
$db_config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'g5_vt1685_site',
    'username' => 'g5_vt1685_site',
    'password' => 'g5_vt1685_site',
    'charset' => 'utf8mb4'
];

// 尝试从配置文件读取数据库配置
$config_files = [
    'config/database.php',
    'application/database.php',
    'config/config.php'
];

foreach ($config_files as $config_file) {
    if (file_exists($config_file)) {
        echo "找到配置文件: {$config_file}\n";
        
        $config_content = file_get_contents($config_file);
        
        // 尝试提取数据库配置
        if (preg_match("/'hostname'\s*=>\s*'([^']+)'/", $config_content, $matches)) {
            $db_config['host'] = $matches[1];
        }
        if (preg_match("/'database'\s*=>\s*'([^']+)'/", $config_content, $matches)) {
            $db_config['database'] = $matches[1];
        }
        if (preg_match("/'username'\s*=>\s*'([^']+)'/", $config_content, $matches)) {
            $db_config['username'] = $matches[1];
        }
        if (preg_match("/'password'\s*=>\s*'([^']+)'/", $config_content, $matches)) {
            $db_config['password'] = $matches[1];
        }
        if (preg_match("/'hostport'\s*=>\s*'?(\d+)'?/", $config_content, $matches)) {
            $db_config['port'] = intval($matches[1]);
        }
        
        break;
    }
}

echo "使用数据库配置:\n";
echo "主机: {$db_config['host']}:{$db_config['port']}\n";
echo "数据库: {$db_config['database']}\n";
echo "用户名: {$db_config['username']}\n\n";

// 连接数据库
try {
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['database']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    echo "请检查数据库配置信息\n";
    exit;
}

// 检查必要的表
echo "=== 1. 检查数据库表 ===\n";
$required_tables = [
    'xy_lixibao' => '利息宝投资记录表',
    'xy_balance_log' => '余额变动记录表',
    'xy_lixibao_list' => '利息宝产品列表表',
    'xy_users' => '用户表'
];

foreach ($required_tables as $table => $desc) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            echo "✅ {$desc} ({$table}) 存在\n";
            
            // 检查记录数
            $count_stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $count_stmt->fetchColumn();
            echo "   记录数: {$count}\n";
        } else {
            echo "❌ {$desc} ({$table}) 不存在\n";
        }
    } catch (PDOException $e) {
        echo "❌ 检查表 {$table} 时出错: " . $e->getMessage() . "\n";
    }
}

echo "\n=== 2. 检查投资数据 ===\n";
try {
    // 统计投资记录
    $stmt = $pdo->query("SELECT type, COUNT(*) as count, SUM(num) as total_amount FROM xy_lixibao GROUP BY type");
    $investment_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $type_names = [1 => '投资', 2 => '赎回', 3 => '收益'];
    
    foreach ($investment_types as $type) {
        $type_name = isset($type_names[$type['type']]) ? $type_names[$type['type']] : '未知';
        echo "类型 {$type['type']} ({$type_name}): {$type['count']} 条记录, 总金额: ¥{$type['total_amount']}\n";
    }
    
    // 检查活跃投资
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_lixibao WHERE type = 1 AND status = 1 AND endtime > " . time());
    $active_count = $stmt->fetchColumn();
    echo "当前活跃投资数: {$active_count}\n";
    
} catch (PDOException $e) {
    echo "❌ 检查投资数据时出错: " . $e->getMessage() . "\n";
}

echo "\n=== 3. 检查收益数据 ===\n";
try {
    // 统计收益记录
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23");
    $income_count = $stmt->fetchColumn();
    echo "总收益记录数: {$income_count}\n";
    
    $stmt = $pdo->query("SELECT SUM(num) FROM xy_balance_log WHERE type = 23");
    $total_income = $stmt->fetchColumn();
    echo "累计收益金额: ¥{$total_income}\n";
    
    // 检查最近3天的收益
    $three_days_ago = time() - (3 * 24 * 3600);
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime > {$three_days_ago}");
    $recent_income = $stmt->fetchColumn();
    echo "最近3天收益记录数: {$recent_income}\n";
    
    // 检查昨天的收益
    $yesterday_start = strtotime(date('Y-m-d', strtotime('-1 day')));
    $yesterday_end = $yesterday_start + 86400;
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime BETWEEN {$yesterday_start} AND {$yesterday_end}");
    $yesterday_income = $stmt->fetchColumn();
    echo "昨天收益记录数: {$yesterday_income}\n";
    
    if ($yesterday_income == 0) {
        echo "⚠️  昨天没有收益记录！\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 检查收益数据时出错: " . $e->getMessage() . "\n";
}

echo "\n=== 4. 检查产品数据 ===\n";
try {
    $stmt = $pdo->query("SELECT * FROM xy_lixibao_list ORDER BY id");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "产品总数: " . count($products) . "\n";
    foreach ($products as $product) {
        $title = isset($product['title']) ? $product['title'] : (isset($product['name']) ? $product['name'] : '未知');
        $rate = isset($product['rate']) ? $product['rate'] : (isset($product['bili']) ? $product['bili'] : 0);
        $day = isset($product['day']) ? $product['day'] : '未知';
        echo "产品ID: {$product['id']}, 名称: {$title}, 利率: {$rate}%, 天数: {$day}\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 检查产品数据时出错: " . $e->getMessage() . "\n";
}

echo "\n=== 5. 检查具体用户投资情况 ===\n";
try {
    // 查找有活跃投资的用户
    $stmt = $pdo->query("
        SELECT u.id, u.username, u.balance, 
               COUNT(l.id) as investment_count,
               SUM(l.num) as total_investment
        FROM xy_users u
        LEFT JOIN xy_lixibao l ON u.id = l.uid AND l.type = 1 AND l.status = 1 AND l.endtime > " . time() . "
        WHERE l.id IS NOT NULL
        GROUP BY u.id
        LIMIT 5
    ");
    
    $active_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "有活跃投资的用户 (前5个):\n";
    foreach ($active_users as $user) {
        echo "用户ID: {$user['id']}, 用户名: {$user['username']}, 余额: ¥{$user['balance']}, 投资笔数: {$user['investment_count']}, 投资总额: ¥{$user['total_investment']}\n";
        
        // 检查该用户的收益记录
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM xy_balance_log WHERE uid = ? AND type = 23 AND addtime > ?");
        $stmt->execute([$user['id'], time() - (7 * 24 * 3600)]);
        $user_income_count = $stmt->fetchColumn();
        echo "  - 最近7天收益记录数: {$user_income_count}\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 检查用户投资情况时出错: " . $e->getMessage() . "\n";
}

echo "\n=== 6. 数据一致性检查 ===\n";
try {
    // 检查是否有投资但没有收益的情况
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM xy_lixibao l
        WHERE l.type = 1 AND l.status = 1 AND l.endtime > " . time() . "
        AND NOT EXISTS (
            SELECT 1 FROM xy_balance_log bl 
            WHERE bl.uid = l.uid AND bl.type = 23 
            AND bl.addtime >= l.addtime
        )
    ");
    
    $no_income_investments = $stmt->fetchColumn();
    if ($no_income_investments > 0) {
        echo "⚠️  发现 {$no_income_investments} 笔投资没有对应的收益记录\n";
    } else {
        echo "✅ 所有活跃投资都有对应的收益记录\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 数据一致性检查时出错: " . $e->getMessage() . "\n";
}

echo "\n=== 7. 修复建议 ===\n";

// 检查昨天是否有收益记录
$yesterday_start = strtotime(date('Y-m-d', strtotime('-1 day')));
$yesterday_end = $yesterday_start + 86400;

try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime BETWEEN {$yesterday_start} AND {$yesterday_end}");
    $yesterday_income = $stmt->fetchColumn();
    
    if ($yesterday_income == 0) {
        echo "🔧 建议1: 昨天没有收益记录，需要立即补发\n";
        echo "   可以运行: quick_fix_lixibao.php\n";
    }
    
    // 检查是否有活跃投资但没有最近收益
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_lixibao WHERE type = 1 AND status = 1 AND endtime > " . time());
    $active_investments = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime > " . (time() - 24 * 3600));
    $recent_income = $stmt->fetchColumn();
    
    if ($active_investments > 0 && $recent_income == 0) {
        echo "🔧 建议2: 有活跃投资但没有最近收益，检查定时任务\n";
        echo "   定时任务URL: http://您的域名/index/crontab/lixibao_js\n";
    }
    
    echo "🔧 建议3: 检查Web服务器和定时任务配置\n";
    echo "🔧 建议4: 查看服务器错误日志\n";
    
} catch (PDOException $e) {
    echo "❌ 生成建议时出错: " . $e->getMessage() . "\n";
}

echo "\n=== 诊断完成 ===\n";
echo "如果发现问题，请根据上述建议进行修复。\n";
?> 