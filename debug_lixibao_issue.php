<?php
/**
 * 利息宝收益问题调试脚本
 * 检查用户投资记录和收益发放情况
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

require __DIR__ . '/thinkphp/base.php';

use think\Db;

function writeOutput($message) {
    echo date('Y-m-d H:i:s') . " - $message<br>\n";
    flush();
}

echo "<h1>利息宝收益问题调试</h1>";

try {
    // 1. 检查最近的投资记录
    writeOutput("=== 1. 检查最近的投资记录 ===");
    
    $recent_investments = Db::name('xy_lixibao')
        ->alias('xl')
        ->leftJoin('xy_users u', 'u.id=xl.uid')
        ->leftJoin('xy_lixibao_list xll', 'xll.id=xl.sid')
        ->where('xl.type', 1) // 转入类型
        ->where('xl.addtime', '>=', strtotime('-10 days')) // 最近10天
        ->field('xl.*, u.username, xll.name as product_name, xll.day as product_day')
        ->order('xl.addtime desc')
        ->limit(20)
        ->select();
    
    writeOutput("找到最近投资记录: " . count($recent_investments) . " 条");
    
    if (!empty($recent_investments)) {
        echo "<table border='1' style='border-collapse:collapse; width:100%; margin:10px 0;'>";
        echo "<tr style='background-color:#f0f0f0;'>";
        echo "<th>ID</th><th>用户</th><th>产品</th><th>金额</th><th>投资时间</th><th>到期时间</th><th>已取出</th><th>状态</th><th>剩余天数</th>";
        echo "</tr>";
        
        foreach ($recent_investments as $investment) {
            $current_time = time();
            $days_left = max(0, ceil(($investment['endtime'] - $current_time) / (24 * 3600)));
            $is_active = $investment['endtime'] > $current_time ? '是' : '否';
            
            echo "<tr>";
            echo "<td>{$investment['id']}</td>";
            echo "<td>{$investment['username']}</td>";
            echo "<td>{$investment['product_name']}</td>";
            echo "<td>{$investment['num']}</td>";
            echo "<td>" . date('Y-m-d H:i:s', $investment['addtime']) . "</td>";
            echo "<td>" . date('Y-m-d H:i:s', $investment['endtime']) . "</td>";
            echo "<td>" . ($investment['is_qu'] ? '是' : '否') . "</td>";
            echo "<td>$is_active</td>";
            echo "<td>$days_left</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 2. 检查收益发放记录
    writeOutput("=== 2. 检查最近的收益发放记录 ===");
    
    $recent_incomes = Db::name('xy_balance_log')
        ->alias('bl')
        ->leftJoin('xy_users u', 'u.id=bl.uid')
        ->where('bl.type', 23) // 收益类型
        ->where('bl.addtime', '>=', strtotime('-7 days')) // 最近7天
        ->field('bl.*, u.username')
        ->order('bl.addtime desc')
        ->limit(20)
        ->select();
    
    writeOutput("找到最近收益记录: " . count($recent_incomes) . " 条");
    
    if (!empty($recent_incomes)) {
        echo "<table border='1' style='border-collapse:collapse; width:100%; margin:10px 0;'>";
        echo "<tr style='background-color:#f0f0f0;'>";
        echo "<th>ID</th><th>用户</th><th>收益金额</th><th>发放时间</th><th>状态</th>";
        echo "</tr>";
        
        foreach ($recent_incomes as $income) {
            echo "<tr>";
            echo "<td>{$income['id']}</td>";
            echo "<td>{$income['username']}</td>";
            echo "<td>{$income['num']}</td>";
            echo "<td>" . date('Y-m-d H:i:s', $income['addtime']) . "</td>";
            echo "<td>" . ($income['status'] ? '成功' : '待处理') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 3. 针对性检查问题用户
    writeOutput("=== 3. 检查具体问题用户 ===");
    
    // 检查有投资但最近没有收益记录的用户
    $users_with_issues = Db::query("
        SELECT u.id, u.username, u.lixibao_balance,
               COUNT(xl.id) as investment_count,
               MAX(xl.endtime) as max_endtime,
               MIN(xl.addtime) as min_addtime,
               (SELECT COUNT(*) FROM xy_balance_log WHERE uid=u.id AND type=23 AND addtime >= ?) as recent_income_count
        FROM xy_users u
        LEFT JOIN xy_lixibao xl ON u.id = xl.uid AND xl.type = 1 AND xl.is_qu = 0
        WHERE u.lixibao_balance > 0
        GROUP BY u.id
        HAVING investment_count > 0 AND recent_income_count = 0
        ORDER BY u.id
        LIMIT 10
    ", [strtotime('-3 days')]);
    
    writeOutput("找到可能有问题的用户: " . count($users_with_issues) . " 个");
    
    if (!empty($users_with_issues)) {
        echo "<table border='1' style='border-collapse:collapse; width:100%; margin:10px 0;'>";
        echo "<tr style='background-color:#ffeeee;'>";
        echo "<th>用户ID</th><th>用户名</th><th>余额宝余额</th><th>投资记录数</th><th>最早投资</th><th>最晚到期</th><th>最近3天收益</th>";
        echo "</tr>";
        
        foreach ($users_with_issues as $user) {
            $is_still_active = $user['max_endtime'] > time() ? '有效' : '已到期';
            
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['lixibao_balance']}</td>";
            echo "<td>{$user['investment_count']}</td>";
            echo "<td>" . date('Y-m-d', $user['min_addtime']) . "</td>";
            echo "<td>" . date('Y-m-d', $user['max_endtime']) . " ($is_still_active)</td>";
            echo "<td>{$user['recent_income_count']}次</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 详细分析第一个用户的情况
        if (count($users_with_issues) > 0) {
            $problem_user = $users_with_issues[0];
            $uid = $problem_user['id'];
            
            writeOutput("=== 详细分析用户 {$problem_user['username']} (ID: $uid) ===");
            
            // 检查该用户的所有投资记录
            $user_investments = Db::name('xy_lixibao')
                ->alias('xl')
                ->leftJoin('xy_lixibao_list xll', 'xll.id=xl.sid')
                ->where('xl.uid', $uid)
                ->where('xl.type', 1)
                ->field('xl.*, xll.name as product_name, xll.bili, xll.day')
                ->order('xl.addtime desc')
                ->select();
            
            echo "<h4>该用户的投资记录:</h4>";
            echo "<table border='1' style='border-collapse:collapse; width:100%; margin:10px 0;'>";
            echo "<tr style='background-color:#f0f0f0;'>";
            echo "<th>投资ID</th><th>产品</th><th>金额</th><th>利率</th><th>期限</th><th>投资时间</th><th>到期时间</th><th>状态</th><th>每日应得收益</th>";
            echo "</tr>";
            
            $current_time = time();
            $total_daily_income = 0;
            
            foreach ($user_investments as $inv) {
                $is_active = ($inv['endtime'] > $current_time && $inv['is_qu'] == 0) ? '活跃' : '已结束';
                $daily_income = $inv['num'] * ($inv['bili'] ?: 0.05);
                
                if ($is_active === '活跃') {
                    $total_daily_income += $daily_income;
                }
                
                echo "<tr>";
                echo "<td>{$inv['id']}</td>";
                echo "<td>{$inv['product_name']}</td>";
                echo "<td>{$inv['num']}</td>";
                echo "<td>" . (($inv['bili'] ?: 0.05) * 100) . "%</td>";
                echo "<td>{$inv['day']}天</td>";
                echo "<td>" . date('Y-m-d H:i:s', $inv['addtime']) . "</td>";
                echo "<td>" . date('Y-m-d H:i:s', $inv['endtime']) . "</td>";
                echo "<td>$is_active</td>";
                echo "<td>$daily_income</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            writeOutput("该用户每日应得总收益: $total_daily_income");
            
            // 检查该用户的收益记录
            $user_incomes = Db::name('xy_balance_log')
                ->where('uid', $uid)
                ->where('type', 23)
                ->where('addtime', '>=', strtotime('-10 days'))
                ->order('addtime desc')
                ->select();
            
            writeOutput("该用户最近10天收益记录: " . count($user_incomes) . " 条");
            
            if (!empty($user_incomes)) {
                echo "<table border='1' style='border-collapse:collapse; width:100%; margin:10px 0;'>";
                echo "<tr style='background-color:#f0f0f0;'>";
                echo "<th>收益金额</th><th>发放时间</th><th>状态</th>";
                echo "</tr>";
                
                foreach ($user_incomes as $income) {
                    echo "<tr>";
                    echo "<td>{$income['num']}</td>";
                    echo "<td>" . date('Y-m-d H:i:s', $income['addtime']) . "</td>";
                    echo "<td>" . ($income['status'] ? '成功' : '待处理') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    }
    
    // 4. 检查定时任务配置
    writeOutput("=== 4. 检查定时任务配置建议 ===");
    
    echo "<div style='background-color:#fff3cd; padding:15px; margin:10px 0; border:1px solid #ffeaa7;'>";
    echo "<h4>定时任务配置:</h4>";
    echo "<p>应确保以下定时任务正常运行:</p>";
    echo "<pre>";
    echo "# 每天12:10计算收益\n";
    echo "10 12 * * * curl -s \"http://您的域名/index/crontab/lixibao_js\" > /dev/null 2>&1\n\n";
    echo "# 每天23:30处理到期投资\n";
    echo "30 23 * * * curl -s \"http://您的域名/index/crontab/lxb_jiesuan\" > /dev/null 2>&1\n";
    echo "</pre>";
    echo "</div>";
    
    // 5. 问题分析和建议
    writeOutput("=== 5. 问题分析和建议 ===");
    
    echo "<div style='background-color:#d1ecf1; padding:15px; margin:10px 0; border:1px solid #bee5eb;'>";
    echo "<h4>可能的问题原因:</h4>";
    echo "<ol>";
    echo "<li><strong>定时任务未正常执行:</strong> 检查服务器的crontab是否配置正确并正常运行</li>";
    echo "<li><strong>时间检查逻辑问题:</strong> 系统可能因为时间偏差导致重复发放检查失效</li>";
    echo "<li><strong>投资期限判断错误:</strong> 系统可能错误地认为投资已到期</li>";
    echo "<li><strong>数据库状态不一致:</strong> 投资记录状态与实际情况不符</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background-color:#d4edda; padding:15px; margin:10px 0; border:1px solid #c3e6cb;'>";
    echo "<h4>解决方案:</h4>";
    echo "<ol>";
    echo "<li><strong>立即补发缺失收益:</strong> 使用 bufa_shouyi.php 脚本补发缺失的收益</li>";
    echo "<li><strong>检查定时任务:</strong> 确保定时任务正常运行，可以手动访问收益计算接口测试</li>";
    echo "<li><strong>修复收益计算逻辑:</strong> 改进时间检查和投资期限判断逻辑</li>";
    echo "<li><strong>增强日志记录:</strong> 添加更详细的日志以便后续排查问题</li>";
    echo "</ol>";
    echo "</div>";
    
    // 6. 手动测试链接
    writeOutput("=== 6. 手动测试链接 ===");
    
    echo "<div style='background-color:#fff; padding:15px; margin:10px 0; border:1px solid #ddd;'>";
    echo "<h4>可以尝试以下操作:</h4>";
    echo "<p><a href='bufa_shouyi.php?start_date=" . date('Y-m-d', strtotime('-7 days')) . "&end_date=" . date('Y-m-d') . "' target='_blank'>";
    echo "补发最近7天缺失收益</a></p>";
    echo "<p><a href='/index/crontab/lixibao_js' target='_blank'>手动触发收益计算</a></p>";
    echo "<p><a href='/index/crontab/lxb_jiesuan' target='_blank'>手动触发到期结算</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    writeOutput("发生错误: " . $e->getMessage());
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h3>调试完成</h3>";
echo "<p>生成时间: " . date('Y-m-d H:i:s') . "</p>";
?> 