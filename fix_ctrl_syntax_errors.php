<?php
/**
 * 修复Ctrl.php文件中的字符编码和语法错误
 */

$file_path = 'application/index/controller/Ctrl.php';

if (!file_exists($file_path)) {
    echo "错误：文件 {$file_path} 不存在\n";
    exit(1);
}

// 读取文件内容
$content = file_get_contents($file_path);

if ($content === false) {
    echo "错误：无法读取文件 {$file_path}\n";
    exit(1);
}

echo "开始修复Ctrl.php文件中的语法错误...\n";

// 修复字符编码问题
$fixes = [
    // 修复注释截断问题
    '// 从配置文件获取利?        $rate = config(\'lxb_bili\');' => '// 从配置文件获取利率' . "\n        " . '$rate = config(\'lxb_bili\');',
    '// 如果利率?或不存在，则尝试从数据库获取最新利?        if (empty($rate) || $rate == \'0\') {' => '// 如果利率为空或不存在，则尝试从数据库获取最新利率' . "\n        " . 'if (empty($rate) || $rate == \'0\') {',
    '// 如果利率为空或不存在，则尝试从数据库获取最新利?        if (empty($rate) || $rate == \'0\') {' => '// 如果利率为空或不存在，则尝试从数据库获取最新利率' . "\n        " . 'if (empty($rate) || $rate == \'0\') {',
    '// 如果利率�?或不存在，则尝试从数据库获取最新利�?        if (empty($rate) || $rate == \'0\') {' => '// 如果利率为空或不存在，则尝试从数据库获取最新利率' . "\n        " . 'if (empty($rate) || $rate == \'0\') {',
    
    // 修复筛选条件问题
    '// 筛选条�?        $start_time = input(\'get.start_time/s\',\'\');' => '// 筛选条件' . "\n        " . '$start_time = input(\'get.start_time/s\',\'\');',
    '// 增加日期筛选条�?        if (!empty($start_time) && !empty($end_time)) {' => '// 增加日期筛选条件' . "\n        " . 'if (!empty($start_time) && !empty($end_time)) {',
    '// 增加类型筛选条�?        if ($type > 0) {' => '// 增加类型筛选条件' . "\n        " . 'if ($type > 0) {',
    '// 默认只查询最�?0�?            $month_ago = strtotime(\'-30 days\');' => '// 默认只查询最近30天' . "\n            " . '$month_ago = strtotime(\'-30 days\');',
    
    // 修复中文字符编码问题
    'lang(\'余额宝转�?\')' => 'lang(\'余额宝转入\')',
    'lang(\'余额宝转�?\')' => 'lang(\'余额宝转出\')',
    '余额宝收益记�?' => '余额宝收益记录',
    '获取用户今日已充值金�?' => '获取用户今日已充值金额',
    '查询余额宝记录异�?' => '查询余额宝记录异常',
    '设置默认值确保页面不会崩�?' => '设置默认值确保页面不会崩溃',
    '默认产品类型和利�?' => '默认产品类型和利率',
    '如果找到产品信息，添加产品名�?' => '如果找到产品信息，添加产品名称',
    '检查是否有未取出的余额宝记�?' => '检查是否有未取出的余额宝记录',
    '开始事�?' => '开始事务',
    '计算总转出金额和总收�?' => '计算总转出金额和总收益',
    '先将余额宝记录标记为已取�?' => '先将余额宝记录标记为已取出',
    '扣除用户余额宝余�?' => '扣除用户余额宝余额',
    '确保金额大于0才记录，避免出现0.00的记�?' => '确保金额大于0才记录，避免出现0.00的记录',
    'lang(\'利息宝余额转�?\')' => 'lang(\'利息宝余额转出\')',
    'lang(\'可用余额不足，请充�?\')' => 'lang(\'可用余额不足，请充值\')',
    'lang(\'获取用户今日已充值金�?\')' => 'lang(\'获取用户今日已充值金额\')',
];

$fixed_count = 0;
foreach ($fixes as $search => $replace) {
    $old_content = $content;
    $content = str_replace($search, $replace, $content);
    if ($content !== $old_content) {
        $fixed_count++;
        echo "修复: {$search}\n";
    }
}

// 备份原文件
$backup_file = $file_path . '.backup.' . date('Y-m-d_H-i-s');
if (copy($file_path, $backup_file)) {
    echo "已创建备份文件: {$backup_file}\n";
} else {
    echo "警告：无法创建备份文件\n";
}

// 写入修复后的内容
if (file_put_contents($file_path, $content) !== false) {
    echo "成功修复 {$fixed_count} 个问题\n";
    echo "文件已保存: {$file_path}\n";
} else {
    echo "错误：无法写入文件 {$file_path}\n";
    exit(1);
}

echo "修复完成！\n";
echo "\n请检查以下文件是否需要上传到服务器：\n";
echo "- {$file_path}\n";
?> 