<?php
/**
 * 修复版利息宝每日收益计算脚本
 * 确保每天都正确发放收益直到投资到期
 * 可直接运行：php fix_lixibao_income_daily.php
 * 或通过浏览器访问：http://您的域名/fix_lixibao_income_daily.php
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

require __DIR__ . '/thinkphp/base.php';

use think\Db;

// 辅助函数：生成订单号
function getSn($prefix = '') {
    return $prefix . date('ymdHis') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
}

// 辅助函数：写入日志
function writeLog($message) {
    $log_file = __DIR__ . '/runtime/log/lixibao_daily_income.log';
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $message\n", FILE_APPEND);
    echo date('Y-m-d H:i:s') . " - $message<br>\n";
    flush();
}

try {
    writeLog("开始执行每日收益计算");
    
    $current_time = time();
    $today_date = date('Y-m-d', $current_time);
    $today_start = strtotime($today_date . ' 00:00:00');
    $today_end = strtotime($today_date . ' 23:59:59');
    
    writeLog("计算日期: $today_date");
    writeLog("当前时间戳: $current_time (" . date('Y-m-d H:i:s', $current_time) . ")");
    
    // 获取所有有投资记录且仍在投资期间的用户
    $users_with_active_investments = Db::name('xy_lixibao')
        ->alias('xl')
        ->leftJoin('xy_users u', 'u.id=xl.uid')
        ->where('xl.type', 1) // 转入类型
        ->where('xl.is_qu', 0) // 未取出
        ->where('xl.addtime', '<=', $current_time) // 投资时间在今天之前或今天
        ->where('xl.endtime', '>', $current_time) // 到期时间在今天之后（仍在投资期间）
        ->field('u.id, u.username')
        ->group('u.id')
        ->select();
    
    writeLog("找到有活跃投资的用户数量: " . count($users_with_active_investments));
    
    $processed_users = 0;
    $total_income = 0;
    $skipped_users = 0;
    
    foreach ($users_with_active_investments as $user_item) {
        $uid = $user_item['id'];
        $username = $user_item['username'];
        
        writeLog("开始处理用户: $username (ID: $uid)");
        
        // 检查该用户今天是否已经发放过收益
        $today_income_check = Db::name('xy_balance_log')
            ->where('uid', $uid)
            ->where('type', 23) // 收益类型
            ->where('status', 1)
            ->where('addtime', '>=', $today_start)
            ->where('addtime', '<=', $today_end)
            ->count();
        
        if ($today_income_check > 0) {
            // 今天已经发放过收益，跳过
            writeLog("用户 $username (ID: $uid) 今日已发放收益，跳过");
            $skipped_users++;
            continue;
        }
        
        // 获取该用户所有未取出且在投资期间内的投资记录
        $user_investments = Db::name('xy_lixibao')
            ->where('uid', $uid)
            ->where('type', 1) // 转入类型
            ->where('is_qu', 0) // 未取出
            ->where('addtime', '<=', $current_time) // 投资时间在今天之前或今天
            ->where('endtime', '>', $current_time) // 到期时间在今天之后
            ->select();
        
        if (empty($user_investments)) {
            writeLog("用户 $username (ID: $uid) 没有有效的投资记录，跳过");
            continue;
        }
        
        writeLog("用户 $username (ID: $uid) 有 " . count($user_investments) . " 条有效投资记录");
        
        // 按产品类型分组计算每种产品的投资总额
        $product_amounts = [];
        $product_names = [];
        $product_rates = [];
        
        foreach ($user_investments as $investment) {
            $product_id = $investment['sid'];
            
            // 记录投资详情到日志
            $inv_details = sprintf(
                "投资ID: %d, 金额: %s, 投资时间: %s, 到期时间: %s, 剩余天数: %d", 
                $investment['id'],
                $investment['num'],
                date('Y-m-d H:i:s', $investment['addtime']),
                date('Y-m-d H:i:s', $investment['endtime']),
                ceil(($investment['endtime'] - $current_time) / (24 * 3600))
            );
            writeLog("  - $inv_details");
            
            // 如果是首次遇到该产品，获取产品信息
            if (!isset($product_amounts[$product_id])) {
                $product_amounts[$product_id] = 0;
                
                // 获取产品信息：名称和利率
                $product_info = Db::name('xy_lixibao_list')
                    ->where('id', $product_id)
                    ->field('name, bili, day')
                    ->find();
                    
                if ($product_info) {
                    $product_names[$product_id] = $product_info['name'];
                    $product_rates[$product_id] = $product_info['bili'];
                    writeLog("  - 产品信息: ID $product_id, 名称: {$product_info['name']}, 日利率: " . ($product_info['bili'] * 100) . "%");
                } else {
                    // 如果找不到产品信息，使用默认值
                    $product_names[$product_id] = "产品#$product_id";
                    $product_rates[$product_id] = 0.05; // 默认5%日利率
                    writeLog("  - 警告: 找不到产品ID $product_id 的信息，使用默认利率5%");
                }
            }
            
            // 累加该产品类型的投资金额
            $product_amounts[$product_id] += $investment['num'];
        }
        
        // 根据每种产品的利率计算每日收益
        $user_total_income = 0;
        $income_details = [];
        
        foreach ($product_amounts as $product_id => $amount) {
            if ($amount <= 0) continue; // 跳过零金额
            
            $rate = $product_rates[$product_id];
            $product_income = $amount * $rate; // 每日收益 = 投资金额 * 日利率
            $user_total_income += $product_income;
            
            // 记录每种产品的收益明细
            $income_details[] = [
                'product_id' => $product_id,
                'product_name' => $product_names[$product_id],
                'amount' => $amount,
                'rate' => $rate,
                'income' => $product_income
            ];
            
            writeLog("  - 产品收益: {$product_names[$product_id]} (ID: $product_id), 投资总额: $amount, 日利率: " . ($rate*100) . "%, 今日收益: $product_income");
        }
        
        if ($user_total_income <= 0) {
            writeLog("用户 $username (ID: $uid) 计算收益为0，跳过");
            continue;
        }
        
        writeLog("用户 $username (ID: $uid) 今日应得总收益: $user_total_income");
        
        try {
            // 开始事务
            Db::startTrans();
            
            // 1. 更新用户余额
            Db::name('xy_users')->where('id', $uid)->setInc('balance', $user_total_income);
            
            // 2. 为每个产品添加收益记录到xy_lixibao表
            foreach ($income_details as $detail) {
                Db::name('xy_lixibao')->insert([
                    'uid'         => $uid,
                    'num'         => $detail['income'],
                    'addtime'     => $current_time,
                    'type'        => 3, // 收益类型
                    'status'      => 1,
                    'yuji_num'    => $detail['income'],
                    'real_num'    => $detail['income'],
                    'is_sy'       => 1,
                    'sid'         => $detail['product_id'],
                    'shouxu'      => 0,
                    'bili'        => $detail['rate'],
                    'day'         => 1,
                    'update_time' => $current_time,
                ]);
                
                writeLog("  - 插入收益记录: 产品 {$detail['product_name']}, 收益 {$detail['income']}");
            }
            
            // 3. 添加余额变动记录
            $oid = getSn('LXB');
            Db::name('xy_balance_log')->insert([
                'uid'       => $uid,
                'oid'       => $oid,
                'num'       => $user_total_income,
                'type'      => 23, // 收益类型
                'status'    => 1,
                'addtime'   => $current_time
            ]);
            
            // 提交事务
            Db::commit();
            
            $processed_users++;
            $total_income += $user_total_income;
            
            writeLog("用户 $username (ID: $uid) 收益发放成功");
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $error_msg = "用户 $username (ID: $uid) 收益发放失败: " . $e->getMessage();
            writeLog($error_msg);
        }
    }
    
    writeLog("每日收益计算完成");
    writeLog("处理用户数: $processed_users");
    writeLog("跳过用户数: $skipped_users");
    writeLog("发放总收益: $total_income");
    
    // 输出HTML结果
    echo "<h2>每日收益计算完成</h2>";
    echo "<p>计算日期: $today_date</p>";
    echo "<p>处理用户数: $processed_users</p>";
    echo "<p>跳过用户数: $skipped_users</p>";
    echo "<p>发放总收益: $total_income</p>";
    echo "<p><a href='/index/crontab/lixibao_js'>查看系统原始收益计算</a></p>";
    echo "<p><a href='debug_lixibao_issue.php'>查看详细调试信息</a></p>";
    
} catch (\Exception $e) {
    writeLog("执行出错：" . $e->getMessage());
    echo "<h2>执行出错</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h3>使用说明</h3>";
echo "<p>该脚本修复了原系统的收益计算逻辑问题：</p>";
echo "<ul>";
echo "<li><strong>问题：</strong>原系统检查到用户今天已发放收益就跳过，导致每天只能发放一次收益</li>";
echo "<li><strong>修复：</strong>改为根据投资期限正确计算每日收益，确保投资期间每天都有收益</li>";
echo "<li><strong>逻辑：</strong>只要投资未到期且未取出，每天都应该产生收益</li>";
echo "</ul>";

echo "<h3>建议设置定时任务</h3>";
echo "<pre>";
echo "# 每天凌晨1点执行修复版收益计算\n";
echo "0 1 * * * cd " . __DIR__ . " && php fix_lixibao_income_daily.php >> " . __DIR__ . "/logs/daily_income_cron.log 2>&1\n";
echo "</pre>";
?> 