<?php
/**
 * VIP3用户进入VIP2接单页面错误修复脚本
 * 
 * 此脚本用于修复VIP等级权限验证问题，确保：
 * 1. VIP等级开关表存在并有正确数据
 * 2. VIP等级权限验证逻辑正确
 * 3. 数据库配置正确
 */

// 数据库配置 - 请根据实际情况修改
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',  // 请填写您的数据库名
    'username' => 'danss',  // 请填写您的数据库用户名
    'password' => 'MTbhcsYaFBrnMiX6',  // 请填写您的数据库密码
    'charset'  => 'utf8mb4'
];

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>VIP等级权限问题修复脚本</h1>";
    echo "<p>开始检查和修复VIP等级相关问题...</p>";
    
    // 1. 检查VIP等级开关表是否存在
    echo "<h2>1. 检查VIP等级开关表</h2>";
    $stmt = $db->query("SHOW TABLES LIKE 'xy_vip_level_switch'");
    $table_exists = $stmt->rowCount() > 0;
    
    if (!$table_exists) {
        echo "<p style='color:orange'>VIP等级开关表不存在，正在创建...</p>";
        
        // 创建VIP等级开关表
        $create_table_sql = "
        CREATE TABLE `xy_vip_level_switch` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
          `vip_level` int(11) NOT NULL COMMENT 'VIP等级 1-6',
          `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开启 1=开启 0=关闭',
          `switch_name` varchar(50) NOT NULL COMMENT '开关名称',
          `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
          `created_time` int(11) NOT NULL COMMENT '创建时间',
          `updated_time` int(11) NOT NULL COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `vip_level` (`vip_level`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP等级开关配置表'";
        
        $db->exec($create_table_sql);
        echo "<p style='color:green'>✅ VIP等级开关表创建成功</p>";
        
        // 插入默认数据
        $insert_data_sql = "
        INSERT INTO `xy_vip_level_switch` (`vip_level`, `is_enabled`, `switch_name`, `description`, `created_time`, `updated_time`) VALUES
        (1, 1, 'VIP1任务开关', 'VIP1等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
        (2, 1, 'VIP2任务开关', 'VIP2等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
        (3, 1, 'VIP3任务开关', 'VIP3等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
        (4, 1, 'VIP4任务开关', 'VIP4等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
        (5, 1, 'VIP5任务开关', 'VIP5等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
        (6, 1, 'VIP6任务开关', 'VIP6等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())";
        
        $db->exec($insert_data_sql);
        echo "<p style='color:green'>✅ 默认VIP等级开关数据插入成功</p>";
    } else {
        echo "<p style='color:green'>✅ VIP等级开关表已存在</p>";
        
        // 检查数据完整性
        $stmt = $db->query("SELECT COUNT(*) as count FROM xy_vip_level_switch");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count < 6) {
            echo "<p style='color:orange'>VIP等级开关数据不完整，正在补充...</p>";
            
            // 补充缺失的VIP等级数据
            for ($i = 1; $i <= 6; $i++) {
                $stmt = $db->prepare("SELECT id FROM xy_vip_level_switch WHERE vip_level = ?");
                $stmt->execute([$i]);
                
                if (!$stmt->fetch()) {
                    $insert_sql = "INSERT INTO xy_vip_level_switch (vip_level, is_enabled, switch_name, description, created_time, updated_time) 
                                  VALUES (?, 1, ?, ?, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())";
                    $stmt = $db->prepare($insert_sql);
                    $stmt->execute([$i, "VIP{$i}任务开关", "VIP{$i}等级任务接单开关"]);
                    echo "<p>补充VIP{$i}等级开关数据</p>";
                }
            }
        }
    }
    
    // 2. 检查VIP等级开关状态
    echo "<h2>2. 检查VIP等级开关状态</h2>";
    $stmt = $db->query("SELECT vip_level, is_enabled, switch_name FROM xy_vip_level_switch ORDER BY vip_level");
    $switches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>VIP等级</th><th>开关状态</th><th>开关名称</th></tr>";
    foreach ($switches as $switch) {
        $status_text = $switch['is_enabled'] ? '开启' : '关闭';
        $status_color = $switch['is_enabled'] ? 'green' : 'red';
        echo "<tr>";
        echo "<td>VIP{$switch['vip_level']}</td>";
        echo "<td style='color:{$status_color}'>{$status_text}</td>";
        echo "<td>{$switch['switch_name']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. 检查xy_goods_cate表的vip_level字段
    echo "<h2>3. 检查商品分类表VIP等级字段</h2>";
    $stmt = $db->query("SHOW COLUMNS FROM xy_goods_cate LIKE 'vip_level'");
    $vip_level_field_exists = $stmt->rowCount() > 0;
    
    if (!$vip_level_field_exists) {
        echo "<p style='color:orange'>xy_goods_cate表缺少vip_level字段，正在添加...</p>";
        $db->exec("ALTER TABLE xy_goods_cate ADD COLUMN vip_level int(11) DEFAULT 1 COMMENT 'VIP等级要求'");
        echo "<p style='color:green'>✅ vip_level字段添加成功</p>";
    } else {
        echo "<p style='color:green'>✅ xy_goods_cate表vip_level字段已存在</p>";
    }
    
    // 4. 检查商品分类的VIP等级设置
    echo "<h2>4. 检查商品分类VIP等级设置</h2>";
    $stmt = $db->query("SELECT id, name, vip_level FROM xy_goods_cate ORDER BY id");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($categories)) {
        echo "<p style='color:orange'>⚠️ 没有找到商品分类数据</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>分类ID</th><th>分类名称</th><th>VIP等级要求</th></tr>";
        foreach ($categories as $category) {
            $vip_level = $category['vip_level'] ?: 1;
            echo "<tr>";
            echo "<td>{$category['id']}</td>";
            echo "<td>{$category['name']}</td>";
            echo "<td>VIP{$vip_level}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 5. 测试VIP等级权限验证
    echo "<h2>5. 测试VIP等级权限验证</h2>";
    
    // 模拟VIP3用户访问VIP2任务的情况
    $user_vip_level = 3;
    $task_vip_level = 2;
    
    echo "<p>测试场景：VIP{$user_vip_level}用户访问VIP{$task_vip_level}任务</p>";
    
    // 检查用户等级是否足够
    if ($user_vip_level >= $task_vip_level) {
        echo "<p style='color:green'>✅ 用户VIP等级检查通过</p>";
        
        // 检查VIP等级开关是否开启
        $stmt = $db->prepare("SELECT is_enabled FROM xy_vip_level_switch WHERE vip_level = ?");
        $stmt->execute([$task_vip_level]);
        $switch_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($switch_info && $switch_info['is_enabled']) {
            echo "<p style='color:green'>✅ VIP{$task_vip_level}等级开关已开启</p>";
            echo "<p style='color:green'><strong>✅ 权限验证测试通过！VIP{$user_vip_level}用户可以访问VIP{$task_vip_level}任务</strong></p>";
        } else {
            echo "<p style='color:red'>❌ VIP{$task_vip_level}等级开关已关闭</p>";
            echo "<p style='color:orange'>需要开启VIP{$task_vip_level}等级开关</p>";
        }
    } else {
        echo "<p style='color:red'>❌ 用户VIP等级不足</p>";
    }
    
    echo "<h2>6. 修复建议</h2>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #007cba;'>";
    echo "<h3>如果问题仍然存在，请检查以下几点：</h3>";
    echo "<ol>";
    echo "<li><strong>确保VipLevelService.php文件存在</strong><br>路径：application/common/service/VipLevelService.php</li>";
    echo "<li><strong>检查RotOrder控制器修改</strong><br>确保已经应用了VIP等级权限验证逻辑</li>";
    echo "<li><strong>清除缓存</strong><br>删除runtime/cache/目录下的所有文件</li>";
    echo "<li><strong>检查错误日志</strong><br>查看runtime/log/目录下的错误日志文件</li>";
    echo "<li><strong>数据库连接</strong><br>确保应用程序能正常连接数据库</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>✅ 修复脚本执行完成</h2>";
    echo "<p style='color:green'>请重新测试VIP3用户访问VIP2接单页面的功能</p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库错误: " . $e->getMessage() . "</p>";
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 执行错误: " . $e->getMessage() . "</p>";
}
?>
