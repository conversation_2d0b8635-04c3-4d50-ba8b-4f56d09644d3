<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',  // 请填写您的数据库名
    'username' => 'danss',  // 请填写您的数据库用户名
    'password' => 'MTbhcsYaFBrnMiX6',  // 请填写您的数据库密码
    'charset'  => 'utf8'
];

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>开始修复VIP等级管理菜单结构</h1>";
    
    // 1. 查找现有的VIP等级开关管理菜单
    $stmt = $db->prepare("SELECT id, pid, title FROM system_menu WHERE title='VIP等级开关管理' OR title LIKE '%VIP等级%' LIMIT 1");
    $stmt->execute();
    $vip_menu = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if($vip_menu) {
        echo "<p>找到现有VIP等级管理菜单，ID: {$vip_menu['id']}, 当前父菜单ID: {$vip_menu['pid']}</p>";
        
        // 2. 如果当前不是主菜单（pid != 0），则修改为主菜单
        if($vip_menu['pid'] != 0) {
            echo "<p>当前VIP等级管理是子菜单，正在修改为主菜单...</p>";
            
            // 获取合适的排序值（在现有主菜单之后）
            $stmt = $db->query("SELECT MAX(sort) as max_sort FROM system_menu WHERE pid = 0");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $new_sort = ($result['max_sort'] ?? 0) + 10;
            
            // 更新菜单为主菜单
            $sql = "UPDATE system_menu SET pid = 0, sort = :sort, icon = 'fa fa-vip' WHERE id = :id";
            $stmt = $db->prepare($sql);
            $stmt->execute(['sort' => $new_sort, 'id' => $vip_menu['id']]);
            
            echo "<p style='color:green'>已将VIP等级管理修改为主菜单，排序值: {$new_sort}</p>";
        } else {
            echo "<p style='color:blue'>VIP等级管理已经是主菜单了</p>";
        }
        
        // 3. 确保菜单标题正确
        $sql = "UPDATE system_menu SET title = 'VIP等级管理', icon = 'fa fa-vip' WHERE id = :id";
        $stmt = $db->prepare($sql);
        $stmt->execute(['id' => $vip_menu['id']]);
        
        echo "<p>已更新菜单标题为'VIP等级管理'</p>";
        
    } else {
        echo "<p style='color:orange'>未找到现有的VIP等级管理菜单，正在创建新的主菜单...</p>";
        
        // 获取合适的排序值
        $stmt = $db->query("SELECT MAX(sort) as max_sort FROM system_menu WHERE pid = 0");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $new_sort = ($result['max_sort'] ?? 0) + 10;
        
        // 创建新的主菜单
        $sql = "INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
                VALUES (0, 'VIP等级管理', 'admin/VipLevelSwitch/index', 'admin/VipLevelSwitch/index', '', 'fa fa-vip', :sort, 1)";
        $stmt = $db->prepare($sql);
        $stmt->execute(['sort' => $new_sort]);
        $vip_menu_id = $db->lastInsertId();
        
        echo "<p style='color:green'>已创建新的VIP等级管理主菜单，ID: {$vip_menu_id}</p>";
        
        // 添加子菜单项
        $submenus = [
            ['title' => '等级开关管理', 'node' => 'admin/VipLevelSwitch/index', 'url' => 'admin/VipLevelSwitch/index'],
            ['title' => '切换开关', 'node' => 'admin/VipLevelSwitch/toggleSwitch', 'url' => 'admin/VipLevelSwitch/toggleSwitch'],
            ['title' => '批量操作', 'node' => 'admin/VipLevelSwitch/batchToggle', 'url' => 'admin/VipLevelSwitch/batchToggle'],
            ['title' => '重置开关', 'node' => 'admin/VipLevelSwitch/resetAll', 'url' => 'admin/VipLevelSwitch/resetAll']
        ];
        
        foreach($submenus as $index => $submenu) {
            $sql_sub = "INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
                        VALUES (:pid, :title, :node, :url, '', '', :sort, 1)";
            $stmt_sub = $db->prepare($sql_sub);
            $stmt_sub->execute([
                'pid' => $vip_menu_id,
                'title' => $submenu['title'],
                'node' => $submenu['node'],
                'url' => $submenu['url'],
                'sort' => ($index + 1) * 10
            ]);
        }
        
        echo "<p>已添加" . count($submenus) . "个子菜单项</p>";
    }
    
    // 4. 检查是否有其他VIP相关的菜单需要整合
    $stmt = $db->query("SELECT id, pid, title, url FROM system_menu WHERE (title LIKE '%VIP%' OR url LIKE '%vip%' OR url LIKE '%level%') AND title != 'VIP等级管理'");
    $other_vip_menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if($other_vip_menus) {
        echo "<h3>发现其他VIP相关菜单：</h3>";
        foreach($other_vip_menus as $menu) {
            echo "<p>- ID: {$menu['id']}, 标题: {$menu['title']}, URL: {$menu['url']}</p>";
        }
        
        // 可以选择将这些菜单移动到VIP等级管理下
        echo "<p style='color:blue'>如需整合这些菜单，请手动调整</p>";
    }
    
    // 5. 清除缓存
    echo "<h3>正在清除系统缓存...</h3>";
    
    // 清除数据库缓存
    try {
        $db->exec("TRUNCATE TABLE system_cache");
        echo "<p>已清除系统缓存表</p>";
    } catch(Exception $e) {
        echo "<p>系统可能没有缓存表，跳过此步骤</p>";
    }
    
    // 清除文件缓存
    $cache_dirs = [
        'runtime/cache',
        'runtime/temp', 
        'application/runtime/cache',
        'application/runtime/temp'
    ];
    
    foreach($cache_dirs as $dir) {
        if(is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach($files as $file) {
                if(is_file($file)) {
                    unlink($file);
                }
            }
            echo "<p>已清除缓存目录: {$dir}</p>";
        }
    }
    
    echo "<h2 style='color:green'>修复完成！</h2>";
    echo "<p><strong>VIP等级管理现在已经是独立的主菜单了</strong></p>";
    echo "<p>请<a href='/admin.html' target='_blank'>重新登录后台</a>查看菜单变化</p>";
    echo "<p style='color:red'><strong>重要提醒：</strong>请清除浏览器缓存后重新登录，确保看到最新的菜单结构</p>";
    
} catch(PDOException $e) {
    die("<p style='color:red'>数据库错误: " . $e->getMessage() . "</p>");
}
?> 