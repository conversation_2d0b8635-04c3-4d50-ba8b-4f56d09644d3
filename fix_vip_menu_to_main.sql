-- 修复VIP等级管理菜单结构 - 将其从子菜单改为主菜单
-- 执行前请备份数据库

-- 1. 查看当前VIP等级管理菜单状态
SELECT id, pid, title, sort, status FROM system_menu WHERE title LIKE '%VIP等级%';

-- 2. 获取当前主菜单的最大排序值
SELECT MAX(sort) as max_sort FROM system_menu WHERE pid = 0;

-- 3. 将VIP等级开关管理菜单修改为主菜单
-- 注意：请将下面的 @new_sort 替换为实际的排序值（上面查询结果的max_sort + 10）
UPDATE system_menu 
SET pid = 0, 
    title = 'VIP等级管理',
    icon = 'fa fa-vip',
    sort = (SELECT MAX(sort) + 10 FROM (SELECT sort FROM system_menu WHERE pid = 0) as temp),
    status = 1
WHERE title = 'VIP等级开关管理' OR title LIKE '%VIP等级开关%';

-- 4. 如果上面的更新没有影响任何行，说明菜单不存在，需要创建新菜单
-- 请根据实际情况执行以下插入语句

-- 获取新的排序值
SET @new_sort = (SELECT MAX(sort) + 10 FROM system_menu WHERE pid = 0);

-- 创建主菜单（如果不存在）
INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
SELECT 0, 'VIP等级管理', 'admin/VipLevelSwitch/index', 'admin/VipLevelSwitch/index', '', 'fa fa-vip', @new_sort, 1
WHERE NOT EXISTS (SELECT 1 FROM system_menu WHERE title = 'VIP等级管理');

-- 获取VIP等级管理菜单的ID
SET @vip_menu_id = (SELECT id FROM system_menu WHERE title = 'VIP等级管理' LIMIT 1);

-- 5. 添加或更新子菜单项
-- 等级开关管理
INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
SELECT @vip_menu_id, '等级开关管理', 'admin/VipLevelSwitch/index', 'admin/VipLevelSwitch/index', '', '', 10, 1
WHERE NOT EXISTS (SELECT 1 FROM system_menu WHERE pid = @vip_menu_id AND title = '等级开关管理');

-- 切换开关
INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
SELECT @vip_menu_id, '切换开关', 'admin/VipLevelSwitch/toggleSwitch', 'admin/VipLevelSwitch/toggleSwitch', '', '', 20, 1
WHERE NOT EXISTS (SELECT 1 FROM system_menu WHERE pid = @vip_menu_id AND title = '切换开关');

-- 批量操作
INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
SELECT @vip_menu_id, '批量操作', 'admin/VipLevelSwitch/batchToggle', 'admin/VipLevelSwitch/batchToggle', '', '', 30, 1
WHERE NOT EXISTS (SELECT 1 FROM system_menu WHERE pid = @vip_menu_id AND title = '批量操作');

-- 重置开关
INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
SELECT @vip_menu_id, '重置开关', 'admin/VipLevelSwitch/resetAll', 'admin/VipLevelSwitch/resetAll', '', '', 40, 1
WHERE NOT EXISTS (SELECT 1 FROM system_menu WHERE pid = @vip_menu_id AND title = '重置开关');

-- 6. 清除可能的缓存表
TRUNCATE TABLE system_cache;

-- 7. 验证修复结果
SELECT 
    m1.id,
    m1.pid,
    m1.title,
    m1.sort,
    m1.status,
    CASE WHEN m1.pid = 0 THEN '主菜单' ELSE '子菜单' END as menu_type
FROM system_menu m1 
WHERE m1.title = 'VIP等级管理' 
   OR m1.pid = (SELECT id FROM system_menu WHERE title = 'VIP等级管理' LIMIT 1)
ORDER BY m1.pid, m1.sort;

-- 修复完成提示
SELECT 'VIP等级管理菜单结构修复完成！请重新登录后台查看效果。' as message; 