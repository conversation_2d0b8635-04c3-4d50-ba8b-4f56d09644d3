<?php
// 强制每日收益计算脚本 - 绕过重复检查逻辑
echo "=== 强制每日收益计算开始 ===\n";
echo "执行时间: " . date('Y-m-d H:i:s') . "\n\n";

// 数据库配置
$host = '127.0.0.1';
$port = 3306;
$database = 'g5_vt1685_site';
$username = 'g5_vt1685_site';
$password = 'g5_vt1685_site';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

// 当前时间
$current_time = time();
$today_start = strtotime(date('Y-m-d'));
$today_end = $today_start + 86400;

echo "计算日期: " . date('Y-m-d') . "\n";
echo "时间范围: " . date('Y-m-d H:i:s', $today_start) . " 到 " . date('Y-m-d H:i:s', $today_end) . "\n\n";

// 1. 获取所有有活跃投资的用户（不检查是否已有今日收益）
$stmt = $pdo->query("
    SELECT DISTINCT l.uid, u.username,
           COUNT(l.id) as investment_count,
           SUM(l.num) as total_investment
    FROM xy_lixibao l
    LEFT JOIN xy_users u ON l.uid = u.id
    WHERE l.type = 1 AND l.status = 1 AND l.endtime > $current_time
    GROUP BY l.uid
    ORDER BY l.uid
");

$users_with_investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "找到 " . count($users_with_investments) . " 个用户有活跃投资\n\n";

if (empty($users_with_investments)) {
    echo "没有找到有活跃投资的用户\n";
    exit;
}

// 2. 获取产品信息
$stmt = $pdo->query("SELECT * FROM xy_lixibao_list ORDER BY id");
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);
$product_info = [];
foreach ($products as $product) {
    $product_info[$product['id']] = [
        'name' => isset($product['title']) ? $product['title'] : (isset($product['name']) ? $product['name'] : '利息宝'),
        'rate' => isset($product['rate']) ? $product['rate'] : (isset($product['bili']) ? $product['bili'] : 0.05)
    ];
}

// 默认产品信息
$default_product = [
    'name' => '利息宝',
    'rate' => 0.05 // 5%
];

// 3. 强制为每个用户计算今日收益
$total_processed = 0;
$total_income = 0;
$skip_existing = 0;

echo "=== 开始计算收益 ===\n";

foreach ($users_with_investments as $user) {
    $uid = $user['uid'];
    $username = $user['username'];
    
    echo "处理用户: $username (ID: $uid)\n";
    
    // 检查今天是否已有收益记录
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM xy_balance_log 
        WHERE uid = ? AND type = 23 
        AND addtime BETWEEN ? AND ?
    ");
    $stmt->execute([$uid, $today_start, $today_end]);
    $today_income_exists = $stmt->fetchColumn();
    
    if ($today_income_exists > 0) {
        echo "  ⚠️  用户今天已有 $today_income_exists 条收益记录，跳过\n\n";
        $skip_existing++;
        continue;
    }
    
    // 获取该用户的活跃投资
    $stmt = $pdo->prepare("
        SELECT l.*, ll.rate, ll.bili, ll.title, ll.name
        FROM xy_lixibao l
        LEFT JOIN xy_lixibao_list ll ON l.product_id = ll.id
        WHERE l.uid = ? AND l.type = 1 AND l.status = 1 AND l.endtime > ?
    ");
    $stmt->execute([$uid, $current_time]);
    $user_investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $user_total_income = 0;
    
    // 按产品分组计算收益
    $product_amounts = [];
    foreach ($user_investments as $investment) {
        $product_id = $investment['product_id'] ? $investment['product_id'] : 1;
        
        if (!isset($product_amounts[$product_id])) {
            $product_amounts[$product_id] = 0;
        }
        $product_amounts[$product_id] += $investment['num'];
    }
    
    // 为每个产品计算收益
    foreach ($product_amounts as $product_id => $amount) {
        $product = isset($product_info[$product_id]) ? $product_info[$product_id] : $default_product;
        $rate = $product['rate'];
        $product_income = $amount * $rate;
        $user_total_income += $product_income;
        
        echo "  产品ID: $product_id, 投资金额: ¥$amount, 利率: " . ($rate * 100) . "%, 收益: ¥$product_income\n";
        
        // 插入利息宝收益记录
        $stmt = $pdo->prepare("
            INSERT INTO xy_lixibao (uid, num, addtime, type, status, yuji_num, real_num, is_sy, sid, shouxu, bili, day, update_time)
            VALUES (?, ?, ?, 3, 1, ?, ?, 1, ?, 0, ?, 1, ?)
        ");
        $stmt->execute([
            $uid,
            $product_income,
            $current_time, // 使用当前时间
            $product_income,
            $product_income,
            $product_id,
            $rate,
            $current_time
        ]);
    }
    
    if ($user_total_income > 0) {
        // 更新用户余额
        $stmt = $pdo->prepare("UPDATE xy_users SET balance = balance + ? WHERE id = ?");
        $stmt->execute([$user_total_income, $uid]);
        
        // 插入余额变动记录
        $oid = 'LXB' . date('YmdHis') . sprintf('%04d', $uid);
        $stmt = $pdo->prepare("
            INSERT INTO xy_balance_log (uid, oid, num, type, status, addtime, remark)
            VALUES (?, ?, ?, 23, 1, ?, ?)
        ");
        $stmt->execute([
            $uid,
            $oid,
            $user_total_income,
            $current_time,
            "利息宝每日收益 - " . date('Y-m-d')
        ]);
        
        echo "  ✅ 用户 $username 收益计算成功，总金额: ¥$user_total_income\n";
        $total_processed++;
        $total_income += $user_total_income;
    } else {
        echo "  ⚠️  用户 $username 计算收益为0\n";
    }
    
    echo "\n";
}

// 输出结果
echo "=== 计算完成 ===\n";
echo "总用户数: " . count($users_with_investments) . "\n";
echo "跳过已有收益的用户数: $skip_existing\n";
echo "成功处理用户数: $total_processed\n";
echo "总收益金额: ¥$total_income\n";
echo "计算时间: " . date('Y-m-d H:i:s') . "\n";

// 保存日志
$log_content = "强制每日收益计算日志\n";
$log_content .= "执行时间: " . date('Y-m-d H:i:s') . "\n";
$log_content .= "总用户数: " . count($users_with_investments) . "\n";
$log_content .= "跳过用户数: $skip_existing\n";
$log_content .= "处理用户数: $total_processed\n";
$log_content .= "总收益金额: ¥$total_income\n";

file_put_contents('force_daily_income.log', $log_content, FILE_APPEND);
echo "\n日志已保存到: force_daily_income.log\n";

echo "\n=== 建议 ===\n";
echo "1. 如果今天已有收益记录的用户很多，说明定时任务正在工作\n";
echo "2. 如果处理的用户数很少，可能是投资数据或产品配置有问题\n";
echo "3. 建议检查用户反馈的具体账户情况\n";

echo "\n计算完成！\n";
?> 