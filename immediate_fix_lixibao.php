<?php
// 立即修复利息宝收益脚本
// 直接通过数据库操作补发昨天的收益

echo "=== 利息宝收益立即修复开始 ===\n";
echo "修复时间: " . date('Y-m-d H:i:s') . "\n\n";

// 数据库配置
$host = '127.0.0.1';
$port = 3306;
$database = 'g5_vt1685_site';
$username = 'g5_vt1685_site';
$password = 'g5_vt1685_site';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

// 计算昨天的时间范围
$yesterday_start = strtotime(date('Y-m-d', strtotime('-1 day')));
$yesterday_end = $yesterday_start + 86400;
$yesterday_date = date('Y-m-d', strtotime('-1 day'));

echo "修复日期: $yesterday_date\n";
echo "时间范围: " . date('Y-m-d H:i:s', $yesterday_start) . " 到 " . date('Y-m-d H:i:s', $yesterday_end) . "\n\n";

// 1. 检查昨天是否已有收益记录
$stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime BETWEEN $yesterday_start AND $yesterday_end");
$existing_income = $stmt->fetchColumn();

if ($existing_income > 0) {
    echo "⚠️  昨天已有 $existing_income 条收益记录，是否继续？(y/n): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    if (trim($line) != 'y') {
        echo "操作已取消\n";
        exit;
    }
    fclose($handle);
}

// 2. 查找昨天有活跃投资的用户
echo "=== 查找需要补发收益的用户 ===\n";

$stmt = $pdo->query("
    SELECT DISTINCT l.uid, u.username, u.balance,
           COUNT(l.id) as investment_count,
           SUM(l.num) as total_investment
    FROM xy_lixibao l
    LEFT JOIN xy_users u ON l.uid = u.id
    WHERE l.type = 1 AND l.status = 1 
    AND l.addtime <= $yesterday_end 
    AND l.endtime > $yesterday_start
    GROUP BY l.uid
    ORDER BY l.uid
");

$users_to_fix = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "找到 " . count($users_to_fix) . " 个用户需要补发收益\n\n";

if (empty($users_to_fix)) {
    echo "没有找到需要补发收益的用户\n";
    exit;
}

// 3. 获取产品信息
$stmt = $pdo->query("SELECT * FROM xy_lixibao_list ORDER BY id");
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);
$product_info = [];
foreach ($products as $product) {
    $product_info[$product['id']] = [
        'name' => isset($product['title']) ? $product['title'] : (isset($product['name']) ? $product['name'] : '利息宝'),
        'rate' => isset($product['rate']) ? $product['rate'] : (isset($product['bili']) ? $product['bili'] : 0.05)
    ];
}

// 默认产品信息
$default_product = [
    'name' => '利息宝',
    'rate' => 0.05 // 5%
];

// 4. 开始修复
$total_fixed = 0;
$total_income = 0;
$log_entries = [];

echo "=== 开始补发收益 ===\n";

foreach ($users_to_fix as $user) {
    $uid = $user['uid'];
    $username = $user['username'];
    
    echo "处理用户: $username (ID: $uid)\n";
    
    // 获取该用户昨天的投资详情
    $stmt = $pdo->prepare("
        SELECT l.*, ll.rate, ll.bili, ll.title, ll.name
        FROM xy_lixibao l
        LEFT JOIN xy_lixibao_list ll ON l.product_id = ll.id
        WHERE l.uid = ? AND l.type = 1 AND l.status = 1 
        AND l.addtime <= ? AND l.endtime > ?
    ");
    $stmt->execute([$uid, $yesterday_end, $yesterday_start]);
    $user_investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $user_total_income = 0;
    
    // 按产品计算收益
    $product_amounts = [];
    foreach ($user_investments as $investment) {
        $product_id = $investment['product_id'] ? $investment['product_id'] : 1;
        
        if (!isset($product_amounts[$product_id])) {
            $product_amounts[$product_id] = 0;
        }
        $product_amounts[$product_id] += $investment['num'];
    }
    
    // 计算每个产品的收益
    foreach ($product_amounts as $product_id => $amount) {
        $product = isset($product_info[$product_id]) ? $product_info[$product_id] : $default_product;
        $rate = $product['rate'];
        $product_income = $amount * $rate;
        $user_total_income += $product_income;
        
        echo "  产品ID: $product_id, 投资金额: ¥$amount, 利率: " . ($rate * 100) . "%, 收益: ¥$product_income\n";
        
        // 插入利息宝收益记录
        $stmt = $pdo->prepare("
            INSERT INTO xy_lixibao (uid, num, addtime, type, status, yuji_num, real_num, is_sy, sid, shouxu, bili, day, update_time)
            VALUES (?, ?, ?, 3, 1, ?, ?, 1, ?, 0, ?, 1, ?)
        ");
        $stmt->execute([
            $uid,
            $product_income,
            $yesterday_start + 3600, // 昨天凌晨1点
            $product_income,
            $product_income,
            $product_id,
            $rate,
            $yesterday_start + 3600
        ]);
    }
    
    if ($user_total_income > 0) {
        // 更新用户余额
        $stmt = $pdo->prepare("UPDATE xy_users SET balance = balance + ? WHERE id = ?");
        $stmt->execute([$user_total_income, $uid]);
        
        // 插入余额变动记录
        $oid = 'LXB' . date('YmdHis') . sprintf('%04d', $uid);
        $stmt = $pdo->prepare("
            INSERT INTO xy_balance_log (uid, oid, num, type, status, addtime, remark)
            VALUES (?, ?, ?, 23, 1, ?, ?)
        ");
        $stmt->execute([
            $uid,
            $oid,
            $user_total_income,
            $yesterday_start + 3600,
            "利息宝收益补发 - $yesterday_date"
        ]);
        
        echo "  ✅ 用户 $username 补发收益成功，总金额: ¥$user_total_income\n";
        $total_fixed++;
        $total_income += $user_total_income;
        
        $log_entries[] = "用户: $username (ID: $uid), 补发收益: ¥$user_total_income";
    } else {
        echo "  ⚠️  用户 $username 计算收益为0，跳过\n";
    }
    
    echo "\n";
}

// 5. 输出修复结果
echo "=== 修复完成 ===\n";
echo "修复用户数: $total_fixed\n";
echo "总补发金额: ¥$total_income\n";
echo "修复日期: $yesterday_date\n\n";

// 6. 保存日志
$log_content = "=== 利息宝收益补发日志 ===\n";
$log_content .= "修复时间: " . date('Y-m-d H:i:s') . "\n";
$log_content .= "修复日期: $yesterday_date\n";
$log_content .= "修复用户数: $total_fixed\n";
$log_content .= "总补发金额: ¥$total_income\n\n";
$log_content .= "详细记录:\n";
foreach ($log_entries as $entry) {
    $log_content .= $entry . "\n";
}

file_put_contents('lixibao_fix_' . date('Ymd_His') . '.log', $log_content);
echo "修复日志已保存到: lixibao_fix_" . date('Ymd_His') . ".log\n";

echo "\n=== 建议后续操作 ===\n";
echo "1. 检查定时任务配置，确保今后能正常执行\n";
echo "2. 监控今天的收益计算是否正常\n";
echo "3. 定期检查系统日志\n";

echo "\n修复完成！\n";
?> 