<?php
// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');
define('THINK_PATH', __DIR__ . '/../thinkphp/');

// 加载核心框架
require __DIR__ . '/../thinkphp/base.php';

// 检查和恢复会话
session_start();

// 获取会话和Cookie中的用户ID
$uid = \think\Session::get('user_id');
$cookie_uid = isset($_COOKIE['user_id']) ? $_COOKIE['user_id'] : null;

// 如果会话中没有但Cookie中有，则恢复到会话中
if (!$uid && $cookie_uid) {
    \think\Session::set('user_id', $cookie_uid);
    $uid = $cookie_uid;
}

// 添加调试参数(可用于跟踪问题)
$debug = isset($_GET['debug']) ? $_GET['debug'] : 0;
if ($debug) {
    echo "<h3>会话状态检查</h3>";
    echo "<p>会话ID: " . session_id() . "</p>";
    echo "<p>会话中用户ID: " . ($uid ? $uid : '未设置') . "</p>";
    echo "<p>Cookie中用户ID: " . ($cookie_uid ? $cookie_uid : '未设置') . "</p>";
    echo "<p>所有Cookie:</p><pre>";
    print_r($_COOKIE);
    echo "</pre>";
    echo "<p>所有会话变量:</p><pre>";
    print_r($_SESSION);
    echo "</pre>";
    exit;
}

// 用户未登录的情况
if (!$uid) {
    header('Location: /index/user/login');
    exit;
}

// 验证用户是否存在且状态正常
try {
    $user = \think\Db::name('xy_users')->where('id', $uid)->where('status', 1)->find();
    if (!$user) {
        // 用户不存在或状态异常，清除会话并跳转到登录页
        \think\Session::clear();
        setcookie('user_id', '', time()-3600, '/');
        header('Location: /index/user/login');
        exit;
    }
} catch (\Exception $e) {
    // 数据库查询失败，跳转到登录页
    header('Location: /index/user/login');
    exit;
}

// 用户已登录且验证通过，直接进入聊天页
header('Location: /index/chat/index');
exit; 