<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>检查会员等级表结构</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>步骤1: 查看xy_level表结构</h2>";
    $stmt = $db->query("DESCRIBE xy_level");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>备注</th></tr>";
    foreach($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>{$col['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>步骤2: 查看现有数据</h2>";
    $stmt = $db->query("SELECT * FROM xy_level ORDER BY level");
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if($data) {
        echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
        echo "<tr>";
        foreach(array_keys($data[0]) as $key) {
            echo "<th>{$key}</th>";
        }
        echo "</tr>";
        foreach($data as $row) {
            echo "<tr>";
            foreach($row as $value) {
                echo "<td>{$value}</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>表中无数据</p>";
    }
    
    echo "<h2>步骤3: 检查是否有任务开关字段</h2>";
    $has_task_switch = false;
    foreach($columns as $col) {
        if($col['Field'] == 'task_enabled') {
            $has_task_switch = true;
            break;
        }
    }
    
    if(!$has_task_switch) {
        echo "<p style='color:orange'>⚠️ 没有任务开关字段，正在添加...</p>";
        
        $alter_sql = "ALTER TABLE `xy_level` ADD COLUMN `task_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '任务开关 1=开启 0=关闭'";
        $db->exec($alter_sql);
        
        echo "<p style='color:green'>✅ 已添加 task_enabled 字段</p>";
        
        // 默认所有等级都开启任务
        $db->exec("UPDATE xy_level SET task_enabled = 1");
        echo "<p style='color:green'>✅ 已将所有等级的任务开关设为开启</p>";
    } else {
        echo "<p style='color:green'>✅ 任务开关字段已存在</p>";
    }
    
    echo "<h2 style='color:green'>🎉 检查完成！</h2>";
    echo "<p>现在可以在会员等级管理页面添加开关功能了</p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 操作失败: " . $e->getMessage() . "</p>";
}
?> 