<?php
/**
 * 钱包地址同步问题诊断脚本
 * 检查 xy_bankinfo 和 xy_user_wallet 表的数据差异
 * 运行目录：public/
 * 独立版本 - 不依赖ThinkPHP框架
 */

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

echo "<h1>钱包地址同步问题诊断工具</h1>";
echo "<p><strong>运行目录：</strong>public/ (独立版本)</p>";

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'g5_vt1685_site',
    'username' => 'g5_vt1685_site',
    'password' => 'g5_vt1685_site',
    'charset' => 'utf8mb4'
];

// 连接数据库
try {
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ 数据库连接失败</h3>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库配置是否正确。</p>";
    echo "</div>";
    exit;
}

try {
    // 检查 xy_bankinfo 表中的钱包数据
    echo "<h2>📊 1. xy_bankinfo 表中的钱包数据（前端用户修改的数据）</h2>";
    $sql1 = "SELECT id, uid, username, cardnum as wallet_address, wallet_type, status FROM xy_bankinfo WHERE cardnum != '' AND wallet_type != ''";
    $stmt1 = $pdo->prepare($sql1);
    $stmt1->execute();
    $bankinfo_data = $stmt1->fetchAll();
    
    if ($bankinfo_data && count($bankinfo_data) > 0) {
        echo "<div style='overflow-x: auto;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>用户ID</th><th>用户名</th><th>钱包地址</th><th>钱包类型</th><th>状态</th></tr>";
        foreach ($bankinfo_data as $item) {
            $status_text = $item['status'] ? '<span style="color: green;">正常</span>' : '<span style="color: red;">禁用</span>';
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>{$item['uid']}</td>";
            echo "<td>{$item['username']}</td>";
            echo "<td style='font-family: monospace;'>" . substr($item['wallet_address'], 0, 10) . "..." . substr($item['wallet_address'], -10) . "</td>";
            echo "<td>{$item['wallet_type']}</td>";
            echo "<td>{$status_text}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        echo "<p style='color: green;'><strong>✓ 共找到 " . count($bankinfo_data) . " 条钱包数据</strong></p>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "<p style='color: #721c24;'><strong>❌ xy_bankinfo 表中没有找到钱包数据</strong></p>";
        echo "</div>";
        $bankinfo_data = [];
    }

    // 检查 xy_user_wallet 表中的钱包数据
    echo "<h2>📊 2. xy_user_wallet 表中的钱包数据（后端管理员看到的数据）</h2>";
    $sql2 = "SELECT id, uid, wallet_address, wallet_type, status, addtime FROM xy_user_wallet";
    $stmt2 = $pdo->prepare($sql2);
    $stmt2->execute();
    $user_wallet_data = $stmt2->fetchAll();
    
    if ($user_wallet_data && count($user_wallet_data) > 0) {
        echo "<div style='overflow-x: auto;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>用户ID</th><th>钱包地址</th><th>钱包类型</th><th>状态</th><th>添加时间</th></tr>";
        foreach ($user_wallet_data as $item) {
            $status_text = $item['status'] ? '<span style="color: green;">正常</span>' : '<span style="color: red;">禁用</span>';
            $addtime = $item['addtime'] ? date('Y-m-d H:i:s', $item['addtime']) : '未知';
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>{$item['uid']}</td>";
            echo "<td style='font-family: monospace;'>" . substr($item['wallet_address'], 0, 10) . "..." . substr($item['wallet_address'], -10) . "</td>";
            echo "<td>{$item['wallet_type']}</td>";
            echo "<td>{$status_text}</td>";
            echo "<td>{$addtime}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        echo "<p style='color: green;'><strong>✓ 共找到 " . count($user_wallet_data) . " 条钱包数据</strong></p>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "<p style='color: #721c24;'><strong>❌ xy_user_wallet 表中没有找到钱包数据</strong></p>";
        echo "</div>";
        $user_wallet_data = [];
    }

    // 分析数据差异
    echo "<h2>🔍 3. 数据差异分析</h2>";
    
    // 获取所有有钱包数据的用户ID
    $bankinfo_uids = [];
    foreach ($bankinfo_data as $item) {
        $bankinfo_uids[] = $item['uid'];
    }
    
    $user_wallet_uids = [];
    foreach ($user_wallet_data as $item) {
        $user_wallet_uids[] = $item['uid'];
    }
    
    $only_in_bankinfo = array_diff($bankinfo_uids, $user_wallet_uids);
    $only_in_user_wallet = array_diff($user_wallet_uids, $bankinfo_uids);
    
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>⚠️ 只在 xy_bankinfo 表中有数据的用户：</h3>";
    if (count($only_in_bankinfo) > 0) {
        echo "<p style='color: #856404;'><strong>用户ID:</strong> " . implode(', ', $only_in_bankinfo) . "</p>";
        echo "<p><strong style='color: red;'>问题：</strong>这些用户在前端修改了钱包地址，但后端管理员看不到！</p>";
    } else {
        echo "<p style='color: green;'><strong>✓ 没有</strong></p>";
    }
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>📋 只在 xy_user_wallet 表中有数据的用户：</h3>";
    if (count($only_in_user_wallet) > 0) {
        echo "<p style='color: #0c5460;'><strong>用户ID:</strong> " . implode(', ', $only_in_user_wallet) . "</p>";
        echo "<p><strong style='color: orange;'>说明：</strong>这些用户的钱包地址只有管理员添加的，前端看不到！</p>";
    } else {
        echo "<p style='color: green;'><strong>✓ 没有</strong></p>";
    }
    echo "</div>";

    // 检查同一用户在两个表中的数据是否一致
    echo "<h3>🔄 同一用户在两个表中的数据对比：</h3>";
    $common_uids = array_intersect($bankinfo_uids, $user_wallet_uids);
    
    $inconsistent_count = 0;
    if (count($common_uids) > 0) {
        echo "<div style='overflow-x: auto;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>用户ID</th><th>xy_bankinfo钱包地址</th><th>xy_user_wallet钱包地址</th><th>是否一致</th></tr>";
        
        foreach ($common_uids as $uid) {
            $bankinfo_item = null;
            foreach ($bankinfo_data as $item) {
                if ($item['uid'] == $uid) {
                    $bankinfo_item = $item;
                    break;
                }
            }
            
            $user_wallet_item = null;
            foreach ($user_wallet_data as $item) {
                if ($item['uid'] == $uid) {
                    $user_wallet_item = $item;
                    break;
                }
            }
            
            if ($bankinfo_item && $user_wallet_item) {
                $is_same = ($bankinfo_item['wallet_address'] === $user_wallet_item['wallet_address']);
                if (!$is_same) $inconsistent_count++;
                
                echo "<tr>";
                echo "<td>{$uid}</td>";
                echo "<td style='font-family: monospace;'>" . substr($bankinfo_item['wallet_address'], 0, 15) . "...</td>";
                echo "<td style='font-family: monospace;'>" . substr($user_wallet_item['wallet_address'], 0, 15) . "...</td>";
                echo "<td style='color: " . ($is_same ? 'green' : 'red') . "; font-weight: bold;'>" . ($is_same ? '✓ 一致' : '❌ 不一致') . "</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
        echo "</div>";
        
        if ($inconsistent_count > 0) {
            echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
            echo "<p style='color: #721c24;'><strong>⚠️ 发现 {$inconsistent_count} 个用户的钱包地址不一致！</strong></p>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: #6c757d;'>没有用户在两个表中都有数据</p>";
    }

    echo "<h2>📋 4. 问题总结</h2>";
    echo "<div style='background: #f0f0f0; padding: 20px; border-left: 4px solid #ff6b6b; margin: 20px 0;'>";
    echo "<h3 style='color: #dc3545;'>🔍 问题根源：</h3>";
    echo "<ul style='line-height: 1.6;'>";
    echo "<li><strong>前端用户修改钱包地址</strong>：数据保存到 <code>xy_bankinfo</code> 表的 <code>cardnum</code> 字段</li>";
    echo "<li><strong>后端管理员查看钱包地址</strong>：从 <code>xy_user_wallet</code> 表读取数据</li>";
    echo "<li><strong>结果</strong>：两个系统使用不同的数据表，导致数据不同步</li>";
    echo "</ul>";
    
    // 判断是否需要修复
    $need_fix = count($only_in_bankinfo) > 0 || $inconsistent_count > 0;
    
    if ($need_fix) {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4 style='color: #856404;'>⚠️ 需要修复</h4>";
        echo "<p>检测到数据不同步问题，建议立即修复。</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4 style='color: #155724;'>✅ 数据同步正常</h4>";
        echo "<p>前端和后端的钱包地址数据已经同步。</p>";
        echo "</div>";
    }
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ 检查过程中出现错误</h3>";
    echo "<p><strong>错误信息:</strong> " . $e->getMessage() . "</p>";
    echo "<p>请检查：</p>";
    echo "<ul>";
    echo "<li>数据库连接是否正常</li>";
    echo "<li>xy_bankinfo 和 xy_user_wallet 表是否存在</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr style='margin: 30px 0;'>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>🔧 修复工具</h4>";
echo "<p>";
echo "<a href='fix_wallet_sync.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px; font-weight: bold;'>🚀 修复钱包地址同步问题</a>";
echo "<a href='../admin.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>返回后台管理</a>";
echo "</p>";
echo "</div>";

echo "<div style='margin-top: 20px; padding: 10px; background: #f1f1f1; border-radius: 4px; font-size: 12px; color: #666;'>";
echo "<p><strong>使用说明：</strong></p>";
echo "<ol style='margin: 5px 0; padding-left: 20px;'>";
echo "<li>定期运行此检查工具，确保数据同步正常</li>";
echo "<li>如果发现数据不同步，立即使用修复工具</li>";
echo "<li>修复完成后，重新运行此检查工具验证结果</li>";
echo "</ol>";
echo "</div>";
?> 