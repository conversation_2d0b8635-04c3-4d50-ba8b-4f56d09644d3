<?php

echo "<h1>清理临时文件</h1>";

// 要删除的临时文件列表
$temp_files = [
    'diagnose_vip_menu.php',
    'init_vip_table.php', 
    'enable_vip_menu.php',
    'complete_vip_fix.php',
    'fix_vip_menu_to_main.php',
    'fix_vip_core_issue.php',
    'final_vip_fix.php',
    'create_vip_data.php',
    'check_level_table.php',
    'test_task_switch.php',
    'test_vip_display.php'
];

$deleted = 0;
$not_found = 0;

foreach($temp_files as $file) {
    $filepath = __DIR__ . '/' . $file;
    if(file_exists($filepath)) {
        if(unlink($filepath)) {
            echo "<p style='color:green'>✅ 已删除: {$file}</p>";
            $deleted++;
        } else {
            echo "<p style='color:red'>❌ 删除失败: {$file}</p>";
        }
    } else {
        echo "<p style='color:gray'>⚪ 文件不存在: {$file}</p>";
        $not_found++;
    }
}

echo "<hr>";
echo "<h2>清理完成</h2>";
echo "<p>已删除文件: {$deleted} 个</p>";
echo "<p>未找到文件: {$not_found} 个</p>";

if($deleted > 0) {
    echo "<p style='color:green'>🎉 临时文件清理完成！VIP等级任务开关功能已集成到会员等级管理页面。</p>";
}

// 删除自己（这个清理脚本）
echo "<p style='color:orange'>⚠️ 5秒后将删除此清理脚本本身...</p>";
echo "<script>
setTimeout(function(){
    fetch(window.location.href + '?delete_self=1')
    .then(() => {
        document.body.innerHTML = '<h1>清理完成</h1><p style=\"color:green\">所有临时文件已清理完毕！</p>';
    });
}, 5000);
</script>";

// 处理删除自己的请求
if(isset($_GET['delete_self'])) {
    unlink(__FILE__);
    echo "清理脚本已删除";
    exit;
}
?> 
<?php

echo "<h1>清理临时文件</h1>";

// 要删除的临时文件列表
$temp_files = [
    'diagnose_vip_menu.php',
    'init_vip_table.php', 
    'enable_vip_menu.php',
    'complete_vip_fix.php',
    'fix_vip_menu_to_main.php',
    'fix_vip_core_issue.php',
    'final_vip_fix.php',
    'create_vip_data.php',
    'check_level_table.php'
];

$deleted = 0;
$not_found = 0;

foreach($temp_files as $file) {
    $filepath = __DIR__ . '/' . $file;
    if(file_exists($filepath)) {
        if(unlink($filepath)) {
            echo "<p style='color:green'>✅ 已删除: {$file}</p>";
            $deleted++;
        } else {
            echo "<p style='color:red'>❌ 删除失败: {$file}</p>";
        }
    } else {
        echo "<p style='color:gray'>⚪ 文件不存在: {$file}</p>";
        $not_found++;
    }
}

echo "<hr>";
echo "<h2>清理完成</h2>";
echo "<p>已删除文件: {$deleted} 个</p>";
echo "<p>未找到文件: {$not_found} 个</p>";

if($deleted > 0) {
    echo "<p style='color:green'>🎉 临时文件清理完成！VIP等级任务开关功能已集成到会员等级管理页面。</p>";
}

// 删除自己（这个清理脚本）
echo "<p style='color:orange'>⚠️ 5秒后将删除此清理脚本本身...</p>";
echo "<script>
setTimeout(function(){
    fetch(window.location.href + '?delete_self=1')
    .then(() => {
        document.body.innerHTML = '<h1>清理完成</h1><p style=\"color:green\">所有临时文件已清理完毕！</p>';
    });
}, 5000);
</script>";

// 处理删除自己的请求
if(isset($_GET['delete_self'])) {
    unlink(__FILE__);
    echo "清理脚本已删除";
    exit;
}
?> 