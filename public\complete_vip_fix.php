<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>VIP等级管理功能完整修复</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $steps_completed = 0;
    $total_steps = 5;
    
    echo "<h2>步骤 1/{$total_steps}: 检查并创建数据库表</h2>";
    
    // 检查表是否存在
    $stmt = $db->query("SHOW TABLES LIKE 'xy_vip_level_switch'");
    $table_exists = $stmt->fetch();
    
    if(!$table_exists) {
        echo "<p>正在创建数据库表...</p>";
        
        $create_table_sql = "
        CREATE TABLE `xy_vip_level_switch` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
          `vip_level` int(11) NOT NULL COMMENT 'VIP等级 1-6',
          `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开启 1=开启 0=关闭',
          `switch_name` varchar(50) NOT NULL COMMENT '开关名称',
          `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
          `created_time` int(11) NOT NULL COMMENT '创建时间',
          `updated_time` int(11) NOT NULL COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `vip_level` (`vip_level`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP等级开关配置表'";
        
        $db->exec($create_table_sql);
        echo "<p style='color:green'>✅ 数据库表创建成功</p>";
    } else {
        echo "<p style='color:green'>✅ 数据库表已存在</p>";
    }
    
    // 插入默认数据
    $stmt = $db->query("SELECT COUNT(*) as count FROM xy_vip_level_switch");
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if($count['count'] == 0) {
        echo "<p>正在插入默认数据...</p>";
        
        $current_time = time();
        $insert_sql = "
        INSERT INTO `xy_vip_level_switch` (`vip_level`, `is_enabled`, `switch_name`, `description`, `created_time`, `updated_time`) VALUES
        (1, 1, 'VIP1任务开关', 'VIP1等级任务接单开关', {$current_time}, {$current_time}),
        (2, 1, 'VIP2任务开关', 'VIP2等级任务接单开关', {$current_time}, {$current_time}),
        (3, 1, 'VIP3任务开关', 'VIP3等级任务接单开关', {$current_time}, {$current_time}),
        (4, 1, 'VIP4任务开关', 'VIP4等级任务接单开关', {$current_time}, {$current_time}),
        (5, 1, 'VIP5任务开关', 'VIP5等级任务接单开关', {$current_time}, {$current_time}),
        (6, 1, 'VIP6任务开关', 'VIP6等级任务接单开关', {$current_time}, {$current_time})";
        
        $db->exec($insert_sql);
        echo "<p style='color:green'>✅ 默认数据插入成功</p>";
    } else {
        echo "<p style='color:green'>✅ 数据已存在 ({$count['count']} 条记录)</p>";
    }
    
    $steps_completed++;
    
    echo "<h2>步骤 2/{$total_steps}: 修复菜单配置</h2>";
    
    // 查找VIP等级管理菜单
    $stmt = $db->query("SELECT id, pid, title, status FROM system_menu WHERE title LIKE '%VIP等级%'");
    $vip_menu = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if(!$vip_menu) {
        echo "<p>VIP等级管理菜单不存在，正在创建...</p>";
        
        // 获取排序值
        $stmt = $db->query("SELECT MAX(sort) as max_sort FROM system_menu WHERE pid = 0");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $new_sort = ($result['max_sort'] ?? 0) + 10;
        
        // 创建主菜单
        $sql = "INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
                VALUES (0, 'VIP等级管理', 'admin/VipLevelSwitch/index', 'admin/VipLevelSwitch/index', '', 'fa fa-vip', :sort, 1)";
        $stmt = $db->prepare($sql);
        $stmt->execute(['sort' => $new_sort]);
        $vip_menu_id = $db->lastInsertId();
        
        echo "<p style='color:green'>✅ VIP等级管理主菜单创建成功 (ID: {$vip_menu_id})</p>";
        
        // 添加子菜单
        $submenus = [
            ['title' => '等级开关管理', 'node' => 'admin/VipLevelSwitch/index', 'url' => 'admin/VipLevelSwitch/index'],
            ['title' => '切换开关', 'node' => 'admin/VipLevelSwitch/toggleSwitch', 'url' => 'admin/VipLevelSwitch/toggleSwitch'],
            ['title' => '批量操作', 'node' => 'admin/VipLevelSwitch/batchToggle', 'url' => 'admin/VipLevelSwitch/batchToggle'],
            ['title' => '重置开关', 'node' => 'admin/VipLevelSwitch/resetAll', 'url' => 'admin/VipLevelSwitch/resetAll']
        ];
        
        foreach($submenus as $index => $submenu) {
            $sql_sub = "INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
                        VALUES (:pid, :title, :node, :url, '', '', :sort, 1)";
            $stmt_sub = $db->prepare($sql_sub);
            $stmt_sub->execute([
                'pid' => $vip_menu_id,
                'title' => $submenu['title'],
                'node' => $submenu['node'],
                'url' => $submenu['url'],
                'sort' => ($index + 1) * 10
            ]);
        }
        
        echo "<p style='color:green'>✅ 子菜单创建成功</p>";
    } else {
        echo "<p>VIP等级管理菜单已存在 (ID: {$vip_menu['id']})</p>";
        
        // 确保菜单是主菜单且启用
        if($vip_menu['pid'] != 0 || $vip_menu['status'] != 1) {
            $stmt = $db->query("SELECT MAX(sort) as max_sort FROM system_menu WHERE pid = 0");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $new_sort = ($result['max_sort'] ?? 0) + 10;
            
            $sql = "UPDATE system_menu SET pid = 0, status = 1, sort = :sort, icon = 'fa fa-vip' WHERE id = :id";
            $stmt = $db->prepare($sql);
            $stmt->execute(['sort' => $new_sort, 'id' => $vip_menu['id']]);
            
            echo "<p style='color:green'>✅ 菜单已修复为主菜单并启用</p>";
        } else {
            echo "<p style='color:green'>✅ 菜单配置正确</p>";
        }
    }
    
    $steps_completed++;
    
    echo "<h2>步骤 3/{$total_steps}: 检查控制器文件</h2>";
    
    $controller_file = '../application/admin/controller/VipLevelSwitch.php';
    if(file_exists($controller_file)) {
        echo "<p style='color:green'>✅ 控制器文件存在</p>";
    } else {
        echo "<p style='color:red'>❌ 控制器文件不存在，需要手动创建</p>";
    }
    
    $steps_completed++;
    
    echo "<h2>步骤 4/{$total_steps}: 检查视图文件</h2>";
    
    $view_file = '../application/admin/view/vip_level_switch/index.html';
    if(file_exists($view_file)) {
        echo "<p style='color:green'>✅ 视图文件存在</p>";
    } else {
        echo "<p style='color:red'>❌ 视图文件不存在，需要手动创建</p>";
    }
    
    $steps_completed++;
    
    echo "<h2>步骤 5/{$total_steps}: 清除缓存</h2>";
    
    // 清除数据库缓存
    try {
        $db->exec("TRUNCATE TABLE system_cache");
        echo "<p style='color:green'>✅ 已清除数据库缓存</p>";
    } catch(Exception $e) {
        echo "<p style='color:orange'>⚠️ 数据库缓存清除失败（可能不存在缓存表）</p>";
    }
    
    // 清除文件缓存
    $cache_dirs = [
        '../runtime/cache',
        '../runtime/temp',
        '../application/runtime/cache',
        '../application/runtime/temp'
    ];
    
    $cleared_dirs = 0;
    foreach($cache_dirs as $dir) {
        if(is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach($files as $file) {
                if(is_file($file)) {
                    unlink($file);
                }
            }
            $cleared_dirs++;
        }
    }
    
    echo "<p style='color:green'>✅ 已清除 {$cleared_dirs} 个缓存目录</p>";
    
    $steps_completed++;
    
    echo "<h2 style='color:green'>🎉 修复完成！</h2>";
    echo "<p><strong>完成进度: {$steps_completed}/{$total_steps}</strong></p>";
    
    echo "<h3>修复总结：</h3>";
    echo "<ul>";
    echo "<li>✅ 数据库表已创建并初始化</li>";
    echo "<li>✅ VIP等级管理菜单已设置为主菜单</li>";
    echo "<li>✅ 菜单状态已启用</li>";
    echo "<li>✅ 系统缓存已清除</li>";
    echo "</ul>";
    
    echo "<h3>接下来的步骤：</h3>";
    echo "<ol>";
    echo "<li><strong>清除浏览器缓存</strong> - 按 Ctrl+F5 强制刷新</li>";
    echo "<li><strong>重新登录后台</strong> - <a href='/admin.html' target='_blank'>点击这里登录</a></li>";
    echo "<li><strong>测试VIP等级管理功能</strong></li>";
    echo "</ol>";
    
    echo "<h3>如果仍有问题：</h3>";
    echo "<ul>";
    echo "<li>检查浏览器控制台是否有JavaScript错误</li>";
    echo "<li>确认管理员账户有足够权限</li>";
    echo "<li>检查服务器错误日志</li>";
    echo "</ul>";
    
    echo "<p><a href='diagnose_vip_menu.php'>运行诊断脚本</a> | <a href='/admin.html' target='_blank'>前往后台管理</a></p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 操作失败: " . $e->getMessage() . "</p>";
}
?> 