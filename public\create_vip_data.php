<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>创建VIP等级开关数据</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>步骤1: 删除旧表（如果存在）</h2>";
    $db->exec("DROP TABLE IF EXISTS `xy_vip_level_switch`");
    echo "<p style='color:green'>✅ 已删除旧表</p>";
    
    echo "<h2>步骤2: 创建新表</h2>";
    $create_table_sql = "
    CREATE TABLE `xy_vip_level_switch` (
      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      `vip_level` int(11) NOT NULL COMMENT 'VIP等级 1-6',
      `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开启 1=开启 0=关闭',
      `switch_name` varchar(50) NOT NULL COMMENT '开关名称',
      `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
      `created_time` int(11) NOT NULL COMMENT '创建时间',
      `updated_time` int(11) NOT NULL COMMENT '更新时间',
      PRIMARY KEY (`id`),
      UNIQUE KEY `vip_level` (`vip_level`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP等级开关配置表'";
    
    $db->exec($create_table_sql);
    echo "<p style='color:green'>✅ 数据库表创建成功</p>";
    
    echo "<h2>步骤3: 插入默认数据</h2>";
    $current_time = time();
    $insert_sql = "
    INSERT INTO `xy_vip_level_switch` (`vip_level`, `is_enabled`, `switch_name`, `description`, `created_time`, `updated_time`) VALUES
    (1, 1, 'VIP1任务开关', 'VIP1等级任务接单开关', {$current_time}, {$current_time}),
    (2, 1, 'VIP2任务开关', 'VIP2等级任务接单开关', {$current_time}, {$current_time}),
    (3, 1, 'VIP3任务开关', 'VIP3等级任务接单开关', {$current_time}, {$current_time}),
    (4, 1, 'VIP4任务开关', 'VIP4等级任务接单开关', {$current_time}, {$current_time}),
    (5, 1, 'VIP5任务开关', 'VIP5等级任务接单开关', {$current_time}, {$current_time}),
    (6, 1, 'VIP6任务开关', 'VIP6等级任务接单开关', {$current_time}, {$current_time})";
    
    $db->exec($insert_sql);
    echo "<p style='color:green'>✅ 默认数据插入成功</p>";
    
    echo "<h2>步骤4: 验证数据</h2>";
    $stmt = $db->query("SELECT * FROM xy_vip_level_switch ORDER BY vip_level");
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr><th>ID</th><th>VIP等级</th><th>开关名称</th><th>描述</th><th>状态</th><th>创建时间</th></tr>";
    foreach($data as $row) {
        $status = $row['is_enabled'] ? '开启' : '关闭';
        $time = date('Y-m-d H:i:s', $row['created_time']);
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>VIP{$row['vip_level']}</td>";
        echo "<td>{$row['switch_name']}</td>";
        echo "<td>{$row['description']}</td>";
        echo "<td>{$status}</td>";
        echo "<td>{$time}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2 style='color:green'>🎉 数据创建完成！</h2>";
    echo "<p><a href='/admin/vip_level_switch/index' target='_blank' style='background:blue;color:white;padding:10px;text-decoration:none;'>现在测试VIP等级管理</a></p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 操作失败: " . $e->getMessage() . "</p>";
}
?> 