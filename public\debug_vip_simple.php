<?php
// 简单的数据库查询，不依赖框架
$host = '127.0.0.1';
$dbname = 'danss';
$username = 'danss';
$password = 'MTbhcsYaFBrnMiX6';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>VIP等级调试 - 直接查询数据库</h1>";
    
    // 从cookie获取用户ID
    $user_id = isset($_COOKIE['user_id']) ? $_COOKIE['user_id'] : null;
    
    if (!$user_id) {
        echo "<p style='color:red;'>Cookie中没有user_id，请先登录</p>";
        exit;
    }
    
    echo "<h2>用户ID: {$user_id}</h2>";
    
    // 查询用户信息
    $stmt = $pdo->prepare("SELECT id, username, tel, level FROM xy_users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<p style='color:red;'>找不到用户信息</p>";
        exit;
    }
    
    echo "<h3>用户信息:</h3>";
    echo "<table border='1'>";
    echo "<tr><td>ID</td><td>{$user['id']}</td></tr>";
    echo "<tr><td>用户名</td><td>{$user['username']}</td></tr>";
    echo "<tr><td>电话</td><td>{$user['tel']}</td></tr>";
    echo "<tr><td style='color:red; font-weight:bold;'>Level字段</td><td style='color:red; font-weight:bold; font-size:18px;'>{$user['level']}</td></tr>";
    echo "</table>";
    
    $user_level = $user['level'];
    
    // 查询xy_level表的所有数据
    echo "<h3>xy_level表所有数据:</h3>";
    $stmt = $pdo->query("SELECT * FROM xy_level ORDER BY id ASC");
    $levels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>ID</th><th>Level</th><th>Name</th><th>Order_num</th><th>Bili</th><th>Num</th></tr>";
    
    $found_match = false;
    foreach ($levels as $level) {
        $highlight = '';
        if ($level['id'] == $user_level) {
            $highlight = 'background-color:yellow; font-weight:bold;';
            $found_match = true;
            echo "<tr style='{$highlight}'>";
            echo "<td>{$level['id']} ← 匹配ID</td>";
        } elseif ($level['level'] == $user_level) {
            $highlight = 'background-color:lightblue; font-weight:bold;';
            $found_match = true;
            echo "<tr style='{$highlight}'>";
            echo "<td>{$level['id']}</td>";
        } else {
            echo "<tr>";
            echo "<td>{$level['id']}</td>";
        }
        
        echo "<td>{$level['level']}</td>";
        echo "<td>{$level['name']}</td>";
        echo "<td>{$level['order_num']}</td>";
        echo "<td>{$level['bili']}</td>";
        echo "<td>{$level['num']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>分析结果:</h3>";
    if ($found_match) {
        // 找到匹配的等级
        foreach ($levels as $level) {
            if ($level['id'] == $user_level || $level['level'] == $user_level) {
                echo "<p style='color:green; font-size:18px; font-weight:bold;'>✓ 应该显示: {$level['name']}</p>";
                echo "<p>匹配方式: " . ($level['id'] == $user_level ? "通过ID匹配" : "通过Level字段匹配") . "</p>";
                break;
            }
        }
    } else {
        echo "<p style='color:red; font-size:18px; font-weight:bold;'>✗ 没有找到匹配的等级！</p>";
        echo "<p>用户level值 '{$user_level}' 在xy_level表中没有对应记录</p>";
        echo "<p>建议检查数据一致性</p>";
    }
    
    // 检查控制器应该如何处理
    echo "<h3>控制器应该如何处理:</h3>";
    if ($found_match) {
        foreach ($levels as $level) {
            if ($level['id'] == $user_level || $level['level'] == $user_level) {
                echo "<p>控制器应该设置:</p>";
                echo "<code>\$level_name = '{$level['name']}';</code><br>";
                echo "<code>\$order_num = {$level['order_num']};</code>";
                break;
            }
        }
    } else {
        echo "<p>控制器应该设置:</p>";
        echo "<code>\$level_name = 'VIP{$user_level}';</code><br>";
        echo "<code>\$order_num = 1;</code>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red;'>数据库错误: " . $e->getMessage() . "</p>";
}
?>
