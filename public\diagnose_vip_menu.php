<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>VIP等级管理功能诊断</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1. 检查菜单配置</h2>";
    
    // 检查VIP等级管理菜单
    $stmt = $db->query("SELECT id, pid, title, node, url, icon, sort, status FROM system_menu WHERE title LIKE '%VIP等级%' OR title LIKE '%VIP%'");
    $vip_menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if($vip_menus) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>父ID</th><th>标题</th><th>节点</th><th>URL</th><th>图标</th><th>排序</th><th>状态</th></tr>";
        foreach($vip_menus as $menu) {
            $status_text = $menu['status'] == 1 ? '启用' : '禁用';
            $menu_type = $menu['pid'] == 0 ? '主菜单' : '子菜单';
            echo "<tr>";
            echo "<td>{$menu['id']}</td>";
            echo "<td>{$menu['pid']} ({$menu_type})</td>";
            echo "<td>{$menu['title']}</td>";
            echo "<td>{$menu['node']}</td>";
            echo "<td>{$menu['url']}</td>";
            echo "<td>{$menu['icon']}</td>";
            echo "<td>{$menu['sort']}</td>";
            echo "<td>{$status_text}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color:red'>❌ 未找到VIP相关菜单</p>";
    }
    
    echo "<h2>2. 检查数据库表</h2>";
    
    // 检查xy_vip_level_switch表是否存在
    $stmt = $db->query("SHOW TABLES LIKE 'xy_vip_level_switch'");
    $table_exists = $stmt->fetch();
    
    if($table_exists) {
        echo "<p style='color:green'>✅ xy_vip_level_switch 表存在</p>";
        
        // 检查表数据
        $stmt = $db->query("SELECT COUNT(*) as count FROM xy_vip_level_switch");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>表中有 {$count['count']} 条记录</p>";
        
        if($count['count'] > 0) {
            $stmt = $db->query("SELECT * FROM xy_vip_level_switch ORDER BY vip_level");
            $switches = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>VIP等级</th><th>开关名称</th><th>状态</th><th>描述</th></tr>";
            foreach($switches as $switch) {
                $status_text = $switch['is_enabled'] == 1 ? '开启' : '关闭';
                echo "<tr>";
                echo "<td>VIP{$switch['vip_level']}</td>";
                echo "<td>{$switch['switch_name']}</td>";
                echo "<td>{$status_text}</td>";
                echo "<td>{$switch['description']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color:red'>❌ xy_vip_level_switch 表不存在</p>";
        echo "<p><strong>需要执行数据库初始化脚本</strong></p>";
    }
    
    echo "<h2>3. 检查控制器文件</h2>";
    
    $controller_file = '../application/admin/controller/VipLevelSwitch.php';
    if(file_exists($controller_file)) {
        echo "<p style='color:green'>✅ 控制器文件存在: {$controller_file}</p>";
    } else {
        echo "<p style='color:red'>❌ 控制器文件不存在: {$controller_file}</p>";
    }
    
    echo "<h2>4. 检查视图文件</h2>";
    
    $view_file = '../application/admin/view/vip_level_switch/index.html';
    if(file_exists($view_file)) {
        echo "<p style='color:green'>✅ 视图文件存在: {$view_file}</p>";
    } else {
        echo "<p style='color:red'>❌ 视图文件不存在: {$view_file}</p>";
    }
    
    echo "<h2>5. 检查URL路由</h2>";
    
    // 检查菜单URL配置
    $stmt = $db->query("SELECT url FROM system_menu WHERE title LIKE '%VIP等级%'");
    $menu_url = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if($menu_url) {
        echo "<p>菜单URL配置: {$menu_url['url']}</p>";
        
        // 分析URL
        $url_parts = explode('/', $menu_url['url']);
        if(count($url_parts) >= 3) {
            echo "<p>模块: {$url_parts[0]}</p>";
            echo "<p>控制器: {$url_parts[1]}</p>";
            echo "<p>方法: {$url_parts[2]}</p>";
        }
    }
    
    echo "<h2>6. 问题诊断结果</h2>";
    
    $issues = [];
    
    // 检查菜单状态
    if($vip_menus) {
        foreach($vip_menus as $menu) {
            if($menu['status'] != 1) {
                $issues[] = "菜单 '{$menu['title']}' 状态为禁用";
            }
        }
    } else {
        $issues[] = "VIP等级管理菜单不存在";
    }
    
    // 检查数据库表
    if(!$table_exists) {
        $issues[] = "数据库表 xy_vip_level_switch 不存在";
    }
    
    // 检查文件
    if(!file_exists($controller_file)) {
        $issues[] = "控制器文件不存在";
    }
    
    if(!file_exists($view_file)) {
        $issues[] = "视图文件不存在";
    }
    
    if(empty($issues)) {
        echo "<p style='color:green'>✅ 未发现明显问题</p>";
        echo "<p>可能的原因：</p>";
        echo "<ul>";
        echo "<li>浏览器缓存问题 - 请清除浏览器缓存</li>";
        echo "<li>权限问题 - 检查管理员权限</li>";
        echo "<li>JavaScript错误 - 检查浏览器控制台</li>";
        echo "<li>服务器配置问题 - 检查URL重写规则</li>";
        echo "</ul>";
    } else {
        echo "<p style='color:red'>❌ 发现以下问题：</p>";
        echo "<ul>";
        foreach($issues as $issue) {
            echo "<li>{$issue}</li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>7. 修复建议</h2>";
    
    if(!$table_exists) {
        echo "<p><strong>1. 创建数据库表：</strong></p>";
        echo "<p><a href='init_vip_table.php' target='_blank'>点击这里初始化数据库表</a></p>";
    }
    
    if($vip_menus) {
        foreach($vip_menus as $menu) {
            if($menu['status'] != 1) {
                echo "<p><strong>2. 启用菜单：</strong></p>";
                echo "<p><a href='enable_vip_menu.php' target='_blank'>点击这里启用VIP菜单</a></p>";
                break;
            }
        }
    }
    
    echo "<p><strong>3. 一键完整修复：</strong></p>";
    echo "<p><a href='complete_vip_fix.php' target='_blank' style='background:green;color:white;padding:10px;text-decoration:none;'>点击这里执行完整修复</a></p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>数据库连接错误: " . $e->getMessage() . "</p>";
}
?> 