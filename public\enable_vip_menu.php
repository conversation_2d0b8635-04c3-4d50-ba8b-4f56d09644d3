<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>启用VIP等级管理菜单</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>正在检查VIP等级管理菜单状态...</p>";
    
    // 查找VIP等级管理菜单
    $stmt = $db->query("SELECT id, title, status FROM system_menu WHERE title LIKE '%VIP等级%'");
    $vip_menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if(empty($vip_menus)) {
        echo "<p style='color:red'>❌ 未找到VIP等级管理菜单</p>";
        echo "<p>请先运行菜单修复脚本创建菜单</p>";
        echo "<p><a href='complete_vip_fix.php'>点击这里执行完整修复</a></p>";
        exit;
    }
    
    $updated_count = 0;
    
    foreach($vip_menus as $menu) {
        echo "<p>检查菜单: {$menu['title']} (ID: {$menu['id']})</p>";
        
        if($menu['status'] != 1) {
            echo "<p style='color:orange'>菜单状态为禁用，正在启用...</p>";
            
            $sql = "UPDATE system_menu SET status = 1 WHERE id = :id";
            $stmt = $db->prepare($sql);
            $stmt->execute(['id' => $menu['id']]);
            
            echo "<p style='color:green'>✅ 菜单 '{$menu['title']}' 已启用</p>";
            $updated_count++;
        } else {
            echo "<p style='color:green'>✅ 菜单 '{$menu['title']}' 已经是启用状态</p>";
        }
    }
    
    // 同时启用所有VIP相关的子菜单
    echo "<p>正在检查VIP相关的子菜单...</p>";
    
    $vip_main_menu = $db->query("SELECT id FROM system_menu WHERE title = 'VIP等级管理' LIMIT 1")->fetch(PDO::FETCH_ASSOC);
    
    if($vip_main_menu) {
        $sql = "UPDATE system_menu SET status = 1 WHERE pid = :pid";
        $stmt = $db->prepare($sql);
        $stmt->execute(['pid' => $vip_main_menu['id']]);
        
        $affected = $stmt->rowCount();
        if($affected > 0) {
            echo "<p style='color:green'>✅ 已启用 {$affected} 个VIP子菜单</p>";
        }
    }
    
    // 清除缓存
    echo "<p>正在清除系统缓存...</p>";
    
    try {
        $db->exec("TRUNCATE TABLE system_cache");
        echo "<p style='color:green'>✅ 已清除数据库缓存</p>";
    } catch(Exception $e) {
        echo "<p style='color:orange'>⚠️ 清除数据库缓存失败（可能不存在缓存表）</p>";
    }
    
    // 清除文件缓存
    $cache_dirs = [
        '../runtime/cache',
        '../runtime/temp',
        '../application/runtime/cache',
        '../application/runtime/temp'
    ];
    
    foreach($cache_dirs as $dir) {
        if(is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach($files as $file) {
                if(is_file($file)) {
                    unlink($file);
                }
            }
            echo "<p>已清除缓存目录: {$dir}</p>";
        }
    }
    
    echo "<h2 style='color:green'>✅ 操作完成！</h2>";
    
    if($updated_count > 0) {
        echo "<p>已成功启用 {$updated_count} 个VIP菜单</p>";
    } else {
        echo "<p>所有VIP菜单都已经是启用状态</p>";
    }
    
    echo "<p><strong>请执行以下步骤：</strong></p>";
    echo "<ol>";
    echo "<li>清除浏览器缓存</li>";
    echo "<li>重新登录后台管理系统</li>";
    echo "<li>检查VIP等级管理菜单是否可以正常访问</li>";
    echo "</ol>";
    
    echo "<p><a href='diagnose_vip_menu.php'>返回诊断页面</a></p>";
    echo "<p><a href='/admin.html' target='_blank'>前往后台管理</a></p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
}
?> 