<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>VIP等级管理最终修复</h1>";
echo "<p style='color:blue'>问题分析：URL路径使用下划线命名(/admin/vip_level_switch/)，但控制器使用驼峰命名(VipLevelSwitch.php)</p>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>步骤1: 检查并创建数据库表</h2>";
    
    // 检查xy_vip_level_switch表
    $stmt = $db->query("SHOW TABLES LIKE 'xy_vip_level_switch'");
    $table_exists = $stmt->fetch();
    
    if(!$table_exists) {
        echo "<p style='color:orange'>⚠️ 数据库表不存在，正在创建...</p>";
        
        $create_table_sql = "
        CREATE TABLE `xy_vip_level_switch` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
          `vip_level` int(11) NOT NULL COMMENT 'VIP等级 1-6',
          `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开启 1=开启 0=关闭',
          `switch_name` varchar(50) NOT NULL COMMENT '开关名称',
          `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
          `created_time` int(11) NOT NULL COMMENT '创建时间',
          `updated_time` int(11) NOT NULL COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `vip_level` (`vip_level`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP等级开关配置表'";
        
        $db->exec($create_table_sql);
        
        // 插入默认数据
        $current_time = time();
        $insert_sql = "
        INSERT INTO `xy_vip_level_switch` (`vip_level`, `is_enabled`, `switch_name`, `description`, `created_time`, `updated_time`) VALUES
        (1, 1, 'VIP1任务开关', 'VIP1等级任务接单开关', {$current_time}, {$current_time}),
        (2, 1, 'VIP2任务开关', 'VIP2等级任务接单开关', {$current_time}, {$current_time}),
        (3, 1, 'VIP3任务开关', 'VIP3等级任务接单开关', {$current_time}, {$current_time}),
        (4, 1, 'VIP4任务开关', 'VIP4等级任务接单开关', {$current_time}, {$current_time}),
        (5, 1, 'VIP5任务开关', 'VIP5等级任务接单开关', {$current_time}, {$current_time}),
        (6, 1, 'VIP6任务开关', 'VIP6等级任务接单开关', {$current_time}, {$current_time})";
        
        $db->exec($insert_sql);
        echo "<p style='color:green'>✅ 数据库表创建成功并插入默认数据</p>";
    } else {
        echo "<p style='color:green'>✅ 数据库表已存在</p>";
        
        // 检查数据
        $stmt = $db->query("SELECT COUNT(*) as count FROM xy_vip_level_switch");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        if($count['count'] == 0) {
            echo "<p style='color:orange'>⚠️ 表中无数据，正在插入默认数据...</p>";
            $current_time = time();
            $insert_sql = "
            INSERT INTO `xy_vip_level_switch` (`vip_level`, `is_enabled`, `switch_name`, `description`, `created_time`, `updated_time`) VALUES
            (1, 1, 'VIP1任务开关', 'VIP1等级任务接单开关', {$current_time}, {$current_time}),
            (2, 1, 'VIP2任务开关', 'VIP2等级任务接单开关', {$current_time}, {$current_time}),
            (3, 1, 'VIP3任务开关', 'VIP3等级任务接单开关', {$current_time}, {$current_time}),
            (4, 1, 'VIP4任务开关', 'VIP4等级任务接单开关', {$current_time}, {$current_time}),
            (5, 1, 'VIP5任务开关', 'VIP5等级任务接单开关', {$current_time}, {$current_time}),
            (6, 1, 'VIP6任务开关', 'VIP6等级任务接单开关', {$current_time}, {$current_time})";
            $db->exec($insert_sql);
            echo "<p style='color:green'>✅ 默认数据插入成功</p>";
        } else {
            echo "<p style='color:green'>✅ 表中已有 {$count['count']} 条数据</p>";
        }
    }
    
    echo "<h2>步骤2: 检查文件结构</h2>";
    
    // 检查控制器文件
    $controller_file = '../application/admin/controller/VipLevelSwitch.php';
    if(file_exists($controller_file)) {
        echo "<p style='color:green'>✅ 控制器文件存在</p>";
    } else {
        echo "<p style='color:red'>❌ 控制器文件不存在</p>";
    }
    
    // 检查视图文件
    $view_file = '../application/admin/view/vip_level_switch/index.html';
    if(file_exists($view_file)) {
        echo "<p style='color:green'>✅ 视图文件存在</p>";
    } else {
        echo "<p style='color:red'>❌ 视图文件不存在</p>";
    }
    
    echo "<h2>步骤3: 清除所有缓存</h2>";
    
    // 清除数据库缓存
    try {
        $db->exec("DELETE FROM system_cache WHERE 1=1");
        echo "<p style='color:green'>✅ 已清除数据库缓存</p>";
    } catch(Exception $e) {
        echo "<p style='color:orange'>⚠️ 数据库缓存清除失败: " . $e->getMessage() . "</p>";
    }
    
    // 清除文件缓存
    $cache_dirs = [
        '../runtime/cache',
        '../runtime/temp',
        '../application/runtime/cache',
        '../application/runtime/temp'
    ];
    
    foreach($cache_dirs as $dir) {
        if(is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach($files as $file) {
                if(is_file($file)) {
                    unlink($file);
                }
            }
            echo "<p>已清除缓存目录: {$dir}</p>";
        }
    }
    
    echo "<h2 style='color:green'>🎉 修复完成！</h2>";
    
    echo "<h3>解决方案说明:</h3>";
    echo "<p><strong>问题根源:</strong> URL路径使用下划线命名 <code>/admin/vip_level_switch/</code>，但控制器文件使用驼峰命名 <code>VipLevelSwitch.php</code></p>";
    echo "<p><strong>解决方法:</strong> 已在 <code>route/route.php</code> 文件中添加路由映射，将下划线URL映射到驼峰控制器</p>";
    
    echo "<h3>添加的路由映射:</h3>";
    echo "<pre style='background:#f5f5f5;padding:10px;'>";
    echo "Route::rule('admin/vip_level_switch/index', 'admin/VipLevelSwitch/index');\n";
    echo "Route::rule('admin/vip_level_switch/toggleSwitch', 'admin/VipLevelSwitch/toggleSwitch');\n";
    echo "Route::rule('admin/vip_level_switch/toggle_switch', 'admin/VipLevelSwitch/toggleSwitch');\n";
    echo "// ... 其他方法映射";
    echo "</pre>";
    
    echo "<h3>现在可以正常访问:</h3>";
    echo "<ul>";
    echo "<li>✅ 菜单点击应该可以正常工作</li>";
    echo "<li>✅ VIP等级开关管理功能完整</li>";
    echo "<li>✅ 支持单个切换、批量操作、全部重置</li>";
    echo "</ul>";
    
    echo "<p><a href='/admin.html' target='_blank' style='background:green;color:white;padding:10px;text-decoration:none;'>返回后台管理</a></p>";
    echo "<p><a href='/admin/vip_level_switch/index' target='_blank' style='background:blue;color:white;padding:10px;text-decoration:none;'>直接测试VIP等级管理</a></p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 操作失败: " . $e->getMessage() . "</p>";
}
?> 