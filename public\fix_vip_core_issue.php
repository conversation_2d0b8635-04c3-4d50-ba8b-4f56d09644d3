<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>VIP等级管理核心问题修复</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>步骤1: 检查当前菜单配置</h2>";
    
    // 查找VIP等级管理菜单
    $stmt = $db->query("SELECT id, pid, title, node, url, icon, sort, status FROM system_menu WHERE title LIKE '%VIP等级%'");
    $vip_menu = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if($vip_menu) {
        echo "<p>找到VIP等级管理菜单:</p>";
        echo "<ul>";
        echo "<li>ID: {$vip_menu['id']}</li>";
        echo "<li>标题: {$vip_menu['title']}</li>";
        echo "<li>URL: {$vip_menu['url']}</li>";
        echo "<li>节点: {$vip_menu['node']}</li>";
        echo "<li>状态: " . ($vip_menu['status'] == 1 ? '启用' : '禁用') . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color:red'>❌ 未找到VIP等级管理菜单</p>";
    }
    
    echo "<h2>步骤2: 修复菜单URL配置</h2>";
    
    if($vip_menu) {
        // 修复菜单URL - 确保使用正确的URL格式
        $correct_url = 'admin/VipLevelSwitch/index';
        $correct_node = 'admin/VipLevelSwitch/index';
        
        if($vip_menu['url'] !== $correct_url || $vip_menu['node'] !== $correct_node) {
            $sql = "UPDATE system_menu SET 
                    url = :url, 
                    node = :node,
                    status = 1
                    WHERE id = :id";
            $stmt = $db->prepare($sql);
            $stmt->execute([
                'url' => $correct_url,
                'node' => $correct_node,
                'id' => $vip_menu['id']
            ]);
            echo "<p style='color:green'>✅ 已修复菜单URL配置</p>";
            echo "<p>新URL: {$correct_url}</p>";
        } else {
            echo "<p style='color:green'>✅ 菜单URL配置正确</p>";
        }
    }
    
    echo "<h2>步骤3: 检查数据库表</h2>";
    
    // 检查xy_vip_level_switch表
    $stmt = $db->query("SHOW TABLES LIKE 'xy_vip_level_switch'");
    $table_exists = $stmt->fetch();
    
    if(!$table_exists) {
        echo "<p style='color:orange'>⚠️ 数据库表不存在，正在创建...</p>";
        
        $create_table_sql = "
        CREATE TABLE `xy_vip_level_switch` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
          `vip_level` int(11) NOT NULL COMMENT 'VIP等级 1-6',
          `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开启 1=开启 0=关闭',
          `switch_name` varchar(50) NOT NULL COMMENT '开关名称',
          `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
          `created_time` int(11) NOT NULL COMMENT '创建时间',
          `updated_time` int(11) NOT NULL COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `vip_level` (`vip_level`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP等级开关配置表'";
        
        $db->exec($create_table_sql);
        
        // 插入默认数据
        $current_time = time();
        $insert_sql = "
        INSERT INTO `xy_vip_level_switch` (`vip_level`, `is_enabled`, `switch_name`, `description`, `created_time`, `updated_time`) VALUES
        (1, 1, 'VIP1任务开关', 'VIP1等级任务接单开关', {$current_time}, {$current_time}),
        (2, 1, 'VIP2任务开关', 'VIP2等级任务接单开关', {$current_time}, {$current_time}),
        (3, 1, 'VIP3任务开关', 'VIP3等级任务接单开关', {$current_time}, {$current_time}),
        (4, 1, 'VIP4任务开关', 'VIP4等级任务接单开关', {$current_time}, {$current_time}),
        (5, 1, 'VIP5任务开关', 'VIP5等级任务接单开关', {$current_time}, {$current_time}),
        (6, 1, 'VIP6任务开关', 'VIP6等级任务接单开关', {$current_time}, {$current_time})";
        
        $db->exec($insert_sql);
        echo "<p style='color:green'>✅ 数据库表创建成功并插入默认数据</p>";
    } else {
        echo "<p style='color:green'>✅ 数据库表存在</p>";
    }
    
    echo "<h2>步骤4: 检查控制器文件</h2>";
    
    $controller_file = '../application/admin/controller/VipLevelSwitch.php';
    if(file_exists($controller_file)) {
        echo "<p style='color:green'>✅ 控制器文件存在</p>";
        
        // 检查控制器内容
        $controller_content = file_get_contents($controller_file);
        if(strpos($controller_content, 'public function index()') !== false) {
            echo "<p style='color:green'>✅ index方法存在</p>";
        } else {
            echo "<p style='color:red'>❌ index方法不存在</p>";
        }
    } else {
        echo "<p style='color:red'>❌ 控制器文件不存在</p>";
    }
    
    echo "<h2>步骤5: 检查视图文件</h2>";
    
    $view_file = '../application/admin/view/vip_level_switch/index.html';
    if(file_exists($view_file)) {
        echo "<p style='color:green'>✅ 视图文件存在</p>";
    } else {
        echo "<p style='color:red'>❌ 视图文件不存在</p>";
    }
    
    echo "<h2>步骤6: 测试URL访问</h2>";
    
    // 构建测试URL
    $test_url = '/admin.html#/admin/VipLevelSwitch/index.html';
    echo "<p>建议的访问URL: <a href='{$test_url}' target='_blank'>{$test_url}</a></p>";
    
    // 检查是否是单页面应用路由问题
    echo "<p style='color:blue'>💡 如果是单页面应用(SPA)，可能需要使用以下URL格式:</p>";
    echo "<ul>";
    echo "<li><a href='/admin.html#/admin/vip_level_switch/index.html' target='_blank'>/admin.html#/admin/vip_level_switch/index.html</a></li>";
    echo "<li><a href='/admin/VipLevelSwitch/index' target='_blank'>/admin/VipLevelSwitch/index</a></li>";
    echo "</ul>";
    
    echo "<h2>步骤7: 清除所有缓存</h2>";
    
    // 清除数据库缓存
    try {
        $db->exec("TRUNCATE TABLE system_cache");
        echo "<p style='color:green'>✅ 已清除数据库缓存</p>";
    } catch(Exception $e) {
        echo "<p style='color:orange'>⚠️ 数据库缓存清除失败</p>";
    }
    
    // 清除文件缓存
    $cache_dirs = [
        '../runtime/cache',
        '../runtime/temp',
        '../application/runtime/cache',
        '../application/runtime/temp'
    ];
    
    foreach($cache_dirs as $dir) {
        if(is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach($files as $file) {
                if(is_file($file)) {
                    unlink($file);
                }
            }
            echo "<p>已清除缓存目录: {$dir}</p>";
        }
    }
    
    echo "<h2 style='color:green'>🎉 核心问题修复完成！</h2>";
    
    echo "<h3>问题诊断结果:</h3>";
    echo "<p>根据分析，VIP等级管理菜单点击无反应的可能原因是:</p>";
    echo "<ol>";
    echo "<li><strong>URL路由问题</strong> - 菜单URL格式可能不正确</li>";
    echo "<li><strong>单页面应用路由</strong> - 可能需要使用特定的URL格式</li>";
    echo "<li><strong>缓存问题</strong> - 系统缓存导致菜单无法正常工作</li>";
    echo "</ol>";
    
    echo "<h3>解决方案:</h3>";
    echo "<ol>";
    echo "<li>✅ 已修复菜单URL配置</li>";
    echo "<li>✅ 已确保数据库表存在</li>";
    echo "<li>✅ 已清除所有缓存</li>";
    echo "<li>🔄 请尝试以下步骤:</li>";
    echo "<ul>";
    echo "<li>清除浏览器缓存 (Ctrl+F5)</li>";
    echo "<li>重新登录后台管理系统</li>";
    echo "<li>如果仍无法访问，请尝试直接访问: <a href='/admin/VipLevelSwitch/index' target='_blank'>/admin/VipLevelSwitch/index</a></li>";
    echo "</ul>";
    echo "</ol>";
    
    echo "<p><a href='/admin.html' target='_blank' style='background:green;color:white;padding:10px;text-decoration:none;'>返回后台管理</a></p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 操作失败: " . $e->getMessage() . "</p>";
}
?> 