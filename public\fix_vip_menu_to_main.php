<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',  // 请填写您的数据库名
    'username' => 'danss',  // 请填写您的数据库用户名
    'password' => 'MTbhcsYaFBrnMiX6',  // 请填写您的数据库密码
    'charset'  => 'utf8'
];

echo "<h1>VIP等级管理菜单修复 - 移动到主菜单</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>正在检查VIP等级管理菜单...</p>";
    
    // 查找VIP等级管理菜单
    $stmt = $db->query("SELECT id, pid, title, status, sort FROM system_menu WHERE title LIKE '%VIP等级%'");
    $vip_menu = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if(!$vip_menu) {
        echo "<p style='color:red'>❌ 未找到VIP等级管理菜单</p>";
        echo "<p>正在创建VIP等级管理菜单...</p>";
        
        // 获取主菜单的最大排序值
        $stmt = $db->query("SELECT MAX(sort) as max_sort FROM system_menu WHERE pid = 0");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $new_sort = ($result['max_sort'] ?? 0) + 10;
        
        // 创建主菜单
        $sql = "INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
                VALUES (0, 'VIP等级管理', 'admin/VipLevelSwitch/index', 'admin/VipLevelSwitch/index', '', 'fa fa-vip', :sort, 1)";
        $stmt = $db->prepare($sql);
        $stmt->execute(['sort' => $new_sort]);
        $new_menu_id = $db->lastInsertId();
        
        echo "<p style='color:green'>✅ VIP等级管理主菜单创建成功 (ID: {$new_menu_id})</p>";
        
        // 添加子菜单
        $submenus = [
            ['title' => '等级开关管理', 'node' => 'admin/VipLevelSwitch/index', 'url' => 'admin/VipLevelSwitch/index'],
            ['title' => '切换开关', 'node' => 'admin/VipLevelSwitch/toggleSwitch', 'url' => 'admin/VipLevelSwitch/toggleSwitch'],
            ['title' => '批量操作', 'node' => 'admin/VipLevelSwitch/batchToggle', 'url' => 'admin/VipLevelSwitch/batchToggle'],
            ['title' => '重置开关', 'node' => 'admin/VipLevelSwitch/resetAll', 'url' => 'admin/VipLevelSwitch/resetAll']
        ];
        
        foreach($submenus as $index => $submenu) {
            $sql_sub = "INSERT INTO system_menu (pid, title, node, url, params, icon, sort, status) 
                        VALUES (:pid, :title, :node, :url, '', '', :sort, 1)";
            $stmt_sub = $db->prepare($sql_sub);
            $stmt_sub->execute([
                'pid' => $new_menu_id,
                'title' => $submenu['title'],
                'node' => $submenu['node'],
                'url' => $submenu['url'],
                'sort' => ($index + 1) * 10
            ]);
        }
        
        echo "<p style='color:green'>✅ 子菜单创建成功</p>";
        
    } else {
        echo "<p>找到VIP等级管理菜单 (ID: {$vip_menu['id']})</p>";
        echo "<p>当前状态: " . ($vip_menu['pid'] == 0 ? '主菜单' : '子菜单') . "</p>";
        echo "<p>菜单状态: " . ($vip_menu['status'] == 1 ? '启用' : '禁用') . "</p>";
        
        if($vip_menu['pid'] != 0) {
            echo "<p style='color:orange'>⚠️ 菜单当前是子菜单，正在移动到主菜单...</p>";
            
            // 获取主菜单的最大排序值
            $stmt = $db->query("SELECT MAX(sort) as max_sort FROM system_menu WHERE pid = 0");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $new_sort = ($result['max_sort'] ?? 0) + 10;
            
            // 更新菜单为主菜单
            $sql = "UPDATE system_menu SET pid = 0, sort = :sort, status = 1, icon = 'fa fa-vip' WHERE id = :id";
            $stmt = $db->prepare($sql);
            $stmt->execute(['sort' => $new_sort, 'id' => $vip_menu['id']]);
            
            echo "<p style='color:green'>✅ VIP等级管理菜单已移动到主菜单</p>";
        } else {
            echo "<p style='color:green'>✅ VIP等级管理菜单已经是主菜单</p>";
        }
        
        // 确保菜单是启用状态
        if($vip_menu['status'] != 1) {
            $sql = "UPDATE system_menu SET status = 1 WHERE id = :id";
            $stmt = $db->prepare($sql);
            $stmt->execute(['id' => $vip_menu['id']]);
            echo "<p style='color:green'>✅ 菜单已启用</p>";
        }
    }
    
    // 清除缓存
    echo "<p>正在清除系统缓存...</p>";
    
    try {
        $db->exec("TRUNCATE TABLE system_cache");
        echo "<p style='color:green'>✅ 已清除数据库缓存</p>";
    } catch(Exception $e) {
        echo "<p style='color:orange'>⚠️ 清除数据库缓存失败（可能不存在缓存表）</p>";
    }
    
    // 清除文件缓存
    $cache_dirs = [
        '../runtime/cache',
        '../runtime/temp',
        '../application/runtime/cache',
        '../application/runtime/temp'
    ];
    
    foreach($cache_dirs as $dir) {
        if(is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach($files as $file) {
                if(is_file($file)) {
                    unlink($file);
                }
            }
            echo "<p>已清除缓存目录: {$dir}</p>";
        }
    }
    
    echo "<h2 style='color:green'>✅ 修复完成！</h2>";
    echo "<p>VIP等级管理菜单已成功设置为主菜单</p>";
    
    echo "<p><strong>请执行以下步骤：</strong></p>";
    echo "<ol>";
    echo "<li>清除浏览器缓存 (按 Ctrl+F5)</li>";
    echo "<li>重新登录后台管理系统</li>";
    echo "<li>检查主菜单中是否出现 'VIP等级管理'</li>";
    echo "</ol>";
    
    echo "<p><a href='diagnose_vip_menu.php'>运行诊断脚本</a> | <a href='/admin.html' target='_blank'>前往后台管理</a></p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
}
?> 