<?php
/**
 * 钱包地址同步问题修复脚本
 * 统一前端和后端的钱包地址数据
 * 运行目录：public/
 * 独立版本 - 不依赖ThinkPHP框架
 */

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

echo "<h1>钱包地址同步问题修复工具</h1>";
echo "<p><strong>运行目录：</strong>public/ (独立版本)</p>";

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'g5_vt1685_site',
    'username' => 'g5_vt1685_site',
    'password' => 'g5_vt1685_site',
    'charset' => 'utf8mb4'
];

// 连接数据库
try {
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ 数据库连接失败</h3>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库配置是否正确。</p>";
    echo "</div>";
    exit;
}

// 处理修复操作
if (isset($_POST['action']) && $_POST['action'] === 'fix_sync') {
    try {
        // 开启事务
        $pdo->beginTransaction();
        
        echo "<h2>开始修复钱包地址同步问题...</h2>";
        
        // 1. 从 xy_bankinfo 表获取所有钱包数据
        $sql1 = "SELECT uid, username, cardnum as wallet_address, wallet_type, status FROM xy_bankinfo WHERE cardnum != '' AND wallet_type != ''";
        $stmt1 = $pdo->prepare($sql1);
        $stmt1->execute();
        $bankinfo_data = $stmt1->fetchAll();
        
        echo "<p>从 xy_bankinfo 表找到 " . count($bankinfo_data) . " 条钱包数据</p>";
        
        $sync_count = 0;
        $update_count = 0;
        $insert_count = 0;
        
        foreach ($bankinfo_data as $item) {
            // 检查 xy_user_wallet 表中是否已存在该用户的钱包数据
            $sql2 = "SELECT id FROM xy_user_wallet WHERE uid = ?";
            $stmt2 = $pdo->prepare($sql2);
            $stmt2->execute([$item['uid']]);
            $existing = $stmt2->fetch();
            
            if ($existing) {
                // 更新现有记录
                $sql3 = "UPDATE xy_user_wallet SET wallet_address = ?, wallet_type = ?, status = ? WHERE uid = ?";
                $stmt3 = $pdo->prepare($sql3);
                $stmt3->execute([
                    $item['wallet_address'],
                    $item['wallet_type'],
                    $item['status'],
                    $item['uid']
                ]);
                $update_count++;
                echo "<p style='color: blue;'>✓ 更新用户 {$item['uid']} 的钱包地址</p>";
            } else {
                // 插入新记录
                $sql4 = "INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) VALUES (?, ?, ?, ?, ?)";
                $stmt4 = $pdo->prepare($sql4);
                $stmt4->execute([
                    $item['uid'],
                    $item['wallet_address'],
                    $item['wallet_type'],
                    $item['status'],
                    time()
                ]);
                $insert_count++;
                echo "<p style='color: green;'>✓ 为用户 {$item['uid']} 添加钱包地址</p>";
            }
            
            $sync_count++;
        }
        
        // 提交事务
        $pdo->commit();
        
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724;'>🎉 修复完成！</h3>";
        echo "<ul>";
        echo "<li>总共处理了 <strong>{$sync_count}</strong> 条钱包数据</li>";
        echo "<li>更新了 <strong>{$update_count}</strong> 条现有记录</li>";
        echo "<li>新增了 <strong>{$insert_count}</strong> 条记录</li>";
        echo "</ul>";
        echo "<p><strong>现在前端和后端的钱包地址数据已经完全同步！</strong></p>";
        echo "</div>";
        
        echo "<p><a href='check_wallet_sync.php' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>重新检查同步状态</a></p>";
        
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollback();
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24;'>❌ 修复失败！</h3>";
        echo "<p>错误信息: " . $e->getMessage() . "</p>";
        echo "<p>请检查数据库连接和表结构是否正确。</p>";
        echo "</div>";
    }
} else {
    // 显示修复选项
    try {
        // 检查当前数据状态
        $sql1 = "SELECT COUNT(*) as count FROM xy_bankinfo WHERE cardnum != '' AND wallet_type != ''";
        $stmt1 = $pdo->prepare($sql1);
        $stmt1->execute();
        $result1 = $stmt1->fetch();
        $bankinfo_count = $result1['count'];
        
        $sql2 = "SELECT COUNT(*) as count FROM xy_user_wallet";
        $stmt2 = $pdo->prepare($sql2);
        $stmt2->execute();
        $result2 = $stmt2->fetch();
        $user_wallet_count = $result2['count'];
        
        echo "<h2>📊 当前数据状态</h2>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<ul style='list-style-type: none; padding: 0;'>";
        echo "<li>📁 xy_bankinfo 表中有钱包数据的用户: <strong>{$bankinfo_count}</strong> 个</li>";
        echo "<li>📁 xy_user_wallet 表中的钱包记录: <strong>{$user_wallet_count}</strong> 条</li>";
        echo "</ul>";
        echo "</div>";
        
        if ($bankinfo_count > 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>🔧 修复方案</h3>";
            echo "<p>将会执行以下操作：</p>";
            echo "<ol>";
            echo "<li>从 <code>xy_bankinfo</code> 表读取所有用户的钱包地址数据</li>";
            echo "<li>同步到 <code>xy_user_wallet</code> 表中</li>";
            echo "<li>如果用户在 <code>xy_user_wallet</code> 表中已有记录，则更新</li>";
            echo "<li>如果用户在 <code>xy_user_wallet</code> 表中没有记录，则新增</li>";
            echo "<li>确保前端和后端看到的钱包地址完全一致</li>";
            echo "</ol>";
            echo "</div>";
            
            echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>⚠️ 重要提醒</h4>";
            echo "<ul>";
            echo "<li>修复过程会使用数据库事务，确保数据安全</li>";
            echo "<li>如果修复失败，所有更改会自动回滚</li>";
            echo "<li>建议在执行前备份相关数据表</li>";
            echo "</ul>";
            echo "</div>";
            
            echo "<form method='post' style='margin: 20px 0;'>";
            echo "<input type='hidden' name='action' value='fix_sync'>";
            echo "<button type='submit' style='background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; font-weight: bold;' onclick='return confirm(\"确定要执行修复操作吗？\\n\\n这将会修改数据库数据，建议先备份相关表。\")'>🚀 开始修复钱包地址同步问题</button>";
            echo "</form>";
        } else {
            echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>✅ 无需修复</h3>";
            echo "<p>xy_bankinfo 表中没有找到钱包数据，无需进行同步操作。</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24;'>❌ 检查数据状态时出错</h3>";
        echo "<p>错误信息: " . $e->getMessage() . "</p>";
        echo "<p>请检查：</p>";
        echo "<ul>";
        echo "<li>数据库连接是否正常</li>";
        echo "<li>xy_bankinfo 和 xy_user_wallet 表是否存在</li>";
        echo "</ul>";
        echo "</div>";
    }
}

echo "<hr style='margin: 30px 0;'>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>🔗 相关工具</h4>";
echo "<p>";
echo "<a href='check_wallet_sync.php' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>检查同步状态</a>";
echo "<a href='../admin.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>返回后台管理</a>";
echo "</p>";
echo "</div>";

echo "<div style='margin-top: 20px; padding: 10px; background: #f1f1f1; border-radius: 4px; font-size: 12px; color: #666;'>";
echo "<p><strong>使用说明：</strong></p>";
echo "<ol style='margin: 5px 0; padding-left: 20px;'>";
echo "<li>访问 <code>http://你的域名/fix_wallet_sync.php</code> 运行此修复工具</li>";
echo "<li>访问 <code>http://你的域名/check_wallet_sync.php</code> 检查数据同步状态</li>";
echo "<li>修复完成后，前端用户和后端管理员看到的钱包地址将完全一致</li>";
echo "</ol>";
echo "</div>";
?> 