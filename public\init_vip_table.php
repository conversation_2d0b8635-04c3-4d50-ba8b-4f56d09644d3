<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>初始化VIP等级开关数据库表</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>正在检查数据库表...</p>";
    
    // 检查表是否已存在
    $stmt = $db->query("SHOW TABLES LIKE 'xy_vip_level_switch'");
    $table_exists = $stmt->fetch();
    
    if($table_exists) {
        echo "<p style='color:orange'>⚠️ 表 xy_vip_level_switch 已存在</p>";
        
        // 检查数据
        $stmt = $db->query("SELECT COUNT(*) as count FROM xy_vip_level_switch");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>表中已有 {$count['count']} 条记录</p>";
        
        if($count['count'] == 0) {
            echo "<p>正在插入默认数据...</p>";
            goto insert_data;
        } else {
            echo "<p style='color:green'>✅ 数据库表和数据都已存在，无需初始化</p>";
            echo "<p><a href='diagnose_vip_menu.php'>返回诊断页面</a></p>";
            exit;
        }
    } else {
        echo "<p>正在创建数据库表...</p>";
        
        // 创建表
        $create_table_sql = "
        CREATE TABLE `xy_vip_level_switch` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
          `vip_level` int(11) NOT NULL COMMENT 'VIP等级 1-6',
          `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开启 1=开启 0=关闭',
          `switch_name` varchar(50) NOT NULL COMMENT '开关名称',
          `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
          `created_time` int(11) NOT NULL COMMENT '创建时间',
          `updated_time` int(11) NOT NULL COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `vip_level` (`vip_level`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP等级开关配置表'";
        
        $db->exec($create_table_sql);
        echo "<p style='color:green'>✅ 数据库表创建成功</p>";
        
        insert_data:
        
        // 插入默认数据
        echo "<p>正在插入默认数据...</p>";
        
        $current_time = time();
        $insert_sql = "
        INSERT INTO `xy_vip_level_switch` (`vip_level`, `is_enabled`, `switch_name`, `description`, `created_time`, `updated_time`) VALUES
        (1, 1, 'VIP1任务开关', 'VIP1等级任务接单开关', {$current_time}, {$current_time}),
        (2, 1, 'VIP2任务开关', 'VIP2等级任务接单开关', {$current_time}, {$current_time}),
        (3, 1, 'VIP3任务开关', 'VIP3等级任务接单开关', {$current_time}, {$current_time}),
        (4, 1, 'VIP4任务开关', 'VIP4等级任务接单开关', {$current_time}, {$current_time}),
        (5, 1, 'VIP5任务开关', 'VIP5等级任务接单开关', {$current_time}, {$current_time}),
        (6, 1, 'VIP6任务开关', 'VIP6等级任务接单开关', {$current_time}, {$current_time})
        ON DUPLICATE KEY UPDATE 
        `updated_time` = {$current_time}";
        
        $db->exec($insert_sql);
        echo "<p style='color:green'>✅ 默认数据插入成功</p>";
    }
    
    // 验证数据
    echo "<h2>验证结果</h2>";
    $stmt = $db->query("SELECT * FROM xy_vip_level_switch ORDER BY vip_level");
    $switches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>VIP等级</th><th>开关名称</th><th>状态</th><th>描述</th><th>创建时间</th></tr>";
    foreach($switches as $switch) {
        $status_text = $switch['is_enabled'] == 1 ? '开启' : '关闭';
        $created_time = date('Y-m-d H:i:s', $switch['created_time']);
        echo "<tr>";
        echo "<td>VIP{$switch['vip_level']}</td>";
        echo "<td>{$switch['switch_name']}</td>";
        echo "<td style='color:" . ($switch['is_enabled'] ? 'green' : 'red') . "'>{$status_text}</td>";
        echo "<td>{$switch['description']}</td>";
        echo "<td>{$created_time}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2 style='color:green'>✅ 初始化完成！</h2>";
    echo "<p>VIP等级开关数据库表已成功创建并插入默认数据</p>";
    echo "<p><a href='diagnose_vip_menu.php'>返回诊断页面</a></p>";
    echo "<p><a href='/admin.html' target='_blank'>前往后台管理</a></p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
}
?> 