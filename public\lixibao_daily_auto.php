<?php
/**
 * 利息宝每日自动收益发放脚本
 * 这个脚本确保用户在投资期间每天都能收到收益
 * 
 * 使用方法：
 * 1. 手动运行：http://您的域名/lixibao_daily_auto.php
 * 2. 设置定时任务：每天凌晨1点自动运行
 * 
 * 定时任务设置：
 * 0 1 * * * curl -s "http://您的域名/lixibao_daily_auto.php" > /dev/null 2>&1
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 正确的路径设置
define('APP_PATH', __DIR__ . '/../application/');
define('RUNTIME_PATH', __DIR__ . '/../runtime/');

// 引入ThinkPHP框架
require __DIR__ . '/../thinkphp/base.php';

use think\Db;
use think\Container;

// 创建日志文件
$log_file = __DIR__ . '/../runtime/log/lixibao_daily_auto.log';
$log_dir = dirname($log_file);
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

function writeLog($message) {
    global $log_file;
    $time = date('Y-m-d H:i:s');
    file_put_contents($log_file, "$time - $message\n", FILE_APPEND);
    echo "$time - $message<br>\n";
    flush();
}

echo "<h1>利息宝每日自动收益发放</h1>";

try {
    // 初始化应用
    Container::get('app')->initialize();
    
    writeLog("=== 开始每日收益计算 ===");
    
    $current_time = time();
    $today_date = date('Y-m-d', $current_time);
    $today_start = strtotime($today_date . ' 00:00:00');
    $today_end = strtotime($today_date . ' 23:59:59');
    
    writeLog("计算日期: $today_date");
    
    // 获取所有有活跃投资的用户（仍在投资期内）
    $users_with_investments = Db::name('xy_lixibao')
        ->alias('xl')
        ->leftJoin('xy_users u', 'u.id=xl.uid')
        ->where('xl.type', 1) // 转入类型
        ->where('xl.is_qu', 0) // 未取出
        ->where('xl.addtime', '<=', $current_time) // 投资时间在今天之前或今天
        ->where('xl.endtime', '>', $current_time) // 到期时间在今天之后（仍在投资期）
        ->field('u.id, u.username')
        ->group('u.id')
        ->select();
    
    writeLog("找到有活跃投资的用户: " . count($users_with_investments) . " 个");
    
    $processed_users = 0;
    $skipped_users = 0;
    $total_income = 0;
    
    foreach ($users_with_investments as $user_item) {
        $uid = $user_item['id'];
        $username = $user_item['username'];
        
        // 检查该用户今天是否已经发放过收益
        $today_income_check = Db::name('xy_balance_log')
            ->where('uid', $uid)
            ->where('type', 23) // 收益类型
            ->where('status', 1)
            ->where('addtime', '>=', $today_start)
            ->where('addtime', '<=', $today_end)
            ->count();
        
        if ($today_income_check > 0) {
            writeLog("用户 $username (ID: $uid) 今日已发放收益，跳过");
            $skipped_users++;
            continue;
        }
        
        // 获取该用户所有有效的投资记录
        $user_investments = Db::name('xy_lixibao')
            ->where('uid', $uid)
            ->where('type', 1) // 转入类型
            ->where('is_qu', 0) // 未取出
            ->where('addtime', '<=', $current_time) // 投资时间在今天之前或今天
            ->where('endtime', '>', $current_time) // 到期时间在今天之后
            ->select();
        
        if (empty($user_investments)) {
            writeLog("用户 $username (ID: $uid) 没有有效投资，跳过");
            continue;
        }
        
        writeLog("处理用户: $username (ID: $uid)，有效投资: " . count($user_investments) . " 条");
        
        // 按产品分组计算收益
        $product_amounts = [];
        $product_rates = [];
        $product_names = [];
        
        foreach ($user_investments as $investment) {
            $product_id = $investment['sid'];
            
            if (!isset($product_amounts[$product_id])) {
                $product_amounts[$product_id] = 0;
                
                // 获取产品信息
                $product_info = Db::name('xy_lixibao_list')
                    ->where('id', $product_id)
                    ->field('name, bili')
                    ->find();
                    
                if ($product_info) {
                    $product_names[$product_id] = $product_info['name'];
                    $product_rates[$product_id] = $product_info['bili'];
                } else {
                    $product_names[$product_id] = "产品#$product_id";
                    $product_rates[$product_id] = 0.05; // 默认5%
                }
            }
            
            $product_amounts[$product_id] += $investment['num'];
        }
        
        // 计算今日总收益
        $user_total_income = 0;
        $income_details = [];
        
        foreach ($product_amounts as $product_id => $amount) {
            if ($amount <= 0) continue;
            
            $rate = $product_rates[$product_id];
            $product_income = $amount * $rate;
            $user_total_income += $product_income;
            
            $income_details[] = [
                'product_id' => $product_id,
                'product_name' => $product_names[$product_id],
                'amount' => $amount,
                'rate' => $rate,
                'income' => $product_income
            ];
            
            writeLog("  产品: {$product_names[$product_id]}, 投资额: $amount, 利率: " . ($rate*100) . "%, 收益: $product_income");
        }
        
        if ($user_total_income <= 0) {
            writeLog("用户 $username (ID: $uid) 计算收益为0，跳过");
            continue;
        }
        
        try {
            // 开始事务
            Db::startTrans();
            
            // 1. 更新用户余额
            Db::name('xy_users')->where('id', $uid)->setInc('balance', $user_total_income);
            
            // 2. 为每个产品添加收益记录到xy_lixibao表
            foreach ($income_details as $detail) {
                Db::name('xy_lixibao')->insert([
                    'uid'         => $uid,
                    'num'         => $detail['income'],
                    'addtime'     => $current_time,
                    'type'        => 3, // 收益类型
                    'status'      => 1,
                    'yuji_num'    => $detail['income'],
                    'real_num'    => $detail['income'],
                    'is_sy'       => 1,
                    'sid'         => $detail['product_id'],
                    'shouxu'      => 0,
                    'bili'        => $detail['rate'],
                    'day'         => 1,
                    'update_time' => $current_time,
                ]);
            }
            
            // 3. 添加余额变动记录
            $order_id = 'DAILY' . date('ymdHis') . $uid;
            Db::name('xy_balance_log')->insert([
                'uid'       => $uid,
                'oid'       => $order_id,
                'num'       => $user_total_income,
                'type'      => 23, // 收益类型
                'status'    => 1,
                'addtime'   => $current_time
            ]);
            
            // 提交事务
            Db::commit();
            
            $processed_users++;
            $total_income += $user_total_income;
            
            writeLog("✓ 用户 $username (ID: $uid) 收益发放成功: $user_total_income 元");
            
        } catch (\Exception $e) {
            Db::rollback();
            writeLog("✗ 用户 $username (ID: $uid) 收益发放失败: " . $e->getMessage());
        }
    }
    
    writeLog("=== 每日收益计算完成 ===");
    writeLog("处理用户数: $processed_users");
    writeLog("跳过用户数: $skipped_users");
    writeLog("发放总收益: $total_income 元");
    
    // 显示结果
    echo "<div style='background-color:#d4edda; padding:20px; margin:20px 0; border:1px solid #c3e6cb; border-radius:5px;'>";
    echo "<h2>✅ 每日收益计算完成</h2>";
    echo "<p><strong>计算日期:</strong> $today_date</p>";
    echo "<p><strong>处理用户数:</strong> $processed_users</p>";
    echo "<p><strong>跳过用户数:</strong> $skipped_users</p>";
    echo "<p><strong>发放总收益:</strong> $total_income 元</p>";
    echo "</div>";
    
    if ($processed_users > 0) {
        echo "<div style='background-color:#d1ecf1; padding:15px; margin:10px 0; border:1px solid #bee5eb; border-radius:5px;'>";
        echo "<h3>✅ 收益发放成功</h3>";
        echo "<p>• 已为 $processed_users 个用户发放今日收益</p>";
        echo "<p>• 每个用户的收益将在其投资期间每天自动发放</p>";
        echo "<p>• 所有操作已记录在数据库和日志中</p>";
        echo "</div>";
    }
    
    // 显示下次运行建议
    echo "<div style='background-color:#fff3cd; padding:15px; margin:10px 0; border:1px solid #ffeaa7; border-radius:5px;'>";
    echo "<h3>🔧 确保每日自动运行</h3>";
    echo "<p><strong>设置定时任务:</strong></p>";
    echo "<pre>0 1 * * * curl -s \"http://您的域名/lixibao_daily_auto.php\" > /dev/null 2>&1</pre>";
    echo "<p>这样用户的投资就能在整个投资期间每天都收到收益了！</p>";
    echo "</div>";
    
} catch (\Exception $e) {
    writeLog("执行出错: " . $e->getMessage());
    echo "<div style='background-color:#f8d7da; padding:15px; margin:10px 0; border:1px solid #f5c6cb; border-radius:5px;'>";
    echo "<h3>❌ 执行错误</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>相关工具:</h3>";
echo "<p>• <a href='lixibao_fix_simple.php'>补发历史收益</a></p>";
echo "<p>• <a href='lixibao_debug.php'>诊断工具</a></p>";
echo "<p>处理时间: " . date('Y-m-d H:i:s') . "</p>";
?> 