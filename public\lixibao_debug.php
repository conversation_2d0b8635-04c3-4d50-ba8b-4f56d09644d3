<?php
/**
 * 利息宝问题诊断脚本 - 简化版
 * 浏览器访问：http://您的域名/lixibao_debug.php
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 正确的路径设置
define('APP_PATH', __DIR__ . '/../application/');
define('RUNTIME_PATH', __DIR__ . '/../runtime/');

// 引入ThinkPHP框架 - 正确路径
require __DIR__ . '/../thinkphp/base.php';

use think\Db;
use think\Container;

// 检查函数是否已存在
if (!function_exists('getSn')) {
    function getSn($prefix = '') {
        return $prefix . date('ymdHis') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
    }
}

echo "<h1>利息宝收益问题诊断</h1>";

try {
    // 初始化应用
    Container::get('app')->initialize();
    
    echo "<p>✅ 框架加载成功</p>";
    
    // 1. 检查数据库连接
    $user_count = Db::name('xy_users')->count();
    echo "<p>✅ 数据库连接成功，用户总数: $user_count</p>";
    
    // 2. 检查最近的投资记录
    $recent_investments = Db::name('xy_lixibao')
        ->alias('xl')
        ->leftJoin('xy_users u', 'u.id=xl.uid')
        ->leftJoin('xy_lixibao_list xll', 'xll.id=xl.sid')
        ->where('xl.type', 1)
        ->where('xl.addtime', '>=', strtotime('-10 days'))
        ->field('xl.*, u.username, xll.name as product_name, xll.bili')
        ->order('xl.addtime desc')
        ->limit(10)
        ->select();
    
    echo "<h3>最近10天投资记录 (" . count($recent_investments) . " 条)</h3>";
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr style='background-color:#f0f0f0;'>";
    echo "<th>用户</th><th>产品</th><th>金额</th><th>利率</th><th>投资时间</th><th>到期时间</th><th>状态</th>";
    echo "</tr>";
    
    foreach ($recent_investments as $inv) {
        $status = $inv['is_qu'] ? '已取出' : ($inv['endtime'] > time() ? '投资中' : '已到期');
        $rate = ($inv['bili'] ?: 0.05) * 100;
        echo "<tr>";
        echo "<td>{$inv['username']}</td>";
        echo "<td>{$inv['product_name']}</td>";
        echo "<td>{$inv['num']}</td>";
        echo "<td>{$rate}%</td>";
        echo "<td>" . date('Y-m-d H:i', $inv['addtime']) . "</td>";
        echo "<td>" . date('Y-m-d H:i', $inv['endtime']) . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. 检查最近的收益记录
    $recent_income = Db::name('xy_balance_log')
        ->alias('bl')
        ->leftJoin('xy_users u', 'u.id=bl.uid')
        ->where('bl.type', 23)
        ->where('bl.addtime', '>=', strtotime('-7 days'))
        ->field('bl.*, u.username')
        ->order('bl.addtime desc')
        ->limit(10)
        ->select();
    
    echo "<h3>最近7天收益记录 (" . count($recent_income) . " 条)</h3>";
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr style='background-color:#f0f0f0;'>";
    echo "<th>用户</th><th>收益金额</th><th>发放时间</th><th>状态</th>";
    echo "</tr>";
    
    foreach ($recent_income as $income) {
        $status = $income['status'] ? '成功' : '待处理';
        echo "<tr>";
        echo "<td>{$income['username']}</td>";
        echo "<td>{$income['num']}</td>";
        echo "<td>" . date('Y-m-d H:i:s', $income['addtime']) . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. 检查问题用户（有投资但最近没收益的）
    $problem_users = Db::query("
        SELECT u.id, u.username, COUNT(xl.id) as investment_count,
               (SELECT COUNT(*) FROM xy_balance_log WHERE uid=u.id AND type=23 AND addtime >= ?) as recent_income_count
        FROM xy_users u
        LEFT JOIN xy_lixibao xl ON u.id = xl.uid AND xl.type = 1 AND xl.is_qu = 0 AND xl.endtime > ?
        WHERE u.lixibao_balance > 0 OR xl.id IS NOT NULL
        GROUP BY u.id
        HAVING investment_count > 0 AND recent_income_count = 0
        LIMIT 10
    ", [strtotime('-3 days'), time()]);
    
    echo "<h3>可能有问题的用户 (" . count($problem_users) . " 个)</h3>";
    if (!empty($problem_users)) {
        echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
        echo "<tr style='background-color:#ffeeee;'>";
        echo "<th>用户ID</th><th>用户名</th><th>投资记录数</th><th>最近3天收益次数</th>";
        echo "</tr>";
        
        foreach ($problem_users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['investment_count']}</td>";
            echo "<td>{$user['recent_income_count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background-color:#fff3cd; padding:15px; margin:10px 0; border:1px solid #ffeaa7;'>";
        echo "<h4>建议操作：</h4>";
        echo "<p><a href='fix_lixibao_quick.php?days=7' target='_blank' style='background-color:#28a745; color:white; padding:10px 20px; text-decoration:none; border-radius:5px;'>立即修复最近7天收益</a></p>";
        echo "</div>";
    } else {
        echo "<p>✅ 未发现明显问题用户</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background-color:#f8d7da; padding:15px; margin:10px 0; border:1px solid #f5c6cb;'>";
    echo "<h3>诊断出错：</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>相关工具：</h3>";
echo "<p><a href='fix_lixibao_quick.php'>快速修复脚本</a></p>";
echo "<p>诊断时间: " . date('Y-m-d H:i:s') . "</p>";
?> 