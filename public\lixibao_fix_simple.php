<?php
/**
 * 利息宝收益简单修复脚本
 * 浏览器访问：http://您的域名/lixibao_fix_simple.php?days=7
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 正确的路径设置
define('APP_PATH', __DIR__ . '/../application/');
define('RUNTIME_PATH', __DIR__ . '/../runtime/');

// 引入ThinkPHP框架
require __DIR__ . '/../thinkphp/base.php';

use think\Db;
use think\Container;

echo "<h1>利息宝收益修复</h1>";

try {
    // 初始化应用
    Container::get('app')->initialize();
    
    echo "<p>✅ 系统初始化成功</p>";
    
    // 获取要修复的天数
    $fix_days = isset($_GET['days']) ? intval($_GET['days']) : 7;
    
    echo "<p>开始检查最近 $fix_days 天的收益问题...</p>";
    
    $current_time = time();
    $fixed_count = 0;
    $total_amount = 0;
    
    // 检查每一天
    for ($i = $fix_days - 1; $i >= 0; $i--) {
        $check_date = date('Y-m-d', strtotime("-$i days"));
        $day_start = strtotime($check_date . ' 00:00:00');
        $day_end = strtotime($check_date . ' 23:59:59');
        
        echo "<h3>处理日期: $check_date</h3>";
        
        // 跳过未来日期
        if ($day_start > $current_time) {
            echo "<p>跳过未来日期</p>";
            continue;
        }
        
        // 查找需要修复的用户
        $sql = "
            SELECT DISTINCT u.id, u.username
            FROM xy_users u
            INNER JOIN xy_lixibao xl ON u.id = xl.uid
            WHERE xl.type = 1 
            AND xl.is_qu = 0
            AND xl.addtime <= ?
            AND xl.endtime > ?
            AND NOT EXISTS (
                SELECT 1 FROM xy_balance_log bl 
                WHERE bl.uid = u.id 
                AND bl.type = 23 
                AND bl.status = 1
                AND bl.addtime >= ? 
                AND bl.addtime <= ?
            )
        ";
        
        $problem_users = Db::query($sql, [$day_end, $day_start, $day_start, $day_end]);
        
        if (empty($problem_users)) {
            echo "<p>✓ 无需修复</p>";
            continue;
        }
        
        echo "<p>找到 " . count($problem_users) . " 个用户需要补发收益</p>";
        echo "<ul>";
        
        foreach ($problem_users as $user) {
            $uid = $user['id'];
            $username = $user['username'];
            
            // 获取用户的投资记录
            $investments = Db::name('xy_lixibao')
                ->alias('xl')
                ->leftJoin('xy_lixibao_list xll', 'xll.id=xl.sid')
                ->where('xl.uid', $uid)
                ->where('xl.type', 1)
                ->where('xl.is_qu', 0)
                ->where('xl.addtime', '<=', $day_end)
                ->where('xl.endtime', '>', $day_start)
                ->field('xl.num, xl.sid, xll.bili, xll.name as product_name')
                ->select();
            
            if (empty($investments)) {
                continue;
            }
            
            // 计算总收益
            $total_income = 0;
            $details = [];
            
            foreach ($investments as $inv) {
                $rate = $inv['bili'] ?: 0.05; // 默认5%
                $daily_income = $inv['num'] * $rate;
                $total_income += $daily_income;
                
                $details[] = [
                    'sid' => $inv['sid'],
                    'amount' => $inv['num'],
                    'rate' => $rate,
                    'income' => $daily_income,
                    'name' => $inv['product_name'] ?: 'Type D/E'
                ];
            }
            
            if ($total_income > 0) {
                try {
                    // 开始事务
                    Db::startTrans();
                    
                    // 1. 更新用户余额
                    Db::name('xy_users')->where('id', $uid)->setInc('balance', $total_income);
                    
                    // 2. 添加投资收益记录
                    foreach ($details as $detail) {
                        Db::name('xy_lixibao')->insert([
                            'uid' => $uid,
                            'num' => $detail['income'],
                            'addtime' => $day_start + 43200,
                            'type' => 3,
                            'status' => 1,
                            'yuji_num' => $detail['income'],
                            'real_num' => $detail['income'],
                            'is_sy' => 1,
                            'sid' => $detail['sid'],
                            'shouxu' => 0,
                            'bili' => $detail['rate'],
                            'day' => 1,
                            'update_time' => $day_start + 43200,
                        ]);
                    }
                    
                    // 3. 添加余额记录
                    $order_id = 'FIX' . date('ymdHis') . $uid;
                    Db::name('xy_balance_log')->insert([
                        'uid' => $uid,
                        'oid' => $order_id,
                        'num' => $total_income,
                        'type' => 23,
                        'status' => 1,
                        'addtime' => $day_start + 43200
                    ]);
                    
                    // 提交事务
                    Db::commit();
                    
                    echo "<li>✓ $username: 补发 $total_income 元";
                    
                    // 显示详情
                    $info = [];
                    foreach ($details as $d) {
                        $info[] = "{$d['name']}({$d['amount']}*" . ($d['rate']*100) . "%={$d['income']})";
                    }
                    echo " [" . implode(', ', $info) . "]</li>";
                    
                    $fixed_count++;
                    $total_amount += $total_income;
                    
                } catch (Exception $e) {
                    Db::rollback();
                    echo "<li>✗ $username: 失败 - " . $e->getMessage() . "</li>";
                }
            }
        }
        
        echo "</ul>";
    }
    
    // 显示结果
    echo "<div style='background-color:#d4edda; padding:20px; margin:20px 0; border:1px solid #c3e6cb; border-radius:5px;'>";
    echo "<h2>✅ 修复完成</h2>";
    echo "<p><strong>修复用户数:</strong> $fixed_count</p>";
    echo "<p><strong>补发总金额:</strong> $total_amount 元</p>";
    echo "</div>";
    
    if ($fixed_count > 0) {
        echo "<div style='background-color:#d1ecf1; padding:15px; margin:10px 0; border:1px solid #bee5eb; border-radius:5px;'>";
        echo "<h3>修复说明:</h3>";
        echo "<p>• 已为 $fixed_count 个用户补发缺失的每日收益</p>";
        echo "<p>• 收益按产品利率正确计算并发放到用户余额</p>";
        echo "<p>• 所有操作已完整记录在数据库中</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background-color:#f8d7da; padding:15px; margin:10px 0; border:1px solid #f5c6cb; border-radius:5px;'>";
    echo "<h3>❌ 执行错误</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>使用说明:</h3>";
echo "<p>• 检查最近7天: <a href='?days=7'>lixibao_fix_simple.php?days=7</a></p>";
echo "<p>• 检查最近15天: <a href='?days=15'>lixibao_fix_simple.php?days=15</a></p>";
echo "<p>• 诊断工具: <a href='lixibao_debug.php'>lixibao_debug.php</a></p>";
echo "<p>修复时间: " . date('Y-m-d H:i:s') . "</p>";
?> 