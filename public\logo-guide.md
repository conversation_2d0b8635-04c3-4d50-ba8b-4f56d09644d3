# Logo 更换指南

## 当前Logo位置

登录页面的Logo位于以下文件中：
- 文件路径：`application/index/view/user/login.html`
- 行数：约第 831-833 行

## 当前Logo代码

```html
<!-- Logo区域 -->
<div class="logo-container">
    <img src="/public/image/logo.png" alt="Logo" class="app-logo">
</div>
```

## Logo文件位置

当前logo文件位于：`public/image/logo.png`

## 更换Logo的方法

### 方法1：替换现有图片文件（推荐）

1. 将您的新logo图片重命名为 `logo.png`
2. 替换 `public/image/logo.png` 文件
3. 确保新logo尺寸合适（建议80x80像素或更大的正方形）

### 方法2：使用不同的图片文件

1. 将您的logo图片上传到 `public/image/` 目录
2. 修改登录页面代码中的图片路径：

```html
<!-- Logo区域 -->
<div class="logo-container">
    <img src="/public/image/your-new-logo.png" alt="Logo" class="app-logo">
</div>
```

### 方法3：使用SVG代码

如果您有SVG格式的logo，可以替换img标签为SVG代码：

```html
<!-- Logo区域 -->
<div class="logo-container">
    <svg class="app-logo" viewBox="0 0 250 250" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- 您的SVG路径代码 -->
    </svg>
</div>
```

### 方法4：使用在线图片

```html
<!-- Logo区域 -->
<div class="logo-container">
    <img src="https://your-domain.com/logo.png" alt="Logo" class="app-logo">
</div>
```

## Logo样式说明

Logo的CSS样式定义在同一文件中：

```css
.app-logo {
    width: 80px;
    height: 80px;
    animation: pulse 3s infinite ease-in-out;
}
```

您可以根据需要调整：
- `width` 和 `height`：调整logo大小
- `animation`：移除或修改动画效果

## 注意事项

1. 建议logo尺寸为正方形（1:1比例）
2. 推荐使用PNG格式，支持透明背景
3. 文件大小建议控制在100KB以内
4. 如果使用深色logo，请确保在深色背景下的可见性

## 修改后需要上传的文件

- `application/index/view/user/login.html` （如果修改了代码）
- `public/image/logo.png` （如果替换了logo文件） 