<?php
// 框架入口文件
define('APP_PATH', __DIR__ . '/../application/');

// 加载基础文件
require __DIR__ . '/../thinkphp/base.php';

// 启动会话
session_start();

// 显示页面头部
echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>会话修复工具</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .card { border: 1px solid #ddd; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        .button { display: inline-block; padding: 10px 15px; background: #4CAF50; color: white; text-decoration: none; border-radius: 4px; margin-right: 10px; }
        .warning { background-color: #fffacd; padding: 10px; border-left: 4px solid #ffd700; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>会话状态修复工具</h1>';

// 获取会话和Cookie中的用户ID
$uid = session('user_id');
$cookie_uid = isset($_COOKIE['user_id']) ? $_COOKIE['user_id'] : null;

// 显示当前状态
echo '<div class="card">
    <h2>当前状态</h2>
    <p>会话ID: <strong>' . session_id() . '</strong></p>
    <p>会话中用户ID: <strong>' . ($uid ? $uid : '未设置') . '</strong></p>
    <p>Cookie中用户ID: <strong>' . ($cookie_uid ? $cookie_uid : '未设置') . '</strong></p>
</div>';

// 处理修复请求
$action = isset($_GET['action']) ? $_GET['action'] : '';

if ($action == 'fix') {
    echo '<div class="card">';
    echo '<h2>执行修复操作</h2>';
    
    // 安全修复：不再从Cookie恢复会话，防止跨浏览器自动登录
    if (!$uid && $cookie_uid) {
        echo '<p class="error">× 检测到Cookie中有用户ID但会话已失效。为了安全，请重新登录。</p>';
        echo '<p><a href="/index/user/login" class="button" style="background-color: #f44336;">重新登录</a></p>';
    } 
    // 如果会话有用户ID，清除可能存在的旧Cookie
    elseif ($uid && $cookie_uid) {
        setcookie("user_id", "", time() - 3600, "/"); // 清除cookie
        echo '<p class="success">✓ 已清除旧的Cookie，现在只依赖会话状态。</p>';
    }
    // 如果两者都没有，提示登录
    elseif (!$uid && !$cookie_uid) {
        echo '<p class="error">× 无法修复，请先登录。</p>';
        echo '<p><a href="/index/user/login" class="button" style="background-color: #f44336;">前往登录</a></p>';
    }
    // 如果只有会话有效
    elseif ($uid && !$cookie_uid) {
        echo '<p class="success">✓ 会话状态正常，无需修复。</p>';
    }
    // 其他情况
    else {
        echo '<p class="success">✓ 会话状态正常。</p>';
    }
    
    echo '</div>';
}

// 显示详细信息
echo '<div class="card">
    <h2>详细信息</h2>
    <h3>会话变量</h3>
    <pre>';
print_r($_SESSION);
echo '</pre>
    <h3>Cookie变量</h3>
    <pre>';
print_r($_COOKIE);
echo '</pre>
</div>';

// 显示可用操作链接
echo '<div class="card">
    <h2>可用操作</h2>
    <p><a href="?action=fix" class="button">修复会话状态</a></p>
    <p><a href="/index/login/index" class="button" style="background-color: #2196F3;">重新登录</a></p>
    <p><a href="/chat_redirect.php" class="button" style="background-color: #FF9800;">尝试进入聊天室</a></p>
    <p><a href="/chat_redirect.php?debug=1" class="button" style="background-color: #9C27B0;">带调试信息进入聊天室</a></p>
    <p><a href="/index/support/index" class="button" style="background-color: #607D8B;">返回客服页面</a></p>
</div>

<div class="warning">
    <strong>提示：</strong> 如果修复后仍有问题，请尝试清除浏览器缓存和Cookie，然后重新登录。
</div>

</body>
</html>'; 