<!DOCTYPE html>
<html>
<head>
    <title>IP检测测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>IP地理位置检测测试</h1>
    <button onclick="testAPI()">测试API</button>
    <div id="result"></div>

    <script>
    function testAPI() {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = '正在检测...';
        
        fetch('/index/api/get_country_by_ip')
        .then(response => {
            console.log('响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('返回数据:', data);
            resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            console.error('错误:', error);
            resultDiv.innerHTML = '错误: ' + error.message;
        });
    }
    </script>
</body>
</html> 