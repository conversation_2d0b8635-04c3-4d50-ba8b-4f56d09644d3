<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>测试任务开关功能</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>当前等级任务开关状态</h2>";
    $stmt = $db->query("SELECT id, name, task_enabled FROM xy_level ORDER BY level");
    $levels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr><th>ID</th><th>等级名称</th><th>任务开关</th><th>操作</th></tr>";
    foreach($levels as $level) {
        $status = $level['task_enabled'] == 1 ? '已开启' : '已关闭';
        $color = $level['task_enabled'] == 1 ? 'green' : 'red';
        $action = $level['task_enabled'] == 1 ? '关闭' : '开启';
        $newStatus = $level['task_enabled'] == 1 ? 0 : 1;
        
        echo "<tr>";
        echo "<td>{$level['id']}</td>";
        echo "<td>{$level['name']}</td>";
        echo "<td style='color:{$color}'>{$status}</td>";
        echo "<td><button onclick=\"testToggle({$level['id']}, {$newStatus})\">{$action}</button></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 处理AJAX请求
    if(isset($_POST['action']) && $_POST['action'] == 'toggle') {
        $id = intval($_POST['id']);
        $status = intval($_POST['status']);
        
        $stmt = $db->prepare("UPDATE xy_level SET task_enabled = ? WHERE id = ?");
        $result = $stmt->execute([$status, $id]);
        
        if($result) {
            echo json_encode(['success' => true, 'message' => '操作成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '操作失败']);
        }
        exit;
    }
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 操作失败: " . $e->getMessage() . "</p>";
}
?>

<script>
function testToggle(id, status) {
    if(confirm('确定要切换该等级的任务开关吗？')) {
        fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=toggle&id=' + id + '&status=' + status
        })
        .then(response => response.json())
        .then(data => {
            if(data.success) {
                alert('操作成功！');
                location.reload();
            } else {
                alert('操作失败：' + data.message);
            }
        })
        .catch(error => {
            alert('网络错误：' + error);
        });
    }
}
</script>

<style>
table { margin: 20px 0; }
th, td { padding: 10px; text-align: center; }
button { padding: 5px 10px; cursor: pointer; }
</style> 