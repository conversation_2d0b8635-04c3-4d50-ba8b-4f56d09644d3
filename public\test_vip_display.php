<?php

// 数据库配置
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',
    'username' => 'danss',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset'  => 'utf8'
];

echo "<h1>测试VIP等级显示功能</h1>";

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1. 检查xy_level表结构</h2>";
    $stmt = $db->query("DESCRIBE xy_level");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $has_task_enabled = false;
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>默认值</th></tr>";
    foreach($columns as $col) {
        echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Default']}</td></tr>";
        if($col['Field'] == 'task_enabled') {
            $has_task_enabled = true;
        }
    }
    echo "</table>";
    
    if($has_task_enabled) {
        echo "<p style='color:green'>✅ task_enabled字段存在</p>";
    } else {
        echo "<p style='color:red'>❌ task_enabled字段不存在</p>";
    }
    
    echo "<h2>2. 检查VIP等级数据</h2>";
    $stmt = $db->query("SELECT id, name, level, task_enabled FROM xy_level ORDER BY level");
    $levels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr><th>ID</th><th>等级名称</th><th>等级</th><th>任务开关</th><th>状态</th></tr>";
    foreach($levels as $level) {
        $status = $level['task_enabled'] == 1 ? '开启' : '关闭';
        $color = $level['task_enabled'] == 1 ? 'green' : 'red';
        echo "<tr>";
        echo "<td>{$level['id']}</td>";
        echo "<td>{$level['name']}</td>";
        echo "<td>{$level['level']}</td>";
        echo "<td>{$level['task_enabled']}</td>";
        echo "<td style='color:{$color}'>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>3. 模拟前端数据处理</h2>";
    
    // 模拟用户等级为3
    $user_level = 3;
    echo "<p>模拟用户等级: {$user_level}</p>";
    
    // 获取任务分类数据（模拟前端控制器逻辑）
    $stmt = $db->query("
        SELECT c.name, c.id, c.cate_info, c.cate_pic, c.pipei_min, c.pipei_max, c.bili, 
               u.name as levelname, u.pic, u.pic2, u.level, u.num_min, u.order_num, u.task_enabled
        FROM xy_goods_cate c
        LEFT JOIN xy_level u ON u.id = c.level_id
        ORDER BY c.id ASC
    ");
    $cate_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr><th>分类ID</th><th>分类名称</th><th>等级</th><th>任务开关</th><th>用户可访问</th><th>最终状态</th></tr>";
    
    foreach($cate_data as $cate) {
        $task_enabled = isset($cate['task_enabled']) ? (int)$cate['task_enabled'] : 1;
        $user_can_access = $user_level >= $cate['level'];
        $is_available = $user_can_access && $task_enabled;
        
        $status_text = '';
        if(!$user_can_access) {
            $status_text = '未解锁（等级不够）';
        } elseif(!$task_enabled) {
            $status_text = '暂时关闭';
        } else {
            $status_text = '可用';
        }
        
        $color = $is_available ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>{$cate['id']}</td>";
        echo "<td>{$cate['name']}</td>";
        echo "<td>{$cate['level']}</td>";
        echo "<td>" . ($task_enabled ? '开启' : '关闭') . "</td>";
        echo "<td>" . ($user_can_access ? '是' : '否') . "</td>";
        echo "<td style='color:{$color}'>{$status_text}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>4. 测试结果</h2>";
    $enabled_count = 0;
    $disabled_count = 0;
    $unlocked_count = 0;
    
    foreach($cate_data as $cate) {
        $task_enabled = isset($cate['task_enabled']) ? (int)$cate['task_enabled'] : 1;
        $user_can_access = $user_level >= $cate['level'];
        
        if($user_can_access && $task_enabled) {
            $enabled_count++;
        } elseif($user_can_access && !$task_enabled) {
            $disabled_count++;
        } elseif(!$user_can_access) {
            $unlocked_count++;
        }
    }
    
    echo "<ul>";
    echo "<li>可用等级: <strong style='color:green'>{$enabled_count}</strong> 个</li>";
    echo "<li>暂时关闭: <strong style='color:orange'>{$disabled_count}</strong> 个</li>";
    echo "<li>未解锁: <strong style='color:red'>{$unlocked_count}</strong> 个</li>";
    echo "</ul>";
    
    if($disabled_count > 0) {
        echo "<p style='color:green'>✅ 任务开关功能正常工作！有 {$disabled_count} 个等级被设置为暂时关闭状态。</p>";
    } else {
        echo "<p style='color:orange'>⚠️ 所有等级都是开启状态，可以在后台管理中测试关闭功能。</p>";
    }
    
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/admin/users/level.html'>返回后台等级管理</a></p>";
echo "<p><a href='/index/index/home.html'>查看前端首页</a></p>";
echo "<p><a href='cleanup_temp_files.php'>清理临时文件</a></p>";
?> 