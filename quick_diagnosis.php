<?php
// 快速诊断脚本 - 检查定时任务执行但没有收益的问题
echo "=== 利息宝定时任务执行问题诊断 ===\n";
echo "诊断时间: " . date('Y-m-d H:i:s') . "\n\n";

// 数据库配置
$host = '127.0.0.1';
$port = 3306;
$database = 'g5_vt1685_site';
$username = 'g5_vt1685_site';
$password = 'g5_vt1685_site';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

// 1. 检查今天和昨天的收益记录
echo "=== 1. 检查最近收益记录 ===\n";

$today_start = strtotime(date('Y-m-d'));
$today_end = $today_start + 86400;
$yesterday_start = strtotime(date('Y-m-d', strtotime('-1 day')));
$yesterday_end = $yesterday_start + 86400;

echo "今天日期: " . date('Y-m-d') . "\n";
echo "昨天日期: " . date('Y-m-d', strtotime('-1 day')) . "\n\n";

// 检查今天的收益记录
$stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime BETWEEN $today_start AND $today_end");
$today_income = $stmt->fetchColumn();
echo "今天收益记录数: $today_income\n";

// 检查昨天的收益记录
$stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime BETWEEN $yesterday_start AND $yesterday_end");
$yesterday_income = $stmt->fetchColumn();
echo "昨天收益记录数: $yesterday_income\n";

// 检查最近3天的收益记录
$three_days_ago = time() - (3 * 24 * 3600);
$stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime > $three_days_ago");
$recent_income = $stmt->fetchColumn();
echo "最近3天收益记录数: $recent_income\n\n";

// 2. 检查当前活跃投资
echo "=== 2. 检查当前活跃投资 ===\n";
$current_time = time();
$stmt = $pdo->query("
    SELECT COUNT(*) as total_investments,
           COUNT(DISTINCT uid) as unique_users,
           SUM(num) as total_amount
    FROM xy_lixibao 
    WHERE type = 1 AND status = 1 AND endtime > $current_time
");
$investment_info = $stmt->fetch(PDO::FETCH_ASSOC);

echo "当前活跃投资数: {$investment_info['total_investments']}\n";
echo "投资用户数: {$investment_info['unique_users']}\n";
echo "投资总金额: ¥{$investment_info['total_amount']}\n\n";

// 3. 检查有投资但没有收益的用户
if ($investment_info['total_investments'] > 0) {
    echo "=== 3. 检查问题用户（有投资但没有收益）===\n";
    
    $stmt = $pdo->query("
        SELECT l.uid, u.username, 
               COUNT(l.id) as investment_count,
               SUM(l.num) as total_investment,
               MAX(l.addtime) as last_investment_time
        FROM xy_lixibao l
        LEFT JOIN xy_users u ON l.uid = u.id
        WHERE l.type = 1 AND l.status = 1 AND l.endtime > $current_time
        AND NOT EXISTS (
            SELECT 1 FROM xy_balance_log bl 
            WHERE bl.uid = l.uid AND bl.type = 23 
            AND bl.addtime > $yesterday_start
        )
        GROUP BY l.uid
        ORDER BY l.uid
        LIMIT 10
    ");
    
    $problem_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($problem_users)) {
        echo "发现 " . count($problem_users) . " 个用户有投资但最近没有收益:\n";
        foreach ($problem_users as $user) {
            $last_investment = date('Y-m-d H:i:s', $user['last_investment_time']);
            echo "  用户ID: {$user['uid']}, 用户名: {$user['username']}, 投资笔数: {$user['investment_count']}, 投资总额: ¥{$user['total_investment']}, 最后投资时间: $last_investment\n";
            
            // 检查这个用户的具体投资情况
            $stmt_detail = $pdo->prepare("
                SELECT l.id, l.num, l.addtime, l.endtime, l.product_id,
                       FROM_UNIXTIME(l.addtime) as start_date,
                       FROM_UNIXTIME(l.endtime) as end_date,
                       DATEDIFF(FROM_UNIXTIME(l.endtime), NOW()) as days_left
                FROM xy_lixibao l 
                WHERE l.uid = ? AND l.type = 1 AND l.status = 1 AND l.endtime > ?
                ORDER BY l.addtime DESC
            ");
            $stmt_detail->execute([$user['uid'], $current_time]);
            $investments = $stmt_detail->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($investments as $inv) {
                echo "    - 投资ID: {$inv['id']}, 金额: ¥{$inv['num']}, 开始: {$inv['start_date']}, 结束: {$inv['end_date']}, 剩余: {$inv['days_left']}天\n";
            }
            echo "\n";
        }
    } else {
        echo "✅ 所有有投资的用户都有最近的收益记录\n";
    }
}

// 4. 检查定时任务执行记录（如果有日志表的话）
echo "=== 4. 检查定时任务执行逻辑问题 ===\n";

// 检查是否存在重复收益检查的逻辑问题
$stmt = $pdo->query("
    SELECT l.uid, u.username,
           COUNT(l.id) as investment_count,
           (SELECT COUNT(*) FROM xy_balance_log bl 
            WHERE bl.uid = l.uid AND bl.type = 23 
            AND bl.addtime > $today_start) as today_income_count
    FROM xy_lixibao l
    LEFT JOIN xy_users u ON l.uid = u.id
    WHERE l.type = 1 AND l.status = 1 AND l.endtime > $current_time
    GROUP BY l.uid
    HAVING today_income_count > 0
    LIMIT 5
");

$users_with_today_income = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (!empty($users_with_today_income)) {
    echo "今天已经有收益的用户:\n";
    foreach ($users_with_today_income as $user) {
        echo "  用户ID: {$user['uid']}, 用户名: {$user['username']}, 今天收益记录数: {$user['today_income_count']}\n";
    }
} else {
    echo "今天还没有用户收到收益\n";
}

// 5. 检查产品配置
echo "\n=== 5. 检查产品配置 ===\n";
$stmt = $pdo->query("SELECT * FROM xy_lixibao_list ORDER BY id");
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($products)) {
    echo "❌ 没有找到利息宝产品配置！\n";
} else {
    echo "利息宝产品配置:\n";
    foreach ($products as $product) {
        $title = isset($product['title']) ? $product['title'] : (isset($product['name']) ? $product['name'] : '未知');
        $rate = isset($product['rate']) ? $product['rate'] : (isset($product['bili']) ? $product['bili'] : 0);
        $day = isset($product['day']) ? $product['day'] : '未知';
        $status = isset($product['status']) ? $product['status'] : '未知';
        echo "  产品ID: {$product['id']}, 名称: $title, 利率: {$rate}%, 天数: $day, 状态: $status\n";
    }
}

// 6. 生成具体的问题分析
echo "\n=== 6. 问题分析与建议 ===\n";

if ($investment_info['total_investments'] > 0) {
    if ($recent_income == 0) {
        echo "🔍 问题分析: 有活跃投资但最近3天没有任何收益记录\n";
        echo "🔧 可能原因:\n";
        echo "   1. 定时任务执行了但计算逻辑有问题\n";
        echo "   2. 数据库写入失败\n";
        echo "   3. 用户投资时间还未满1天\n";
        echo "   4. 产品利率配置为0\n";
    } elseif ($yesterday_income == 0 && $today_income == 0) {
        echo "🔍 问题分析: 有活跃投资但昨天今天都没有收益记录\n";
        echo "🔧 建议立即运行补发脚本\n";
    } else {
        echo "✅ 系统正常: 有活跃投资且有收益记录\n";
    }
} else {
    echo "ℹ️  当前没有活跃投资，这可能是正常情况\n";
}

echo "\n=== 7. 下一步操作建议 ===\n";
echo "1. 如果发现问题用户，建议运行: auto_fix_lixibao.php\n";
echo "2. 检查定时任务的实际执行结果页面内容\n";
echo "3. 查看服务器错误日志\n";
echo "4. 手动触发一次收益计算并观察结果\n";

echo "\n=== 诊断完成 ===\n";
?> 