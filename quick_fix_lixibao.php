<?php
/**
 * 快速修复利息宝收益问题
 * 针对用户反映的"投资15天，每天应该要给用户产生收益，但系统只会算1天的收益就停止了"的问题
 * 
 * 直接运行：php quick_fix_lixibao.php
 * 浏览器访问：http://您的域名/quick_fix_lixibao.php?days=7
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

require __DIR__ . '/thinkphp/base.php';

use think\Db;

function getSn($prefix = '') {
    return $prefix . date('ymdHis') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
}

echo "<h1>利息宝收益快速修复</h1>";

try {
    // 获取要修复的天数，默认7天
    $fix_days = isset($_GET['days']) ? intval($_GET['days']) : 7;
    
    echo "<p>开始修复最近 $fix_days 天的利息宝收益问题...</p>";
    
    $current_time = time();
    $fixed_count = 0;
    $total_amount = 0;
    
    // 遍历最近几天
    for ($i = $fix_days - 1; $i >= 0; $i--) {
        $check_date = date('Y-m-d', strtotime("-$i days"));
        $day_start = strtotime($check_date . ' 00:00:00');
        $day_end = strtotime($check_date . ' 23:59:59');
        
        echo "<h3>检查日期: $check_date</h3>";
        
        // 如果是未来日期，跳过
        if ($day_start > $current_time) {
            echo "<p>跳过未来日期</p>";
            continue;
        }
        
        // 查找在该日期有活跃投资但没有收益记录的用户
        $problem_users = Db::query("
            SELECT DISTINCT u.id, u.username
            FROM xy_users u
            INNER JOIN xy_lixibao xl ON u.id = xl.uid
            WHERE xl.type = 1 
            AND xl.is_qu = 0
            AND xl.addtime <= ?
            AND xl.endtime > ?
            AND NOT EXISTS (
                SELECT 1 FROM xy_balance_log bl 
                WHERE bl.uid = u.id 
                AND bl.type = 23 
                AND bl.status = 1
                AND bl.addtime >= ? 
                AND bl.addtime <= ?
            )
        ", [$day_end, $day_start, $day_start, $day_end]);
        
        if (empty($problem_users)) {
            echo "<p>✓ 该日期无需修复</p>";
            continue;
        }
        
        echo "<p>发现 " . count($problem_users) . " 个用户需要补发收益:</p>";
        echo "<ul>";
        
        foreach ($problem_users as $user) {
            $uid = $user['id'];
            $username = $user['username'];
            
            // 获取该用户在该日期的有效投资
            $investments = Db::name('xy_lixibao')
                ->alias('xl')
                ->leftJoin('xy_lixibao_list xll', 'xll.id=xl.sid')
                ->where('xl.uid', $uid)
                ->where('xl.type', 1)
                ->where('xl.is_qu', 0)
                ->where('xl.addtime', '<=', $day_end)
                ->where('xl.endtime', '>', $day_start)
                ->field('xl.*, xll.name as product_name, xll.bili, xll.day')
                ->select();
            
            if (empty($investments)) {
                continue;
            }
            
            // 按产品计算收益
            $total_income = 0;
            $income_details = [];
            
            foreach ($investments as $inv) {
                $daily_income = $inv['num'] * ($inv['bili'] ?: 0.05);
                $total_income += $daily_income;
                
                $income_details[] = [
                    'product_id' => $inv['sid'],
                    'product_name' => $inv['product_name'] ?: 'Type D/E',
                    'amount' => $inv['num'],
                    'rate' => $inv['bili'] ?: 0.05,
                    'income' => $daily_income
                ];
            }
            
            if ($total_income > 0) {
                try {
                    // 开始事务
                    Db::startTrans();
                    
                    // 更新用户余额
                    Db::name('xy_users')->where('id', $uid)->setInc('balance', $total_income);
                    
                    // 添加收益记录
                    foreach ($income_details as $detail) {
                        Db::name('xy_lixibao')->insert([
                            'uid' => $uid,
                            'num' => $detail['income'],
                            'addtime' => $day_start + 43200, // 中午12点
                            'type' => 3,
                            'status' => 1,
                            'yuji_num' => $detail['income'],
                            'real_num' => $detail['income'],
                            'is_sy' => 1,
                            'sid' => $detail['product_id'],
                            'shouxu' => 0,
                            'bili' => $detail['rate'],
                            'day' => 1,
                            'update_time' => $day_start + 43200,
                        ]);
                    }
                    
                    // 添加余额变动记录
                    Db::name('xy_balance_log')->insert([
                        'uid' => $uid,
                        'oid' => getSn('FIX'),
                        'num' => $total_income,
                        'type' => 23,
                        'status' => 1,
                        'addtime' => $day_start + 43200
                    ]);
                    
                    Db::commit();
                    
                    echo "<li>✓ $username: 补发收益 $total_income 元";
                    
                    // 显示详细信息
                    $details = [];
                    foreach ($income_details as $detail) {
                        $details[] = "{$detail['product_name']}({$detail['amount']}元*" . ($detail['rate']*100) . "%={$detail['income']}元)";
                    }
                    echo " [" . implode(', ', $details) . "]</li>";
                    
                    $fixed_count++;
                    $total_amount += $total_income;
                    
                } catch (Exception $e) {
                    Db::rollback();
                    echo "<li>✗ $username: 修复失败 - " . $e->getMessage() . "</li>";
                }
            }
        }
        
        echo "</ul>";
    }
    
    echo "<div style='background-color:#d4edda; padding:20px; margin:20px 0; border:1px solid #c3e6cb; border-radius:5px;'>";
    echo "<h2>修复完成！</h2>";
    echo "<p><strong>修复用户数:</strong> $fixed_count</p>";
    echo "<p><strong>补发总金额:</strong> $total_amount 元</p>";
    echo "</div>";
    
    if ($fixed_count > 0) {
        echo "<div style='background-color:#d1ecf1; padding:15px; margin:10px 0; border:1px solid #bee5eb; border-radius:5px;'>";
        echo "<h3>修复说明:</h3>";
        echo "<p>✓ 已为 $fixed_count 个用户补发了缺失的每日收益</p>";
        echo "<p>✓ 收益按照各产品的投资金额和日利率正确计算</p>";
        echo "<p>✓ 所有收益都已加入用户余额并记录在案</p>";
        echo "</div>";
    }
    
    echo "<div style='background-color:#fff3cd; padding:15px; margin:10px 0; border:1px solid #ffeaa7; border-radius:5px;'>";
    echo "<h3>问题解决:</h3>";
    echo "<p><strong>问题:</strong> 投资15天的产品应该每天产生收益，但系统只算了1天就停止</p>";
    echo "<p><strong>原因:</strong> 定时任务可能中断或逻辑错误导致收益计算停止</p>";
    echo "<p><strong>解决:</strong> 已补发所有缺失的每日收益，确保投资期间每天都有正确收益</p>";
    echo "</div>";
    
    echo "<div style='background-color:#f8f9fa; padding:15px; margin:10px 0; border:1px solid #dee2e6; border-radius:5px;'>";
    echo "<h3>预防措施:</h3>";
    echo "<p>为防止此问题再次发生，建议:</p>";
    echo "<ol>";
    echo "<li>设置定时任务每天自动计算收益</li>";
    echo "<li>定期运行检查脚本确保收益正常发放</li>";
    echo "<li>监控日志文件及时发现问题</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>相关链接:</h3>";
    echo "<p><a href='complete_lixibao_fix.php?mode=daily'>每日收益计算</a></p>";
    echo "<p><a href='debug_lixibao_issue.php'>详细诊断工具</a></p>";
    echo "<p><a href='/index/crontab/lixibao_js'>系统原始收益计算</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background-color:#f8d7da; padding:15px; margin:10px 0; border:1px solid #f5c6cb; border-radius:5px;'>";
    echo "<h3>执行错误:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p style='color:#666;'>修复完成时间: " . date('Y-m-d H:i:s') . "</p>";
?> 