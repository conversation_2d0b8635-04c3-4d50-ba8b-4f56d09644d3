<?php

use think\facade\Route;

// 加密货币充值页面路由 - 完整路径匹配
Route::rule('index/cryptorecharge/recharge', 'index/CryptoRecharge/recharge');
// 简化路径匹配
Route::rule('cryptorecharge/recharge', 'index/CryptoRecharge/recharge');
Route::rule('cryptorecharge', 'index/CryptoRecharge/recharge');
Route::rule('crypto', 'index/CryptoRecharge/recharge');

// 原始充值路由
Route::rule('index/ctrl/recharge', 'index/Ctrl/recharge');
Route::rule('ctrl/recharge', 'index/Ctrl/recharge');

// 简化版充值路由 - 指向现有的 recharge 方法
Route::rule('index/ctrl/recharge_simple', 'index/Ctrl/recharge');
Route::rule('ctrl/recharge_simple', 'index/Ctrl/recharge');

// 利息宝相关路由
Route::rule('index/ctrl/lixibao', 'index/Ctrl/lixibao');
Route::rule('ctrl/lixibao', 'index/Ctrl/lixibao');
Route::rule('index/ctrl/lixibao_ru', 'index/Ctrl/lixibao_ru');
Route::rule('index/ctrl/lixibao_chu', 'index/Ctrl/lixibao_chu');
Route::rule('index/ctrl/lixibao_income', 'index/Ctrl/lixibao_income');
Route::rule('index/ctrl/deposityj', 'index/Ctrl/deposityj');

// 团队相关路由 - 支持语言参数
Route::rule('index/ctrl/junior', 'index/Ctrl/junior');
Route::rule('ctrl/junior', 'index/Ctrl/junior');

// 利息宝三点菜单API路由
Route::rule('index/ctrl/get_funds_detail', 'index/Ctrl/get_funds_detail');
Route::rule('index/ctrl/get_report_detail', 'index/Ctrl/get_report_detail');
Route::rule('index/ctrl/get_rules_detail', 'index/Ctrl/get_rules_detail');

// **新增：定时任务和补发收益路由**
Route::rule('index/crontab/lixibao_js', 'index/Crontab/lixibao_js');
Route::rule('index/crontab/lxb_jiesuan', 'index/Crontab/lxb_jiesuan');
Route::rule('index/crontab/fix_missing_income', 'index/Crontab/fix_missing_income');
Route::rule('crontab/lixibao_js', 'index/Crontab/lixibao_js');
Route::rule('crontab/lxb_jiesuan', 'index/Crontab/lxb_jiesuan');
Route::rule('crontab/fix_missing_income', 'index/Crontab/fix_missing_income');

// 加密货币支付相关API路由
Route::rule('index/crypto/get_payment_details', 'index/CryptoRecharge/get_payment_details');
Route::rule('crypto/get_payment_details', 'index/CryptoRecharge/get_payment_details');
Route::rule('index/crypto/submit_recharge', 'index/CryptoRecharge/submit_recharge');
Route::rule('crypto/submit_recharge', 'index/CryptoRecharge/submit_recharge');
Route::rule('index/ctrl/get_crypto_payment_details', 'index/Ctrl/get_crypto_payment_details');

// 特殊处理 - 直接访问 /cryptorecharge/recharge.html
Route::rule('cryptorecharge/recharge.html', 'index/CryptoRecharge/recharge');

// 添加显式的充值路由，确保用户可以直接访问
Route::rule('/', 'index/Ctrl/recharge'); // 首页直接访问充值页面
Route::rule('/recharge', 'index/Ctrl/recharge'); // 简短URL直接访问充值页面

// 查看此控制器文件是否存在的测试路由
Route::rule('test/crypto', function() {
    if (class_exists('app\\index\\controller\\CryptoRecharge')) {
        return '控制器存在！';
    } else {
        return '控制器不存在！请检查文件路径和命名空间。';
    }
});

// 直接输出完整路径用于调试
Route::rule('test/path', function() {
    return '当前路径: ' . __DIR__ . '<br>应用路径: ' . \think\facade\App::getAppPath() .
           '<br>控制器路径: ' . \think\facade\App::getAppPath() . 'index/controller/';
});

// 添加诊断路由 - 查看文件是否存在
Route::rule('test/file', function() {
    $controller_file = \think\facade\App::getAppPath() . 'index/controller/CryptoRecharge.php';
    $template_file = \think\facade\App::getAppPath() . 'index/view/cryptorecharge/recharge.html';
    $error_controller = \think\facade\App::getAppPath() . 'index/controller/Error.php';

    return '控制器文件: ' . ($controller_file && file_exists($controller_file) ? '存在' : '不存在') .
           '<br>模板文件: ' . ($template_file && file_exists($template_file) ? '存在' : '不存在') .
           '<br>错误控制器: ' . ($error_controller && file_exists($error_controller) ? '存在' : '不存在');
});

// 404处理 - 当所有路由都未匹配时执行
Route::miss(function() {
    return redirect('/index/ctrl/recharge');
});

// 添加后台测试路由
Route::rule('admin/test/order_info', 'admin/Index/order_info');

// 添加测试控制器路由
Route::rule('index/test/db', 'index/Test/db_test');
Route::rule('index/test/log', 'index/Test/log_test');
Route::rule('index/test/api', 'index/Test/api_test');

// 添加测试路由来检查xy_bank表数据
Route::rule('test/bank', function() {
    try {
        $wallets = \think\Db::name('xy_bank')->where('status', 1)->select();
        $fields = [];

        if (count($wallets) > 0) {
            $fields = array_keys($wallets[0]);
        }

        return json([
            'success' => true,
            'count' => count($wallets),
            'fields' => $fields,
            'data' => $wallets
        ]);
    } catch (\Exception $e) {
        return json([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
});

// VIP等级管理路由映射 - 解决下划线URL到驼峰控制器的映射问题
Route::rule('admin/vip_level_switch/index', 'admin/VipLevelSwitch/index');
Route::rule('admin/vip_level_switch/toggleSwitch', 'admin/VipLevelSwitch/toggleSwitch');
Route::rule('admin/vip_level_switch/toggle_switch', 'admin/VipLevelSwitch/toggleSwitch');
Route::rule('admin/vip_level_switch/batchToggle', 'admin/VipLevelSwitch/batchToggle');
Route::rule('admin/vip_level_switch/batch_toggle', 'admin/VipLevelSwitch/batchToggle');
Route::rule('admin/vip_level_switch/getSwitchStatus', 'admin/VipLevelSwitch/getSwitchStatus');
Route::rule('admin/vip_level_switch/get_switch_status', 'admin/VipLevelSwitch/getSwitchStatus');
Route::rule('admin/vip_level_switch/resetAll', 'admin/VipLevelSwitch/resetAll');
Route::rule('admin/vip_level_switch/reset_all', 'admin/VipLevelSwitch/resetAll');