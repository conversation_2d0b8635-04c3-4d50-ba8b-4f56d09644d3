<?php

use think\facade\Route;

// 充值记录路由 - 新增（这是我们修复的核心功能）
Route::rule('index/ctrl/recharge_admin', 'index/Ctrl/recharge_admin');
Route::rule('ctrl/recharge_admin', 'index/Ctrl/recharge_admin');

// 原始充值路由
Route::rule('index/ctrl/recharge', 'index/Ctrl/recharge');
Route::rule('ctrl/recharge', 'index/Ctrl/recharge');

// 利息宝相关路由
Route::rule('index/ctrl/lixibao', 'index/Ctrl/lixibao');
Route::rule('ctrl/lixibao', 'index/Ctrl/lixibao');
Route::rule('index/ctrl/lixibao_ru', 'index/Ctrl/lixibao_ru');
Route::rule('index/ctrl/lixibao_chu', 'index/Ctrl/lixibao_chu');
Route::rule('index/ctrl/lixibao_income', 'index/Ctrl/lixibao_income');
Route::rule('index/ctrl/deposityj', 'index/Ctrl/deposityj');

// 团队相关路由
Route::rule('index/ctrl/junior', 'index/Ctrl/junior');
Route::rule('ctrl/junior', 'index/Ctrl/junior');

// 利息宝三点菜单API路由
Route::rule('index/ctrl/get_funds_detail', 'index/Ctrl/get_funds_detail');
Route::rule('index/ctrl/get_report_detail', 'index/Ctrl/get_report_detail');
Route::rule('index/ctrl/get_rules_detail', 'index/Ctrl/get_rules_detail');

// 加密货币支付相关API路由
Route::rule('index/crypto/get_payment_details', 'index/CryptoRecharge/get_payment_details');
Route::rule('crypto/get_payment_details', 'index/CryptoRecharge/get_payment_details');
Route::rule('index/crypto/submit_recharge', 'index/CryptoRecharge/submit_recharge');
Route::rule('crypto/submit_recharge', 'index/CryptoRecharge/submit_recharge');
Route::rule('index/ctrl/get_crypto_payment_details', 'index/Ctrl/get_crypto_payment_details');

// VIP等级管理路由映射
Route::rule('admin/vip_level_switch/index', 'admin/VipLevelSwitch/index');
Route::rule('admin/vip_level_switch/toggleSwitch', 'admin/VipLevelSwitch/toggleSwitch');
Route::rule('admin/vip_level_switch/toggle_switch', 'admin/VipLevelSwitch/toggleSwitch');

// 注意：移除了可能导致循环重定向的以下配置：
// 1. Route::rule('/', 'index/Ctrl/recharge'); // 首页重定向
// 2. Route::miss() 404处理
// 3. 其他可能冲突的路由规则 