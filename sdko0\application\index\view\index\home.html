<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="stylesheet" href="/p_static1/css/base.css">
		<title>{$Think.lang.home}</title>
		<link href="/static_new6/css/apps.css" rel="stylesheet">
		<script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
		<script charset="utf-8" src="/static_new/js/common.js"></script>
	
		<style type="text/css" title="fading circle style">
	a{
		color:#000;
	}
			.circle-color-23 > div::before {
				background-color: #ccc;
			}
			@media screen and (max-width:360px) {
	
			}
	
			.announcement-task .a-t-items{
				width: 45%!important;
				padding: 10px;
			}
			.a-t-t-3 > img[data-v-eebac136] {
				 width: 100%;
				margin-top: .16rem;
				height: 100%;
	
			}
			.a-t-text[data-v-eebac136]{
				position: relative;
			}
			.a-t-text[data-v-eebac136]{
				left: 0;
			}
			.a-t-text[data-v-eebac136] {
				top: .1rem;
			}
	
			.aui li[data-v-eebac136]{
				width: 20%;
			}
	
		</style>
		{if !config('shop_status')}
		<style>
			.aui li[data-v-eebac136]{
				width: 25%;
			}
		</style>
		{/if}
		<style>
			body {
				padding-top: 0 !important;
				padding-bottom: 5rem;
			}
			/* 消息展示 */
			.p_top {
				box-sizing: border-box;
				display: flex;
				align-items: center;
				height: 1.5rem;
				background-color: rgba(247, 237, 237, 1);
			}
			.p_top-img {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 2.8rem;
				height: 100%;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(191, 130, 130, 1);
				background: url(/p_static1/img/index_img-1.svg) no-repeat;
				background-size: cover;
			}
			.p_top marquee {
				flex: 1;
				display: flex;
				align-items: center;
				height: 100%;
				font-size: 0.6rem;
				line-height: 0.6rem;
				color: rgba(191, 130, 130, 1);
			}
			.p_top-arrow {
				margin: 0 0.75rem;
				width: 1.05rem;
				height: 1.05rem;
			}
			/* 头部(头像和下载按钮) */
			.p_header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin: 0.25rem 0.75rem 0;
			}
			.p_header-img {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 1.65rem;
				height: 1.65rem;
				border-radius: 50%;
			}
			.p_header-img img {
				width: 100%;
				height: 100%;
				border-radius: 50%;
			}
			/* 资产信息 */
			.p_asset {
				box-sizing: border-box;
				position: relative;
				margin: 0.825rem 0.75rem 0;
				padding: 1rem 0 0 0;
				height: 9.75rem;
				background: url(/p_static1/img/index_img-2.png) no-repeat;
				background-size: 100% 100%;
			}
			.p_asset-icon {
				position: absolute;
				top: 1rem;
				right: 1.25rem;
				width: 0.75rem;
				height: 0.6rem;
			}
			.p_asset-title {
				padding: 0 1.25rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(255, 255, 255, 0.8);
			}
			.p_asset-num {
				margin-top: 0.5rem;
				padding: 0 1.25rem;
				font-size: 1.4rem;
				line-height: 1.4rem;
				font-weight: bold;
				color: rgba(255, 255, 255, 1);
			}
			.p_asset-item-wrapper {
				display: flex;
				justify-content: space-between;
				margin-top: 1.05rem;
				padding: 0.75rem 1.25rem 0;
				border-top: 0.025rem solid rgba(101, 106, 150, 1);;
			}
			.p_asset-item {
				
			}
			.p_asset-item-title {
				font-size: 0.6rem;
				line-height: 0.7rem;
				color: rgba(255, 255, 255, 0.8);
			}
			.p_asset-item-num {
				margin-top: 0.5rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(255, 255, 255, 1);
			}
			/* 导航标签 */
			.p_nav {
				display: flex;
				flex-wrap: wrap;
				margin-top: 0.95rem;
			}
			.p_nav-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 25%;
			}
			.p_nav-item-img {
				width: 2.5rem;
				height: 2.5rem;
			}
			.p_nav-item-title {
				margin-top: 0.5rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(83, 87, 120, 1);
				text-align: center;
			}
			/* 任务栏 */
			.p_mission {
				margin-top: 1.05rem;
				padding-top: 0.75rem;
				border-top: 0.25rem solid rgba(245, 245, 247, 1);
			}
			.p_mission-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.p_mission-more {
				display: flex;
				align-items: center;
				margin-right: 0.75rem;
			}
			.p_mission-more span {
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_mission-more img {
				margin-left: 0.5rem;
				width: 1.05rem;
				height: 1.05rem;
			}
			.p_mission-title {
				display: flex;
				align-items: center;
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: bold;
				color: rgba(36, 44, 107, 1);
			}
			.p_mission-title::before {
				display: block;
				content: '';
				margin-right: 0.6rem;
				width: 0.15rem;
				height: 0.8rem;
				background-color: rgba(36, 44, 107, 1);
			}
			
			/* 更多弹窗 */
			.p_mission-more-popup-wrapper {
				display: none;
				position: fixed;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, .3);
				z-index: 1000;
			}
			.p_mission-more-popup {
				position: absolute;
				left: 0;
				bottom: 0;
				padding-bottom: 1rem;
				background-color: #fff;
				border-radius: 0.5rem 0.5rem 0 0;
			}
			.p_mission-more-header {
				display: flex;
				align-items: center;
				padding: 0 0.75rem;
				height: 3rem;
				background: url(/p_static1/img/index_more-img1.png) no-repeat;
				background-size: 100% 100%;
			}
			.p_mission-more-header-text {
				display: flex;
				align-items: center;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: bold;
				color: rgba(248, 217, 193, 1);
			}
			.p_mission-more-header-text::before {
				display: block;
				content: '';
				margin-right: 0.6rem;
				width: 0.15rem;
				height: 0.8rem;
				background-color: rgba(248, 217, 193, 1);
			}
			.p_mission-more-list {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				padding: 0.9rem 0.75rem 0;
			}
			.p_mission-more-item {
				position: relative;
				box-sizing: border-box;
				width: 49%;
				height: 4.15rem;
				padding: 0.6rem 0 0 2.625rem;
				margin-bottom: 0.5rem;
				background-repeat: no-repeat;
				background-size: 100% 100%;
			}
			.p_mission-more-item.v1 {
				background-image: url(/p_static1/img/index_mission-bg-1.png);
			}
			.p_mission-more-item.v2 {
				background-image: url(/p_static1/img/index_mission-bg-2.png);
			}
			.p_mission-more-item.v3 {
				background-image: url(/p_static1/img/index_mission-bg-3.png);
			}
			.p_mission-more-item.v4 {
				background-image: url(/p_static1/img/index_mission-bg-4.png);
			}
			.p_mission-more-item.v5 {
				background-image: url(/p_static1/img/index_mission-bg-5.png);
			}
			.p_mission-more-item.v6 {
				background-image: url(/p_static1/img/index_mission-bg-6.png);
			}
			.p_mission-more-item-title {
				font-size: 0.6rem;
				line-height: 0.6rem;
				font-weight: bold;
				color: rgba(36, 44, 107, 1);
			}
			.p_mission-more-item-subtitle {
				margin-top: 0.5rem;
				font-size: 0.6rem;
				line-height: 0.8rem;
			}
			.v1 .p_mission-more-item-subtitle{
				color: rgba(103, 107, 138, 1);
			}
			.v2 .p_mission-more-item-subtitle{
				color: rgba(105, 146, 194, 1);
			}
			.v3 .p_mission-more-item-subtitle{
				color: rgba(86, 126, 231, 1);
			}
			.v4 .p_mission-more-item-subtitle{
				color: rgba(102, 181, 56, 1);
			}
			.v5 .p_mission-more-item-subtitle{
				color: rgba(62, 192, 104, 1);
			}
			.v6 .p_mission-more-item-subtitle{
				color: rgba(222, 133, 31, 1);
			}
					/* 首页弹窗相关样式 */
		.p_pop-up-wrapper {
			position: fixed;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, .3);
			z-index: 1000;
		}
		.p_pop-up {
			box-sizing: border-box;
			position: absolute;
			top: 20%;
			left: 50%;
			width: 80%;
			height: 55vh;
			background-color: #fff;
			border-radius: 0.5rem;
			transform: translate(-50%, 0);
		}
		.p_pop-up-title {
			position: relative;
			box-sizing: border-box;
			padding: 0.65rem 6.1rem 0 0.825rem;
			width: 100%;
			height: 3.25rem;
			font-size: 0.8rem;
			font-weight: bold;
			line-height: 1.05rem;
			color: rgba(173, 94, 33, 1);
			background: url(/p_static1/img/index_bg-5.svg) no-repeat;
			background-size: cover;
		}
		.p_pop-up-title-img {
			position: absolute;
			top: -1.325rem;
			right: 0.35rem;
			width: 5.4rem;
			height: 4.275rem;
			background: url(/p_static1/img/index_alert-img1.png) no-repeat;
			background-size: 100% 100%;
		}
		.p_pop-up-content {
			padding: 0.8rem 0.625rem 0;
			overflow: auto;
		}
		.p_pop-up-text {
			height: 9.5rem;
			font-size: 0.7rem;
			line-height: 1.05rem;
			color: rgba(173, 94, 33, 1);
			text-indent: 1.4rem;
			text-align: justify;
		}
		.p_pop-up-btn {
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 1.45rem 0.625rem 0;
			height: 2.5rem;
			font-size: 0.8rem;
			line-height: 0.8rem;
			color: rgba(248, 217, 193, 1);
			background-color: rgba(36, 44, 107, 1);
			border-radius: 0.5rem;
		}
		/* 关于我们 */
		.p_about-us {
			margin-top: 1.75rem;
			padding-top: 0.75rem;
		}
			.p_about-us-title {
				display: flex;
				align-items: center;
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: bold;
				color: rgba(36, 44, 107, 1);
			}
			.p_about-us-title::before {
				display: block;
				content: '';
				margin-right: 0.6rem;
				width: 0.15rem;
				height: 0.8rem;
				background-color: rgba(36, 44, 107, 1);
			}
			.p_about-us-list {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				padding: 0.9rem 0.75rem 0;
			}
			.p_about-us-item {
				box-sizing: border-box;
				width: 49%;
				height: 5rem;
				padding: 0.775rem 0.5rem 0;
				margin-bottom: 0.675rem;
				background-repeat: no-repeat;
				background-size: 100% 100%;
			}
			.info1 {
				background-image: url(/p_static1/img/index_bg-1.svg);
			}
			.info2 {
				background-image: url(/p_static1/img/index_bg-2.svg);
			}
			.info3 {
				background-image: url(/p_static1/img/index_bg-3.svg);
			}
			.info4 {
				background-image: url(/p_static1/img/index_bg-4.svg);
			}
			.p_about-us-item-text {
				font-size: 0.7rem;
				line-height: 0.8rem;
				font-weight: bold;
			}
			.info1 .p_about-us-item-text{
				color: rgba(173, 133, 31, 1);
			}
			.info2 .p_about-us-item-text{
				color: rgba(81, 116, 140, 1);
			}
			.info3 .p_about-us-item-text{
				color: rgba(124, 81, 143, 1);
			}
			.info4 .p_about-us-item-text{
				color: rgba(171, 84, 84, 1);
			}
			.p_about-us-item-btn {
				box-sizing: border-box;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-top: 0.4rem;
				width: 5rem;
				height: 1.275rem;
				font-size: 0.6rem;
				line-height: 0.6rem;
				padding: 0.2rem;
				border-radius: 1.125rem;
			}
			.info1 .p_about-us-item-btn {
				background-color: rgba(227, 208, 161, 1);
				color: rgba(163, 130, 46, 1);
			}
			.info2 .p_about-us-item-btn {
				background-color: rgba(175, 202, 222, 1);
				color: rgba(81, 116, 140, 1);
			}
			.info3 .p_about-us-item-btn {
				background-color: rgba(204, 173, 217, 1);
				color: rgba(124, 81, 143, 1);
			}
			.info4 .p_about-us-item-btn {
				background-color: rgba(227, 184, 184, 1);
				color: rgba(171, 84, 84, 1);
			}
			/* 底部导航栏 */
			.p_footer {
				position: fixed;
				left: 0;
				bottom: 0;
				box-sizing: border-box;
				display: flex;
				width: 100%;
				height: 3rem;
				background-color: #fff;
				border: 0.05rem solid rgba(237, 240, 255, 1);
				z-index: 99;
			}
			.p_footer-item {
				position: relative;
				box-sizing: border-box;
				padding: 0.625rem 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 20%;
				height: 100%;
			}
			.p_footer-item-img {
				width: 0.85rem;
				height: 0.85rem;
			}
			.p_footer-item-text {
				margin-top: 0.4rem;
				font-size: 0.5rem;
				line-height: 0.5rem;
				color: rgba(200, 203, 227, 1);
			}
			.p_footer-item-text.active {
				color: rgba(36, 44, 107, 1);
			}
			.p_footer-middle {
				position: absolute;
				left: 50%;
				top: 0;
				width: 4.3rem;
				height: 4.3rem;
				background: url(/p_static1/img/footer_img-middle.svg) no-repeat;
				background-size: 100% 100%;
				transform: translate(-50%, -40%);
			}
			.p_footer-middle-text {
				margin-top: 2.5rem;
				font-size: 0.5rem;
				line-height: 0.5rem;
				text-align: center;
				color: rgba(248, 217, 193, 1);
				transform: scale(.8);
			}
		</style>
	</head>
	<body>
		<!-- 消息展示 -->
		<div class="p_top">
			<div class="p_top-img">{:lang('information')}</div>
			<marquee direction="left">{:lang($gundong)}</marquee>
			<img class="p_top-arrow" src="/p_static1/img/arrowright-circel-pink.svg" >
		</div>
		
		<!-- 头部(头像和下载按钮) -->
		<div class="p_header">
			<a href="#" class="p_header-img">
				<img src="/p_static1/img/avatar.png">
			</a>
			<a href="/app/" class="p_header-img" style="width: 4.2rem;">
			    <span style="color: rgb(36,44,107);">APP</span>
				<img src="/p_static1/img/arrowdown-circle.svg">
			</a>
		</div>
		
		<!-- 资产信息 -->
		<div class="p_asset">
			<img class="p_asset-icon" src="/p_static1/img/hat.svg">
			<div class="p_asset-title">{:lang('Account_balance')}</div>
			<div class="p_asset-num">{$users.balance} USDT</div>
			<div class="p_asset-item-wrapper">
				<div class="p_asset-item">
					<div class="p_asset-item-title">{:lang('Yesterdayearnings')}</div>
					<div class="p_asset-item-num">{$yes_user_yongjin} USDT</div>
				</div>
				<div class="p_asset-item" style="margin-left:10%">
					<div class="p_asset-item-title">{:lang('commission')}</div>
					<div class="p_asset-item-num">{$user_yongjin} USDT</div>
				</div>
				<div class="p_asset-item" style="margin-left:10%"> 
					<div class="p_asset-item-title">{:lang('Commissiongrabbedtoday')}</div>
					<div class="p_asset-item-num">{$tod_user_yongjin} USDT</div>
				</div>
			</div>
		</div>
		
		<!-- 导航标签 -->
		<div class="p_nav">
			{if config('shop_status')}
			<a class="p_nav-item" href="/index/ctrl/lixibao">
				<img src="/p_static1/img/index_tab-1.svg" class="p_nav-item-img">
				<div class="p_nav-item-title">{$Think.lang.Quickupgrade}</div>
			</a>
			{/if}
			<a class="p_nav-item" href="/index/ctrl/recharge">
				<img src="/p_static1/img/index_tab-2.svg" class="p_nav-item-img">
				<div class="p_nav-item-title">{:lang('Recharge')}</div>
			</a>
			<a class="p_nav-item" href="/index/ctrl/deposit">
				<img src="/p_static1/img/index_tab-3.svg" class="p_nav-item-img">
				<div class="p_nav-item-title">{:lang('Withdraw')}</div>
			</a>
			<a class="p_nav-item" href="/index/my/invite">
				<img src="/p_static1/img/index_tab-4.svg" class="p_nav-item-img">
				<div class="p_nav-item-title">{$Think.lang.Shippingaddress}</div>
			</a>
		</div>
		
		<!-- 任务栏 -->
		<div class="p_mission">
			<div class="p_mission-header">
				<div class="p_mission-title">{$Think.lang.Missionhall}</div>
				<div class="p_mission-more" id="more-btn">
					<span>{:lang('More')}</span>
					<img src="/p_static1/img/arrowright-circel-blue.svg" >
				</div>
			</div>
			
    		<div style="margin-top:0.5rem;">
              <div data-v-eebac136="" class="announcement-task flexS" style="">
                {foreach $cate as $key=>$vo}
                <div style="margin-top:.3rem" data-v-eebac136="" data-cid="{$vo.id}" class="a-t-items {$info['level'] >= $vo['level'] ? '' : 'not'}">
                   <div data-v-eebac136="" class="a-t-bg"><img data-v-eebac136="" src="/static_new/img/default.png" alt=""></div>
                  <div data-v-eebac136="" class="a-t-title">
                    <img data-v-eebac136="" src="{$vo.pic}" alt="">
                  </div>
                  <div data-v-eebac136="" class="a-t-text">
                    <div data-v-eebac136="" class="a-t-t-1" style="font-size:0.3rem">
                      {:lang('佣金比')}：
                      {switch $vo.id}
                      {case 1}2.0{/case}
                      {case 2}2.0{/case}
                      {case 3}2.2{/case}
                      {case 4}2.4{/case}
                      {case 5}2.6{/case}
                      {case 6}2.8{/case}
                      {default}2.4{/default}
                      {/switch}%
                    </div>
                    <div data-v-eebac136="" class="a-t-t-2" style="font-size:0.2rem">
                      {:lang('匹配区间')}：{$vo.num_min}$
                    </div>
                    <div data-v-eebac136="" class="a-t-t-3">
                      <img data-v-eebac136="" src="{$vo.pic2}" alt="" style="height:4rem">
                      {if($vo['level'] > $info['level'])}
                      <div style="width:100%;height:4.2rem;background:rgb(0, 0, 0, 0.45);position:relative;top:-4rem;display:flex;align-items:center;justify-content:center;flex-direction: column;">
                        <img src="/p_static1/img/pwd.png" style="width:15%;height:30%">
                        <p style="color:#fff;font-size: 0.532rem;margin-top: 0.053333rem;">
                          {:lang('Not_unlocked')}
                        </p>
                      </div>
                      {/if}
                    </div>
                  </div>
                </div>
                {/foreach}
              </div>
            </div>
			
			<!-- 更多弹窗 -->
			<div class="p_mission-more-popup-wrapper" id="more-wrapper">
				<div class="p_mission-more-popup" id="more-container">
					<div class="p_mission-more-header">
						<div class="p_mission-more-header-text">{:lang('More')}</div>
					</div>
					<div class="p_mission-more-list">
						{foreach $cate as $key=>$vo}
						<div class="v{$vo.id} p_mission-more-item">
							<div class="p_mission-more-item-title">{:lang('佣金比')}：
								{switch $vo.id}
								{case 1}2.0{/case}
								{case 2}2.0{/case}
								{case 3}2.2{/case}
								{case 4}2.4{/case}
								{case 5}2.6{/case}
								{case 6}2.8{/case}
								{default}2.4{/default}
								{/switch}%
							</div>
							<div class="p_mission-more-item-subtitle">
								<div>{:lang('匹配区间')}</div>
								<div>{$vo.num_min}$</div>
							</div>
						</div>
						{/foreach}
					</div>
				</div>
			</div>
		</div>

		<!-- 关于我们 -->
		<div class="p_about-us">
			<div class="p_about-us-title">{:lang('More')}</div>
			<div class="p_about-us-list">
				<a class="p_about-us-item info1" href="/index/my/detail.html?id=2">
					<div class="p_about-us-item-text">{:lang('js')}</div>
					<div class="p_about-us-item-btn">{:lang('understanding')}</div>
				</a>
				<a class="p_about-us-item info2" href="/index/my/detail.html?id=3">
					<div class="p_about-us-item-text">{:lang('gz')}</div>
					<div class="p_about-us-item-btn">{:lang('understanding')}</div>
				</a>
				<a class="p_about-us-item info3" href="/index/my/detail.html?id=4">
					<div class="p_about-us-item-text">{:lang('qy')}</div>
					<div class="p_about-us-item-btn">{:lang('understanding')}</div>
				</a>
				<a class="p_about-us-item info4" href="/index/my/detail.html?id=12">
					<div class="p_about-us-item-text">{:lang('hz')}</div>
					<div class="p_about-us-item-btn">{:lang('understanding')}</div>
				</a>
			</div>
		</div>
		
		<!-- 首页弹窗 -->
		<div class="p_pop-up-wrapper" id="pop-up">
			<div class="p_pop-up">
				<div class="p_pop-up-title">
					<div class="p_pop-up-title-img"></div>
					<div>{:lang('Important_news')}</div>
				</div>
				<div class="p_pop-up-content">
					<div class="p_pop-up-text"><?=$tanchunag?></div>
				</div>
				<div class="p_pop-up-btn" id="pop-up-btn">{:lang('I_see')}</div>
			</div>
		</div>
		
		<!-- 底部导航栏 -->
		<div class="p_footer">
			<a class="p_footer-item" href="{:url('index/home')}">
				<img src="/p_static1/img/footer_img-1_active.png" class="p_footer-item-img">
				<div class="p_footer-item-text active">{:lang('Home')}</div>
			</a>
			<a class="p_footer-item" href="{:url('order/index')}">
				<img src="/p_static1/img/footer_img-2.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Registro')}</div>
			</a>
			<?php
				$level = session('level') ? session('level') : 0;
				// 安全处理level值，确保为数字
				$level = is_numeric($level) ? (int)$level : 0;
				$level = $level + 1;
				$url = '/index/rot_order/index.html?type=' . $level;
			?>
			<a class="p_footer-item" href="<?=$url?>">
				<div class="p_footer-middle">
					<div class="p_footer-middle-text">{:lang('Apero')}</div>
				</div>
			</a>
			<a class="p_footer-item" href="{:url('support/index')}">
				<img src="/p_static1/img/footer_img-3.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Servicio')}</div>
			</a>
			<a class="p_footer-item" href="{:url('my/index')}">
				<img src="/p_static1/img/footer_img-4.svg" class="p_footer-item-img">
				<div class="p_footer-item-text">{:lang('Mi')}</div>
			</a>
		</div>
		
		<script src="/static_new/js/jquery.min.js"></script>
		<script>
			// 点击确认按钮，隐藏首页弹窗
			$('#pop-up-btn').click(function() {
				$('#pop-up').hide();
			});
			
			// 根据会员等级，跳转页面
			$('.announcement-task .a-t-items').click(function() {
				if (!$(this).hasClass('not')) {
					var cid = $(this).data('cid');
					window.location.href = '/index/rot_order/index.html?type='+cid;
				}
			});

			// 显示隐藏 更多 弹窗
			$('#more-btn').on('click', function(event) {
				$('#more-wrapper').show();
			});
			$('#more-container').on('click', function(event) {
				event.stopPropagation();
			});
			$('#more-wrapper').on('click', function() {
				$('#more-wrapper').hide();
			});
		</script>
	</body>
</html>
