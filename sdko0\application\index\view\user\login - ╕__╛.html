<!DOCTYPE html>
<html lang="cn">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0;" name="viewport" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>登录页面</title>
    <link rel="stylesheet" href="__ROOT__/public/css/style.css">
    <script src="__ROOT__/static/plugs/jquery/jquery.min.js"></script>
    <script src="__ROOT__/public/js/ui.js"></script>
    <link rel="stylesheet" href="__ROOT__/public/css/ui.css">
    <link rel="stylesheet" href="__ROOT__/public/js/layer_mobile/need/layer.css">
    <script src="__ROOT__/public/js/layer_mobile/layer.js"></script>
    <style>
        body {
            background-image: url(__ROOT__/public/img/user_bg.png);
            background-size: cover;
            padding-top: 3rem;
            font-size: .6rem;
        }

        .logo {
            width: 4.5rem;
            height: 4.5rem;
            margin: 0 auto 3rem;
            background: white;
            border-radius: 50%;
        }
        .form{
            margin: 0 auto;
            width: 80%;
            display: flex;
            justify-content: space-between;
            flex-direction: column;
        }
        .input_radius {
            background: white;
            width: 100%;
            border-radius: 50px;
            height: 2rem;
            margin: 0 auto;
            display: flex;
            padding: 0 1rem;
        }

        .input_radius span {
            width: 1rem;
            height: 1rem;
            margin: auto .5rem auto 0;
            background: #bbbbbb;
            position: relative;
            border-radius: 50%;
        }
        .input_radius span::before{
            content:"";
            width: .6rem;
            height: .6rem;
            position: absolute;
            left: 0;right: 0;bottom: 0;top: 0;
            margin: auto;
            background-size: 100%;
            background-repeat: no-repeat;
        }
        .input_radius input {
            border: none;
            outline: none;
        }

        input::placeholder {
            color: #bbbbbb;
        }
        a{  
            width: 30%;
            margin: 1rem 0 0 auto;
        }
        .btn{
            width: 80%;
            height: 2rem;
            line-height: 2rem;
            text-align: center;
            border-radius: 50px;
            margin: 1rem auto 0;
            color: white;
            letter-spacing: .5rem;
            font-size: .7rem;
        }
        .user::before{
            background-image: url(__ROOT__/public/img/user.png);
        }
        .input_radius span.pwd::before{
            background-image: url(__ROOT__/public/img/pwd.png);
            width: .7rem;
            height: .7rem;
        }
    </style>
</head>

<body>
    <div class="logo">
        <img src="/public/image/logo.png" alt="Logo">
    </div>
    <div class="form">
        <div class="input_radius" style="margin-bottom: 2rem;">
            <span class="icon user"></span>
            <input type="text" id="tel" placeholder="请输入账号">
        </div>
        <div class="input_radius">
            <span class="icon pwd"></span>
            <input type="password" id="pwd" placeholder="请输入密码">
        </div>
        <a href="./forget.html">忘记密码 ？</a>
    </div>
    <div class="btn login_btn" style="background: rgb(224, 90, 91);margin-top: 2rem;">登录</div>
    <div class="btn" onclick="location.href='./register.html'" style="background: rgb(90, 153, 224);">注册</div>
</body>
<script>
    $(".login_btn").click(function() {
        var tel = $('#tel').val(),
            pwd = $('#pwd').val();
        if (tel == "") {
            QS_toast.show("请输入账号", true)
        }
        else if (pwd == "") {
            QS_toast.show("请输入密码", true)
        } else {
            $.ajax({
                url: "{:url('do_login')}",
                type: "POST",
                dataType: "JSON",
                data: { tel:tel, pwd:pwd },
                success: function(res) {
                    console.log(res)
                    if(res.code==0){
                        QS_toast.show(res.info,true)
                        var timer = setTimeout(function(){
                            location.href="{:url('index/home')}"
                        },1800)
                    }else{
                        QS_toast.show(res.info,true)
                    }
                },
                error: function(err) { console.log(err) }
            })
        }

    })
</script>

</html>