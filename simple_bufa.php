<?php
/**
 * 简单补发收益脚本 - 不依赖框架
 * 直接运行：php simple_bufa.php
 * 或通过浏览器访问：http://您的域名/simple_bufa.php?start_date=2025-05-19&end_date=2025-05-22
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 数据库配置 - 根据您的实际配置修改
$db_config = [
    'host' => 'localhost',
    'port' => 3306,
    'dbname' => 'danss', // 修改为实际数据库名
    'username' => 'danss',    // 一般为root
    'password' => 'MTbhcsYaFBrnMiX6',    // 修改为实际密码
    'charset' => 'utf8'
];

// 尝试从配置文件读取数据库配置
if (file_exists(__DIR__ . '/config/database.php')) {
    $config = include(__DIR__ . '/config/database.php');
    if (isset($config['connections']['mysql'])) {
        $mysql_config = $config['connections']['mysql'];
        $db_config = [
            'host' => $mysql_config['hostname'] ?? 'localhost',
            'port' => $mysql_config['hostport'] ?? 3306,
            'dbname' => $mysql_config['database'] ?? 'danss',
            'username' => $mysql_config['username'] ?? 'danss',
            'password' => $mysql_config['password'] ?? 'MTbhcsYaFBrnMiX6',
            'charset' => $mysql_config['charset'] ?? 'utf8'
        ];
    }
}

// 连接数据库
try {
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "数据库连接成功<br>\n";
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 辅助函数：生成订单号
function getSn($prefix = '') {
    return $prefix . date('ymdHis') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
}

// 辅助函数：写入日志
function writeLog($message) {
    $log_file = __DIR__ . '/runtime/log/simple_bufa.log';
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    file_put_contents($log_file, date('Y-m-d H:i:s') . " - $message\n", FILE_APPEND);
    echo date('Y-m-d H:i:s') . " - $message<br>\n";
    
    // 安全的缓冲区刷新
    if (ob_get_level()) {
        ob_flush();
    }
    if (function_exists('flush')) {
        flush();
    }
}

try {
    // 获取参数
    $start_date = $_GET['start_date'] ?? ($_POST['start_date'] ?? date('Y-m-d', strtotime('-7 days')));
    $end_date = $_GET['end_date'] ?? ($_POST['end_date'] ?? date('Y-m-d'));
    
    writeLog("开始补发收益：{$start_date} 到 {$end_date}");
    
    // 转换日期为时间戳
    $start_timestamp = strtotime($start_date . ' 00:00:00');
    $end_timestamp = strtotime($end_date . ' 23:59:59');
    
    if ($start_timestamp >= $end_timestamp) {
        writeLog("错误：开始日期必须小于结束日期");
        exit;
    }
    
    // 生成需要检查的日期列表
    $check_dates = [];
    $current = $start_timestamp;
    while ($current <= $end_timestamp) {
        $check_dates[] = [
            'date' => date('Y-m-d', $current),
            'start' => $current,
            'end' => $current + 86399
        ];
        $current += 86400;
    }
    
    writeLog("需要检查的日期数量：" . count($check_dates));
    
    // 测试数据库连接
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_users");
    $user_count = $stmt->fetchColumn();
    writeLog("数据库连接正常，用户总数：$user_count");
    
    // 获取在检查期间内有投资记录的所有用户
    $sql = "SELECT DISTINCT uid FROM xy_lixibao 
            WHERE type = 1 AND is_qu = 0 
            AND addtime <= ? AND endtime >= ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$end_timestamp, $start_timestamp]);
    $users_with_investments = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    writeLog("找到需要检查的用户数量：" . count($users_with_investments));
    
    $processed_users = 0;
    $missing_days = 0;
    $total_bufa_income = 0;
    
    foreach ($users_with_investments as $uid) {
        // 获取用户名
        $stmt = $pdo->prepare("SELECT username FROM xy_users WHERE id = ?");
        $stmt->execute([$uid]);
        $username = $stmt->fetchColumn() ?: "用户#$uid";
        
        writeLog("开始处理用户：$username (ID: $uid)");
        
        $user_missing_days = 0;
        $user_total_income = 0;
        
        foreach ($check_dates as $date_info) {
            $check_date = $date_info['date'];
            $day_start = $date_info['start'];
            $day_end = $date_info['end'];
            
            // 检查该用户在这一天是否已经有收益记录
            $stmt = $pdo->prepare("SELECT id FROM xy_balance_log 
                                  WHERE uid = ? AND type = 23 AND status = 1 
                                  AND addtime BETWEEN ? AND ?");
            $stmt->execute([$uid, $day_start, $day_end]);
            
            if ($stmt->fetchColumn()) {
                // 已有收益记录，跳过
                continue;
            }
            
            // 获取该用户在这一天的有效投资记录
            $stmt = $pdo->prepare("SELECT * FROM xy_lixibao 
                                  WHERE uid = ? AND type = 1 AND is_qu = 0 
                                  AND addtime <= ? AND endtime > ?");
            $stmt->execute([$uid, $day_end, $day_start]);
            $user_investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($user_investments)) {
                // 该用户在这一天没有有效投资，跳过
                continue;
            }
            
            // 按产品分组计算收益
            $product_amounts = [];
            $product_rates = [];
            $product_names = [];
            
            foreach ($user_investments as $investment) {
                $product_id = $investment['sid'];
                
                if (!isset($product_amounts[$product_id])) {
                    $product_amounts[$product_id] = 0;
                    
                    // 获取产品信息
                    $stmt = $pdo->prepare("SELECT name, bili FROM xy_lixibao_list WHERE id = ?");
                    $stmt->execute([$product_id]);
                    $product_info = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($product_info) {
                        $product_names[$product_id] = $product_info['name'];
                        $product_rates[$product_id] = $product_info['bili'];
                    } else {
                        $product_names[$product_id] = "产品#$product_id";
                        $product_rates[$product_id] = 0.05; // 默认5%
                    }
                }
                
                $product_amounts[$product_id] += $investment['num'];
            }
            
            // 计算这一天的总收益
            $day_total_income = 0;
            $day_details = [];
            
            foreach ($product_amounts as $product_id => $amount) {
                if ($amount <= 0) continue;
                
                $rate = $product_rates[$product_id];
                $product_income = $amount * $rate;
                $day_total_income += $product_income;
                
                $day_details[] = [
                    'product_id' => $product_id,
                    'product_name' => $product_names[$product_id],
                    'amount' => $amount,
                    'rate' => $rate,
                    'income' => $product_income
                ];
                
                writeLog("  产品：{$product_names[$product_id]} (ID: $product_id), 金额：$amount, 利率：" . ($rate*100) . "%, 收益：$product_income");
            }
            
            if ($day_total_income > 0) {
                // 需要补发收益
                $user_missing_days++;
                $user_total_income += $day_total_income;
                
                // 使用当天的时间戳（中午12点）作为补发时间
                $bufa_time = $day_start + 43200; // 12:00:00
                
                try {
                    // 开始事务
                    $pdo->beginTransaction();
                    
                    // 更新用户余额
                    $stmt = $pdo->prepare("UPDATE xy_users SET balance = balance + ? WHERE id = ?");
                    $stmt->execute([$day_total_income, $uid]);
                    
                    // 为每个产品添加收益记录到xy_lixibao表
                    foreach ($day_details as $detail) {
                        $stmt = $pdo->prepare("INSERT INTO xy_lixibao 
                                              (uid, num, addtime, type, status, yuji_num, real_num, is_sy, sid, shouxu, bili, day, update_time) 
                                              VALUES (?, ?, ?, 3, 1, ?, ?, 1, ?, 0, ?, 1, ?)");
                        $stmt->execute([
                            $uid,
                            $detail['income'],
                            $bufa_time,
                            $detail['income'],
                            $detail['income'],
                            $detail['product_id'],
                            $detail['rate'],
                            $bufa_time
                        ]);
                    }
                    
                    // 添加余额变动记录
                    $oid = getSn('FIX');
                    $stmt = $pdo->prepare("INSERT INTO xy_balance_log 
                                          (uid, oid, num, type, status, addtime) 
                                          VALUES (?, ?, ?, 23, 1, ?)");
                    $stmt->execute([$uid, $oid, $day_total_income, $bufa_time]);
                    
                    // 提交事务
                    $pdo->commit();
                    
                    writeLog("✅ 补发收益成功：用户 $username (ID: $uid), 日期: $check_date, 金额: $day_total_income");
                    
                } catch (Exception $e) {
                    // 回滚事务
                    $pdo->rollback();
                    $error_msg = "❌ 补发收益失败: 用户 $username (ID: $uid), 日期: $check_date, 错误: " . $e->getMessage();
                    writeLog($error_msg);
                }
            }
        }
        
        if ($user_missing_days > 0) {
            $processed_users++;
            $missing_days += $user_missing_days;
            $total_bufa_income += $user_total_income;
            
            writeLog("用户 $username 处理完成：缺失天数 $user_missing_days，补发金额 $user_total_income");
        } else {
            writeLog("用户 $username 无需补发收益");
        }
    }
    
    writeLog("🎉 补发收益完成");
    writeLog("📊 处理用户数：$processed_users");
    writeLog("📅 补发天数：$missing_days");
    writeLog("💰 补发总金额：$total_bufa_income");
    
    // 输出HTML结果
    echo "<h2>补发收益完成</h2>";
    echo "<p>检查期间：$start_date 到 $end_date</p>";
    echo "<p>处理用户数：$processed_users</p>";
    echo "<p>补发天数：$missing_days</p>";
    echo "<p>补发总金额：$total_bufa_income</p>";
    echo "<p>详细日志请查看：runtime/log/simple_bufa.log</p>";
    
} catch (Exception $e) {
    writeLog("执行出错：" . $e->getMessage());
    echo "<h2>执行出错</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?> 