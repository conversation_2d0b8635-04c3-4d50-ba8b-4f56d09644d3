<?php
// 简单的定时任务测试脚本
echo "=== 定时任务测试 ===\n";
echo "当前时间: " . date('Y-m-d H:i:s') . "\n";
echo "PHP版本: " . PHP_VERSION . "\n";

// 检查是否是Web访问
$is_web = isset($_SERVER['HTTP_HOST']);
echo "是否Web访问: " . ($is_web ? '是' : '否') . "\n";

if ($is_web) {
    echo "域名: " . $_SERVER['HTTP_HOST'] . "\n";
    echo "请求URI: " . $_SERVER['REQUEST_URI'] . "\n";
}

// 测试HTTP请求
echo "\n=== HTTP请求测试 ===\n";
$url = 'https://tiktokpro.org/index/crontab/lixibao_js';

// 方法1: 使用curl命令行
$curl_result = shell_exec('curl -s "' . $url . '"');
if ($curl_result) {
    if (strpos($curl_result, '处理用户数') !== false) {
        echo "✅ curl命令执行成功\n";
        // 提取处理用户数
        if (preg_match('/处理用户数[：:]?\s*(\d+)/', $curl_result, $matches)) {
            echo "处理用户数: " . $matches[1] . "\n";
        }
    } else {
        echo "❌ curl命令可能失败或返回异常\n";
        echo "返回内容前100字符: " . substr($curl_result, 0, 100) . "\n";
    }
} else {
    echo "❌ curl命令执行失败\n";
}

echo "\n=== 建议 ===\n";
echo "如果curl命令能正常工作，建议宝塔计划任务使用以下命令:\n";
echo 'curl -s "https://tiktokpro.org/index/crontab/lixibao_js"' . "\n";
echo "\n如果不行，则可能是权限、防火墙或其他问题\n";
?> 