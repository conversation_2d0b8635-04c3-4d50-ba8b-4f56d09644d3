<?php
// 简化的利息宝数据库测试脚本
echo "=== 利息宝问题诊断 ===\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n\n";

// 数据库配置
$host = '127.0.0.1';
$port = 3306;
$database = 'g5_vt1685_site';
$username = 'g5_vt1685_site';
$password = 'g5_vt1685_site';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

// 1. 检查活跃投资
echo "=== 1. 检查活跃投资 ===\n";
$stmt = $pdo->query("SELECT COUNT(*) FROM xy_lixibao WHERE type = 1 AND status = 1 AND endtime > " . time());
$active_investments = $stmt->fetchColumn();
echo "当前活跃投资数: $active_investments\n";

if ($active_investments > 0) {
    // 显示前几个活跃投资
    $stmt = $pdo->query("
        SELECT l.id, l.uid, l.num, l.addtime, l.endtime, l.product_id,
               FROM_UNIXTIME(l.addtime) as start_date,
               FROM_UNIXTIME(l.endtime) as end_date,
               DATEDIFF(FROM_UNIXTIME(l.endtime), NOW()) as days_left
        FROM xy_lixibao l 
        WHERE l.type = 1 AND l.status = 1 AND l.endtime > " . time() . "
        ORDER BY l.addtime DESC 
        LIMIT 5
    ");
    
    $investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "活跃投资详情 (前5个):\n";
    foreach ($investments as $inv) {
        echo "  投资ID: {$inv['id']}, 用户ID: {$inv['uid']}, 金额: ¥{$inv['num']}, 剩余天数: {$inv['days_left']}\n";
    }
} else {
    echo "⚠️  没有活跃投资！\n";
}

// 2. 检查最近收益记录
echo "\n=== 2. 检查最近收益记录 ===\n";
$yesterday_start = strtotime(date('Y-m-d', strtotime('-1 day')));
$yesterday_end = $yesterday_start + 86400;

$stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime BETWEEN $yesterday_start AND $yesterday_end");
$yesterday_income = $stmt->fetchColumn();
echo "昨天收益记录数: $yesterday_income\n";

$stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23 AND addtime > " . (time() - 24 * 3600));
$last_24h_income = $stmt->fetchColumn();
echo "最近24小时收益记录数: $last_24h_income\n";

if ($yesterday_income == 0 && $active_investments > 0) {
    echo "❌ 问题发现：有活跃投资但昨天没有收益记录！\n";
}

// 3. 检查最近的收益记录详情
if ($last_24h_income > 0) {
    echo "\n最近收益记录详情:\n";
    $stmt = $pdo->query("
        SELECT bl.id, bl.uid, bl.num, bl.addtime, bl.remark,
               FROM_UNIXTIME(bl.addtime) as income_date
        FROM xy_balance_log bl 
        WHERE bl.type = 23 AND bl.addtime > " . (time() - 24 * 3600) . "
        ORDER BY bl.addtime DESC 
        LIMIT 5
    ");
    
    $recent_incomes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($recent_incomes as $income) {
        echo "  收益ID: {$income['id']}, 用户ID: {$income['uid']}, 金额: ¥{$income['num']}, 时间: {$income['income_date']}\n";
    }
}

// 4. 检查产品配置
echo "\n=== 3. 检查产品配置 ===\n";
$stmt = $pdo->query("SELECT * FROM xy_lixibao_list ORDER BY id");
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($products)) {
    echo "❌ 没有找到利息宝产品！\n";
} else {
    echo "产品列表:\n";
    foreach ($products as $product) {
        $title = isset($product['title']) ? $product['title'] : (isset($product['name']) ? $product['name'] : '未知');
        $rate = isset($product['rate']) ? $product['rate'] : (isset($product['bili']) ? $product['bili'] : 0);
        $day = isset($product['day']) ? $product['day'] : '未知';
        $status = isset($product['status']) ? $product['status'] : '未知';
        echo "  产品ID: {$product['id']}, 名称: $title, 利率: {$rate}%, 天数: $day, 状态: $status\n";
    }
}

// 5. 检查用户余额宝余额
echo "\n=== 4. 检查用户余额宝余额 ===\n";
$stmt = $pdo->query("
    SELECT COUNT(*) as user_count, SUM(lixibao_balance) as total_balance 
    FROM xy_users 
    WHERE lixibao_balance > 0
");
$balance_info = $stmt->fetch(PDO::FETCH_ASSOC);
echo "有余额宝余额的用户数: {$balance_info['user_count']}\n";
echo "总余额宝余额: ¥{$balance_info['total_balance']}\n";

// 6. 检查具体问题用户
echo "\n=== 5. 检查具体问题用户 ===\n";
if ($active_investments > 0) {
    // 查找有投资但最近没有收益的用户
    $stmt = $pdo->query("
        SELECT DISTINCT l.uid, u.username, 
               COUNT(l.id) as investment_count,
               SUM(l.num) as total_investment
        FROM xy_lixibao l
        LEFT JOIN xy_users u ON l.uid = u.id
        WHERE l.type = 1 AND l.status = 1 AND l.endtime > " . time() . "
        AND NOT EXISTS (
            SELECT 1 FROM xy_balance_log bl 
            WHERE bl.uid = l.uid AND bl.type = 23 
            AND bl.addtime > $yesterday_start
        )
        GROUP BY l.uid
        LIMIT 5
    ");
    
    $problem_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($problem_users)) {
        echo "发现问题用户 (有投资但昨天没有收益):\n";
        foreach ($problem_users as $user) {
            echo "  用户ID: {$user['uid']}, 用户名: {$user['username']}, 投资笔数: {$user['investment_count']}, 投资总额: ¥{$user['total_investment']}\n";
        }
    } else {
        echo "✅ 所有有投资的用户都有最近的收益记录\n";
    }
}

// 7. 生成修复建议
echo "\n=== 6. 修复建议 ===\n";

if ($active_investments > 0 && $yesterday_income == 0) {
    echo "🔧 问题确认：有活跃投资但昨天没有收益记录\n";
    echo "🔧 建议1: 立即手动触发收益计算\n";
    echo "   访问: http://您的域名/index/crontab/lixibao_js\n";
    echo "🔧 建议2: 运行补发脚本\n";
    echo "   执行: quick_fix_lixibao.php\n";
    echo "🔧 建议3: 检查定时任务是否正常运行\n";
    echo "   检查crontab配置和Web服务器状态\n";
} elseif ($active_investments == 0) {
    echo "ℹ️  当前没有活跃投资，这可能是正常情况\n";
} else {
    echo "✅ 系统看起来正常，有投资且有最近的收益记录\n";
}

echo "\n=== 诊断完成 ===\n";
?> 