<?php
/**
 * 测试补发脚本 - 检查数据库连接
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始测试补发脚本...\n";

// 数据库配置
$db_config = [
    'host' => 'localhost',
    'port' => 3306,
    'dbname' => 'danss',
    'username' => 'root',
    'password' => 'MTbhcsYaFBrnMiX6',
    'charset' => 'utf8'
];

echo "数据库配置：\n";
echo "主机: {$db_config['host']}\n";
echo "端口: {$db_config['port']}\n";
echo "数据库: {$db_config['dbname']}\n";
echo "用户名: {$db_config['username']}\n";
echo "密码: " . str_repeat('*', strlen($db_config['password'])) . "\n\n";

// 测试数据库连接
try {
    echo "正在连接数据库...\n";
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功！\n\n";
    
    // 测试基本查询
    echo "测试查询用户表...\n";
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_users");
    $user_count = $stmt->fetchColumn();
    echo "✅ 用户总数：$user_count\n\n";
    
    // 测试查询投资表
    echo "测试查询投资表...\n";
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_lixibao WHERE type = 1 AND is_qu = 0");
    $investment_count = $stmt->fetchColumn();
    echo "✅ 活跃投资记录数：$investment_count\n\n";
    
    // 测试查询收益表
    echo "测试查询收益表...\n";
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_balance_log WHERE type = 23");
    $income_count = $stmt->fetchColumn();
    echo "✅ 收益记录数：$income_count\n\n";
    
    // 测试查询产品表
    echo "测试查询产品表...\n";
    $stmt = $pdo->query("SELECT id, name, bili FROM xy_lixibao_list WHERE status = 1");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ 活跃产品数：" . count($products) . "\n";
    foreach ($products as $product) {
        $rate_percent = ($product['bili'] * 100);
        echo "  - 产品ID: {$product['id']}, 名称: {$product['name']}, 日利率: {$rate_percent}%\n";
    }
    echo "\n";
    
    // 测试特定用户的投资情况（用户19）
    echo "测试查询用户19的投资情况...\n";
    $stmt = $pdo->prepare("SELECT * FROM xy_lixibao WHERE uid = 19 AND type = 1 ORDER BY addtime DESC");
    $stmt->execute();
    $user19_investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ 用户19的投资记录数：" . count($user19_investments) . "\n";
    
    foreach ($user19_investments as $investment) {
        $start_date = date('Y-m-d H:i:s', $investment['addtime']);
        $end_date = date('Y-m-d H:i:s', $investment['endtime']);
        $status = $investment['is_qu'] ? '已取出' : '投资中';
        echo "  - 投资ID: {$investment['id']}, 金额: {$investment['num']}, 产品ID: {$investment['sid']}, 开始: $start_date, 结束: $end_date, 状态: $status\n";
    }
    echo "\n";
    
    // 测试用户19最近的收益记录
    echo "测试查询用户19最近的收益记录...\n";
    $stmt = $pdo->prepare("SELECT * FROM xy_balance_log WHERE uid = 19 AND type = 23 ORDER BY addtime DESC LIMIT 10");
    $stmt->execute();
    $user19_income = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ 用户19最近的收益记录数：" . count($user19_income) . "\n";
    
    foreach ($user19_income as $income) {
        $income_date = date('Y-m-d H:i:s', $income['addtime']);
        echo "  - 收益ID: {$income['id']}, 金额: {$income['num']}, 时间: $income_date, 状态: {$income['status']}\n";
    }
    echo "\n";
    
    echo "🎉 所有测试通过！数据库连接和表结构都正常。\n";
    echo "您现在可以安全地运行补发脚本了。\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    echo "请检查以下内容：\n";
    echo "1. 数据库服务是否启动\n";
    echo "2. 数据库名称是否正确: danss\n";
    echo "3. 用户名和密码是否正确\n";
    echo "4. MySQL服务是否在端口3306运行\n";
} catch (Exception $e) {
    echo "❌ 执行出错: " . $e->getMessage() . "\n";
}
?> 