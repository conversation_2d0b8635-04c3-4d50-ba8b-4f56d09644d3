<?php
/**
 * 测试宝塔计划任务与网页直接访问的差异
 * 用于诊断为什么网页访问能执行而计划任务不能执行的问题
 */

echo "=== 定时任务环境差异诊断 ===\n";
echo "执行时间: " . date('Y-m-d H:i:s') . "\n\n";

// 1. 检查执行环境
echo "=== 1. 执行环境信息 ===\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "当前用户: " . get_current_user() . "\n";
echo "进程ID: " . getmypid() . "\n";
echo "当前目录: " . getcwd() . "\n";
echo "脚本文件: " . __FILE__ . "\n";
echo "PHP执行文件: " . PHP_BINARY . "\n\n";

// 2. 检查环境变量
echo "=== 2. 环境变量 ===\n";
$important_vars = ['HTTP_HOST', 'SERVER_NAME', 'REQUEST_URI', 'REMOTE_ADDR', 'HTTP_USER_AGENT', 'PATH'];
foreach ($important_vars as $var) {
    $value = isset($_SERVER[$var]) ? $_SERVER[$var] : '未设置';
    echo "$var: $value\n";
}
echo "\n";

// 3. 检查是否是HTTP请求
echo "=== 3. 请求类型检查 ===\n";
$is_web = isset($_SERVER['HTTP_HOST']);
$is_cli = php_sapi_name() === 'cli';
echo "是否Web访问: " . ($is_web ? '是' : '否') . "\n";
echo "是否CLI模式: " . ($is_cli ? '是' : '否') . "\n";
echo "SAPI名称: " . php_sapi_name() . "\n\n";

// 4. 测试数据库连接
echo "=== 4. 数据库连接测试 ===\n";
$host = '127.0.0.1';
$port = 3306;
$database = 'g5_vt1685_site';
$username = 'g5_vt1685_site';
$password = 'g5_vt1685_site';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n";
    
    // 测试查询
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_users");
    $user_count = $stmt->fetchColumn();
    echo "用户总数: $user_count\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM xy_lixibao WHERE type = 1 AND status = 1 AND endtime > " . time());
    $active_investments = $stmt->fetchColumn();
    echo "活跃投资数: $active_investments\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. 模拟网页访问的条件
echo "=== 5. 模拟网页访问条件 ===\n";

// 设置必要的$_SERVER变量（模拟网页访问）
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'tiktokpro.org';
    echo "设置 HTTP_HOST: tiktokpro.org\n";
}

if (!isset($_SERVER['REQUEST_URI'])) {
    $_SERVER['REQUEST_URI'] = '/index/crontab/lixibao_js';
    echo "设置 REQUEST_URI: /index/crontab/lixibao_js\n";
}

if (!isset($_SERVER['HTTP_USER_AGENT'])) {
    $_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (compatible; CronJob/1.0)';
    echo "设置 HTTP_USER_AGENT: Mozilla/5.0 (compatible; CronJob/1.0)\n";
}

echo "\n";

// 6. 测试框架加载
echo "=== 6. 框架加载测试 ===\n";
try {
    // 尝试加载ThinkPHP框架
    if (file_exists('./thinkphp/base.php')) {
        require_once './thinkphp/base.php';
        echo "✅ ThinkPHP框架加载成功\n";
    } else {
        echo "❌ 找不到ThinkPHP框架文件\n";
        
        // 检查其他可能的路径
        $possible_paths = [
            './thinkphp/start.php',
            './vendor/autoload.php',
            './application/index.php'
        ];
        
        foreach ($possible_paths as $path) {
            if (file_exists($path)) {
                echo "找到可能的框架文件: $path\n";
            }
        }
    }
} catch (Exception $e) {
    echo "❌ 框架加载失败: " . $e->getMessage() . "\n";
}

// 7. 直接HTTP请求测试
echo "\n=== 7. HTTP请求测试 ===\n";
$url = 'https://tiktokpro.org/index/crontab/lixibao_js';

// 测试不同的User-Agent
$user_agents = [
    'curl/7.68.0',
    'Mozilla/5.0 (compatible; BaoTaCron/1.0)',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    ''
];

foreach ($user_agents as $ua) {
    echo "测试User-Agent: " . ($ua ?: '空') . "\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => "User-Agent: $ua\r\n",
            'timeout' => 10
        ]
    ]);
    
    $result = @file_get_contents($url, false, $context);
    
    if ($result !== false) {
        // 提取关键信息
        if (preg_match('/处理用户数[：:]?\s*(\d+)/', $result, $matches)) {
            $processed_users = $matches[1];
            echo "  处理用户数: $processed_users\n";
        }
        
        if (preg_match('/总收益金额[：:]?\s*([\d.]+)/', $result, $matches)) {
            $total_income = $matches[1];
            echo "  总收益金额: $total_income\n";
        }
        
        if (strpos($result, '执行成功') !== false) {
            echo "  ✅ 执行成功\n";
        } else {
            echo "  ❌ 执行失败或异常\n";
        }
    } else {
        echo "  ❌ 请求失败\n";
    }
    echo "\n";
}

// 8. 建议的解决方案
echo "=== 8. 解决方案建议 ===\n";
echo "基于以上检测，可能的问题和解决方案：\n\n";

echo "1. **环境变量问题**\n";
echo "   - 宝塔计划任务可能缺少HTTP相关的环境变量\n";
echo "   - 建议在计划任务中使用完整的curl命令\n\n";

echo "2. **User-Agent问题**\n";
echo "   - 某些防护软件可能阻止特定的User-Agent\n";
echo "   - 建议设置浏览器类型的User-Agent\n\n";

echo "3. **权限问题**\n";
echo "   - 计划任务执行用户可能与网页执行用户不同\n";
echo "   - 检查文件和目录权限\n\n";

echo "4. **推荐的宝塔计划任务配置**\n";
echo "   任务类型: Shell脚本\n";
echo "   执行周期: 每天 13:00\n";
echo "   脚本内容:\n";
echo '   curl -H "User-Agent: Mozilla/5.0 (compatible; BaoTaCron/1.0)" -s "https://tiktokpro.org/index/crontab/lixibao_js" > /tmp/lixibao_result.log 2>&1' . "\n\n";

echo "5. **备用PHP脚本方案**\n";
echo "   如果HTTP请求方式不行，可以直接使用PHP脚本:\n";
echo "   任务类型: PHP脚本\n";
echo "   脚本路径: " . getcwd() . "/force_daily_income.php\n\n";

echo "=== 诊断完成 ===\n";
?> 