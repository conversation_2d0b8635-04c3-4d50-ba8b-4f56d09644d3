<?php
/**
 * 测试ThinkPHP框架加载
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始测试ThinkPHP框架加载...\n";

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

try {
    echo "加载框架文件: " . __DIR__ . '/thinkphp/base.php' . "\n";
    
    if (!file_exists(__DIR__ . '/thinkphp/base.php')) {
        die("错误：框架文件不存在\n");
    }
    
    require __DIR__ . '/thinkphp/base.php';
    
    echo "框架加载成功\n";
    
    echo "测试数据库连接...\n";
    
    // 测试数据库连接
    
    // 初始化应用
    $app = new think\App();
    $app->initialize();
    
    $count = \think\Db::name('xy_users')->count();
    echo "数据库连接成功，用户总数: $count\n";
    
    echo "所有测试通过！\n";
    
} catch (\Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
?> 