<?php
// 利息宝HTTP测试脚本
// 通过HTTP请求测试利息宝收益计算功能

echo "=== 利息宝HTTP测试开始 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 配置
$base_url = "http://localhost"; // 请根据实际情况修改域名
$test_endpoints = [
    'lixibao_js' => '/index/crontab/lixibao_js',
    'lxb_jiesuan' => '/index/crontab/lxb_jiesuan'
];

// 测试函数
function testEndpoint($url, $name) {
    echo "测试 {$name}: {$url}\n";
    
    // 使用curl发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ 请求失败: {$error}\n";
        return false;
    }
    
    if ($http_code == 200) {
        echo "✅ 请求成功 (HTTP {$http_code})\n";
        
        // 尝试解析响应
        if (strpos($response, 'json') !== false || strpos($response, '{') === 0) {
            // JSON响应
            $json_data = json_decode($response, true);
            if ($json_data) {
                echo "响应数据: " . print_r($json_data, true) . "\n";
            }
        } else {
            // HTML响应，提取关键信息
            if (strpos($response, '余额宝收益计算结果') !== false) {
                echo "✅ 检测到收益计算页面\n";
                
                // 提取关键信息
                if (preg_match('/处理用户数:\s*(\d+)/', $response, $matches)) {
                    echo "处理用户数: {$matches[1]}\n";
                }
                if (preg_match('/总收益金额:\s*([\d.]+)/', $response, $matches)) {
                    echo "总收益金额: {$matches[1]}\n";
                }
            } else {
                echo "响应内容长度: " . strlen($response) . " 字符\n";
                echo "响应前200字符: " . substr($response, 0, 200) . "\n";
            }
        }
        return true;
    } else {
        echo "❌ 请求失败 (HTTP {$http_code})\n";
        echo "响应内容: " . substr($response, 0, 500) . "\n";
        return false;
    }
}

// 执行测试
foreach ($test_endpoints as $name => $endpoint) {
    $url = $base_url . $endpoint;
    $success = testEndpoint($url, $name);
    echo "\n" . str_repeat("-", 50) . "\n\n";
    
    if (!$success) {
        echo "⚠️  {$name} 测试失败，可能的原因:\n";
        echo "1. 服务器未启动或域名配置错误\n";
        echo "2. 路由配置问题\n";
        echo "3. 数据库连接问题\n";
        echo "4. PHP错误或异常\n\n";
    }
}

// 额外的诊断信息
echo "=== 额外诊断信息 ===\n";

// 检查本地服务器状态
echo "检查本地服务器状态...\n";
$ping_result = @file_get_contents($base_url, false, stream_context_create([
    'http' => [
        'timeout' => 5,
        'method' => 'GET'
    ]
]));

if ($ping_result !== false) {
    echo "✅ 本地服务器响应正常\n";
} else {
    echo "❌ 本地服务器无响应\n";
    echo "请检查:\n";
    echo "1. Web服务器是否启动 (Apache/Nginx)\n";
    echo "2. PHP是否正常工作\n";
    echo "3. 项目路径是否正确\n";
}

echo "\n=== 建议的解决方案 ===\n";
echo "1. 确保Web服务器正在运行\n";
echo "2. 检查项目的.htaccess或nginx配置\n";
echo "3. 验证数据库连接配置\n";
echo "4. 查看服务器错误日志\n";
echo "5. 手动在浏览器中访问: {$base_url}/index/crontab/lixibao_js\n";

echo "\n=== 测试完成 ===\n";
?> 