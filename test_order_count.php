<?php
/**
 * 测试抢单次数计算逻辑
 * 这个文件用于验证修改后的抢单次数显示是否正确
 */

// 模拟数据
$user_level = 1; // 用户等级
$order_num_by_level = [
    0 => 0,   // VIP 0
    1 => 5,   // VIP 1
    2 => 20,  // VIP 2
    3 => 50,  // VIP 3
    4 => 200, // VIP 4
    5 => 500, // VIP 5
    6 => 1000 // VIP 6
];

$today_grabbed_orders = 3; // 今日已抢单数

// 计算剩余可抢单次数
$max_orders = $order_num_by_level[$user_level];
$remaining_orders = max(0, $max_orders - $today_grabbed_orders);

echo "用户等级: VIP {$user_level}\n";
echo "每日可抢单次数: {$max_orders}\n";
echo "今日已抢单数: {$today_grabbed_orders}\n";
echo "今日剩余可抢单次数: {$remaining_orders}\n";

// 测试边界情况
echo "\n=== 边界测试 ===\n";

// 测试已达上限的情况
$today_grabbed_orders = 5;
$remaining_orders = max(0, $max_orders - $today_grabbed_orders);
echo "已抢满情况 - 今日已抢单数: {$today_grabbed_orders}, 剩余: {$remaining_orders}\n";

// 测试超出上限的情况
$today_grabbed_orders = 7;
$remaining_orders = max(0, $max_orders - $today_grabbed_orders);
echo "超出上限情况 - 今日已抢单数: {$today_grabbed_orders}, 剩余: {$remaining_orders}\n";

echo "\n修改完成！现在页面将显示'今日可抢单次数'而不是'今日已抢单数'。\n"; 