<?php
/**
 * 充值记录页面修复测试脚本
 * 
 * 此脚本用于测试recharge_admin方法是否正常工作
 */

// 数据库配置 - 请根据实际情况修改
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',  // 请填写您的数据库名
    'username' => 'danss',  // 请填写您的数据库用户名
    'password' => 'MTbhcsYaFBrnMiX6',  // 请填写您的数据库密码
    'charset'  => 'utf8mb4'
];

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>充值记录页面修复测试</h1>";
    echo "<p>测试/index/ctrl/recharge_admin页面是否能正常显示充值记录...</p>";
    
    // 1. 检查xy_recharge表是否存在
    echo "<h2>1. 检查充值记录表</h2>";
    $stmt = $db->query("SHOW TABLES LIKE 'xy_recharge'");
    $table_exists = $stmt->rowCount() > 0;
    
    if (!$table_exists) {
        echo "<p style='color:red'>❌ xy_recharge表不存在</p>";
        echo "<p>请检查数据库配置或创建充值记录表</p>";
        exit;
    } else {
        echo "<p style='color:green'>✅ xy_recharge表存在</p>";
    }
    
    // 2. 检查表结构
    echo "<h2>2. 检查表结构</h2>";
    $stmt = $db->query("DESCRIBE xy_recharge");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $required_fields = ['id', 'uid', 'num', 'status', 'addtime', 'pay_name'];
    $missing_fields = [];
    $existing_fields = array_column($columns, 'Field');
    
    foreach ($required_fields as $field) {
        if (!in_array($field, $existing_fields)) {
            $missing_fields[] = $field;
        }
    }
    
    if (empty($missing_fields)) {
        echo "<p style='color:green'>✅ 所有必需字段都存在</p>";
    } else {
        echo "<p style='color:orange'>⚠️ 缺少字段: " . implode(', ', $missing_fields) . "</p>";
    }
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>默认值</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. 检查充值记录数据
    echo "<h2>3. 检查充值记录数据</h2>";
    $stmt = $db->query("SELECT COUNT(*) as total FROM xy_recharge");
    $total_records = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p>总充值记录数: <strong>{$total_records}</strong></p>";
    
    if ($total_records > 0) {
        // 显示最近的几条记录
        $stmt = $db->query("
            SELECT id, uid, num, status, addtime, pay_name, remark 
            FROM xy_recharge 
            ORDER BY addtime DESC 
            LIMIT 5
        ");
        $recent_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>最近5条充值记录:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>订单号</th><th>用户ID</th><th>金额</th><th>状态</th><th>支付方式</th><th>时间</th></tr>";
        
        foreach ($recent_records as $record) {
            $status_text = '';
            $status_color = '';
            
            switch ($record['status']) {
                case 1:
                    $status_text = '待审核';
                    $status_color = '#ff7070';
                    break;
                case 2:
                    $status_text = '审核通过';
                    $status_color = '#777b9e';
                    break;
                case 3:
                    $status_text = '审核失败';
                    $status_color = '#ff7070';
                    break;
                default:
                    $status_text = '未知';
                    $status_color = '#999';
                    break;
            }
            
            echo "<tr>";
            echo "<td>{$record['id']}</td>";
            echo "<td>{$record['uid']}</td>";
            echo "<td>" . number_format($record['num'], 2) . "</td>";
            echo "<td style='color:{$status_color}'>{$status_text}</td>";
            echo "<td>{$record['pay_name']}</td>";
            echo "<td>" . date('Y-m-d H:i:s', $record['addtime']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 按状态统计
        echo "<h3>按状态统计:</h3>";
        $stmt = $db->query("
            SELECT status, COUNT(*) as count 
            FROM xy_recharge 
            GROUP BY status 
            ORDER BY status
        ");
        $status_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>状态</th><th>状态名称</th><th>数量</th></tr>";
        
        foreach ($status_stats as $stat) {
            $status_name = '';
            switch ($stat['status']) {
                case 1: $status_name = '待审核'; break;
                case 2: $status_name = '审核通过'; break;
                case 3: $status_name = '审核失败'; break;
                default: $status_name = '未知'; break;
            }
            
            echo "<tr>";
            echo "<td>{$stat['status']}</td>";
            echo "<td>{$status_name}</td>";
            echo "<td>{$stat['count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color:orange'>⚠️ 没有找到充值记录数据</p>";
        echo "<p>这可能是正常的，如果系统刚刚部署或者还没有用户充值</p>";
    }
    
    // 4. 检查控制器文件
    echo "<h2>4. 检查控制器文件</h2>";
    $ctrl_file = 'application/index/controller/Ctrl.php';
    
    if (file_exists($ctrl_file)) {
        echo "<p style='color:green'>✅ Ctrl.php控制器文件存在</p>";
        
        // 检查是否包含recharge_admin方法
        $file_content = file_get_contents($ctrl_file);
        if (strpos($file_content, 'function recharge_admin') !== false || strpos($file_content, 'public function recharge_admin') !== false) {
            echo "<p style='color:green'>✅ recharge_admin方法已添加</p>";
        } else {
            echo "<p style='color:red'>❌ recharge_admin方法不存在</p>";
        }
    } else {
        echo "<p style='color:red'>❌ Ctrl.php控制器文件不存在</p>";
    }
    
    // 5. 检查模板文件
    echo "<h2>5. 检查模板文件</h2>";
    $template_file = 'application/index/view/ctrl/recharge_admin.html';
    
    if (file_exists($template_file)) {
        echo "<p style='color:green'>✅ recharge_admin.html模板文件存在</p>";
    } else {
        echo "<p style='color:red'>❌ recharge_admin.html模板文件不存在</p>";
    }
    
    // 6. 测试建议
    echo "<h2>6. 测试建议</h2>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #007cba;'>";
    echo "<h3>接下来请进行以下测试：</h3>";
    echo "<ol>";
    echo "<li><strong>清除缓存</strong><br>删除 runtime/cache/ 目录下的所有文件</li>";
    echo "<li><strong>访问充值记录页面</strong><br>URL: /index/ctrl/recharge_admin</li>";
    echo "<li><strong>检查页面显示</strong><br>确认页面能正常加载，不再显示'There is no record on this page'</li>";
    echo "<li><strong>测试分页功能</strong><br>如果有多条记录，测试分页是否正常工作</li>";
    echo "<li><strong>检查错误日志</strong><br>如果仍有问题，查看 runtime/log/ 目录下的错误日志</li>";
    echo "</ol>";
    echo "</div>";
    
    // 7. 可能的问题和解决方案
    echo "<h2>7. 可能的问题和解决方案</h2>";
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107;'>";
    echo "<h3>如果页面仍然显示'There is no record on this page'：</h3>";
    echo "<ul>";
    echo "<li><strong>用户未登录</strong>：确保用户已正确登录，cookie中有user_id</li>";
    echo "<li><strong>数据库连接问题</strong>：检查数据库配置是否正确</li>";
    echo "<li><strong>权限问题</strong>：确保数据库用户有查询xy_recharge表的权限</li>";
    echo "<li><strong>缓存问题</strong>：清除所有缓存文件</li>";
    echo "<li><strong>路由问题</strong>：确保URL路由配置正确</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>✅ 测试脚本执行完成</h2>";
    echo "<p>请根据上述检查结果进行相应的修复操作。</p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库错误: " . $e->getMessage() . "</p>";
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 执行错误: " . $e->getMessage() . "</p>";
}
?>
