<?php
/**
 * VIP等级权限修复测试脚本
 * 
 * 此脚本用于测试VIP3用户访问VIP2接单页面的权限验证是否正常工作
 */

// 数据库配置 - 请根据实际情况修改
$database = [
    'hostname' => '127.0.0.1',
    'database' => 'danss',  // 请填写您的数据库名
    'username' => 'danss',  // 请填写您的数据库用户名
    'password' => 'MTbhcsYaFBrnMiX6',  // 请填写您的数据库密码
    'charset'  => 'utf8mb4'
];

try {
    // 连接数据库
    $dsn = "mysql:host={$database['hostname']};dbname={$database['database']};charset={$database['charset']}";
    $db = new PDO($dsn, $database['username'], $database['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>VIP等级权限修复测试</h1>";
    echo "<p>测试VIP3用户访问VIP2接单页面的权限验证...</p>";
    
    // 1. 检查VIP等级开关表
    echo "<h2>1. 检查VIP等级开关表</h2>";
    $stmt = $db->query("SELECT vip_level, is_enabled FROM xy_vip_level_switch WHERE vip_level IN (2, 3) ORDER BY vip_level");
    $switches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($switches)) {
        echo "<p style='color:red'>❌ VIP等级开关表不存在或数据为空</p>";
        echo "<p>请先运行 fix_vip_level_issue.php 脚本进行修复</p>";
        exit;
    }
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>VIP等级</th><th>开关状态</th></tr>";
    foreach ($switches as $switch) {
        $status = $switch['is_enabled'] ? '开启' : '关闭';
        $color = $switch['is_enabled'] ? 'green' : 'red';
        echo "<tr><td>VIP{$switch['vip_level']}</td><td style='color:{$color}'>{$status}</td></tr>";
    }
    echo "</table>";
    
    // 2. 模拟权限验证逻辑
    echo "<h2>2. 模拟权限验证</h2>";
    
    // 模拟VIP3用户访问VIP2任务
    $user_vip_level = 3;
    $task_vip_level = 2;
    
    echo "<p><strong>测试场景：</strong>VIP{$user_vip_level}用户访问VIP{$task_vip_level}任务</p>";
    
    // 检查用户等级是否足够
    if ($user_vip_level >= $task_vip_level) {
        echo "<p style='color:green'>✅ 用户VIP等级检查通过（{$user_vip_level} >= {$task_vip_level}）</p>";
        
        // 检查VIP等级开关
        $stmt = $db->prepare("SELECT is_enabled FROM xy_vip_level_switch WHERE vip_level = ?");
        $stmt->execute([$task_vip_level]);
        $switch_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($switch_info && $switch_info['is_enabled']) {
            echo "<p style='color:green'>✅ VIP{$task_vip_level}等级开关已开启</p>";
            echo "<p style='color:green; font-weight:bold; background-color:#e8f5e8; padding:10px; border-radius:5px;'>🎉 测试通过！VIP{$user_vip_level}用户可以正常访问VIP{$task_vip_level}接单页面</p>";
        } else {
            echo "<p style='color:red'>❌ VIP{$task_vip_level}等级开关已关闭</p>";
            echo "<p style='color:orange'>需要开启VIP{$task_vip_level}等级开关才能正常访问</p>";
        }
    } else {
        echo "<p style='color:red'>❌ 用户VIP等级不足（{$user_vip_level} < {$task_vip_level}）</p>";
    }
    
    // 3. 检查商品分类表
    echo "<h2>3. 检查商品分类表</h2>";
    $stmt = $db->query("SHOW COLUMNS FROM xy_goods_cate LIKE 'vip_level'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color:green'>✅ xy_goods_cate表包含vip_level字段</p>";
        
        // 查看分类数据
        $stmt = $db->query("SELECT id, name, vip_level FROM xy_goods_cate LIMIT 5");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($categories)) {
            echo "<p>商品分类示例：</p>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>分类ID</th><th>分类名称</th><th>VIP等级要求</th></tr>";
            foreach ($categories as $category) {
                $vip_level = $category['vip_level'] ?: 1;
                echo "<tr><td>{$category['id']}</td><td>{$category['name']}</td><td>VIP{$vip_level}</td></tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color:orange'>⚠️ xy_goods_cate表缺少vip_level字段</p>";
        echo "<p>请运行 fix_vip_level_issue.php 脚本进行修复</p>";
    }
    
    // 4. 检查关键文件
    echo "<h2>4. 检查关键文件</h2>";
    
    $files_to_check = [
        'application/common/service/VipLevelService.php' => 'VIP等级服务类',
        'application/index/controller/RotOrder.php' => '抢单控制器',
        'application/admin/model/Convey.php' => '订单模型'
    ];
    
    foreach ($files_to_check as $file => $description) {
        if (file_exists($file)) {
            echo "<p style='color:green'>✅ {$description}文件存在：{$file}</p>";
        } else {
            echo "<p style='color:red'>❌ {$description}文件不存在：{$file}</p>";
        }
    }
    
    // 5. 测试建议
    echo "<h2>5. 测试建议</h2>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #007cba;'>";
    echo "<h3>接下来请进行以下测试：</h3>";
    echo "<ol>";
    echo "<li><strong>清除缓存</strong><br>删除 runtime/cache/ 目录下的所有文件</li>";
    echo "<li><strong>使用VIP3用户登录</strong><br>确保测试用户的VIP等级为3</li>";
    echo "<li><strong>访问VIP2接单页面</strong><br>URL: /index/rot_order/index.html?type=2</li>";
    echo "<li><strong>尝试抢单操作</strong><br>点击抢单按钮，观察是否还有500错误</li>";
    echo "<li><strong>检查错误日志</strong><br>如果仍有问题，查看 runtime/log/ 目录下的错误日志</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>✅ 测试脚本执行完成</h2>";
    echo "<p>如果所有检查都通过，VIP3用户应该能够正常访问VIP2接单页面了。</p>";
    
} catch(PDOException $e) {
    echo "<p style='color:red'>❌ 数据库错误: " . $e->getMessage() . "</p>";
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 执行错误: " . $e->getMessage() . "</p>";
}
?>
