<?php
/**
 * 钱包地址删除功能测试文件
 * 用于验证管理员密码验证是否正常工作
 */

// 引入ThinkPHP框架
require_once 'thinkphp/start.php';

use think\Db;

echo "=== 钱包地址删除功能测试 ===\n\n";

// 1. 测试管理员密码验证逻辑
echo "1. 测试管理员密码验证逻辑\n";

// 获取管理员信息
$admin = Db::name('system_user')->where('username', 'admin')->find();
if (!$admin) {
    echo "❌ 错误：找不到管理员账户\n";
    exit;
}

echo "✅ 管理员账户信息：\n";
echo "   - ID: {$admin['id']}\n";
echo "   - 用户名: {$admin['username']}\n";
echo "   - 密码哈希: " . substr($admin['password'], 0, 10) . "...\n";

// 2. 测试密码加密方式
echo "\n2. 测试密码加密方式\n";

$test_password = "123456"; // 假设管理员密码是123456
$md5_password = md5($test_password);
$sha1_password = sha1($test_password . ($admin['salt'] ?? '') . config('pwd_str'));

echo "   - 测试密码: {$test_password}\n";
echo "   - MD5加密: {$md5_password}\n";
echo "   - SHA1加密: {$sha1_password}\n";
echo "   - 数据库密码: {$admin['password']}\n";

if ($md5_password === $admin['password']) {
    echo "✅ 密码验证方式：MD5（正确）\n";
} elseif ($sha1_password === $admin['password']) {
    echo "✅ 密码验证方式：SHA1+Salt（正确）\n";
} else {
    echo "❌ 密码验证方式：未知或密码不匹配\n";
}

// 3. 测试钱包地址数据
echo "\n3. 测试钱包地址数据\n";

$wallet_count = Db::name('xy_user_wallet')->count();
echo "   - 钱包地址总数: {$wallet_count}\n";

if ($wallet_count > 0) {
    $sample_wallet = Db::name('xy_user_wallet')->order('id desc')->find();
    echo "   - 最新钱包地址ID: {$sample_wallet['id']}\n";
    echo "   - 用户ID: {$sample_wallet['uid']}\n";
    echo "   - 钱包地址: " . substr($sample_wallet['wallet_address'], 0, 10) . "...\n";
} else {
    echo "   - 暂无钱包地址数据\n";
}

// 4. 模拟删除请求验证
echo "\n4. 模拟删除请求验证\n";

// 模拟session数据
$mock_admin_session = [
    'id' => $admin['id'],
    'username' => $admin['username']
];

echo "   - 模拟管理员session: " . json_encode($mock_admin_session) . "\n";

// 模拟密码验证
$input_password = "123456"; // 假设输入的密码
$encrypted_password = md5($input_password);

if ($encrypted_password === $admin['password']) {
    echo "✅ 密码验证成功：删除操作可以执行\n";
} else {
    echo "❌ 密码验证失败：删除操作被阻止\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n修复说明：\n";
echo "1. 修正了session键名从 'admin' 改为 'admin_user'\n";
echo "2. 修正了密码验证方式从 SHA1+Salt 改为 MD5\n";
echo "3. 优化了前端密码提示信息\n";
echo "4. 现在删除钱包地址功能应该可以正常工作\n"; 