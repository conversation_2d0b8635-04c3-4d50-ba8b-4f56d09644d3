-- VIP等级开关配置表
CREATE TABLE IF NOT EXISTS `xy_vip_level_switch` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `vip_level` int(11) NOT NULL COMMENT 'VIP等级 1-6',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开启 1=开启 0=关闭',
  `switch_name` varchar(50) NOT NULL COMMENT '开关名称',
  `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
  `created_time` int(11) NOT NULL COMMENT '创建时间',
  `updated_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `vip_level` (`vip_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP等级开关配置表';

-- 插入默认数据（VIP1-VIP6默认全部开启）
INSERT INTO `xy_vip_level_switch` (`vip_level`, `is_enabled`, `switch_name`, `description`, `created_time`, `updated_time`) VALUES
(1, 1, 'VIP1任务开关', 'VIP1等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 1, 'VIP2任务开关', 'VIP2等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 1, 'VIP3任务开关', 'VIP3等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, 1, 'VIP4任务开关', 'VIP4等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, 1, 'VIP5任务开关', 'VIP5等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(6, 1, 'VIP6任务开关', 'VIP6等级任务接单开关', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()); 