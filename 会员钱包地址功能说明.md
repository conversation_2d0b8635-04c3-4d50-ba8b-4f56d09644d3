# 会员钱包地址管理功能

## 功能概述

本次更新实现了两个主要需求：

### 第一个问题：移除会员列表中的银行卡和地址按钮
- ✅ 已从会员列表页面移除"银行卡"和"地址"按钮
- ✅ 简化了会员列表的操作界面

### 第二个问题：新增会员钱包地址管理功能
- ✅ 在会员等级菜单下新增"会员钱包地址"子菜单
- ✅ 实现完整的钱包地址管理功能
- ✅ 编辑和删除操作需要管理员密码验证

## 功能特性

### 1. 钱包地址列表显示
- 用户等级
- 用户名
- 账号
- 钱包地址（超长地址自动截断显示）
- 钱包类型
- 状态（正常/禁用）
- 添加时间
- 操作按钮

### 2. 支持的钱包类型
- USDT-TRC20
- USDT-ERC20
- BTC
- ETH

### 3. 安全特性
- 编辑钱包地址需要输入管理员密码验证
- 删除钱包地址需要输入管理员密码验证
- 防止重复添加相同的钱包地址

### 4. 搜索功能
- 按账号搜索
- 按用户名搜索
- 按钱包类型筛选

## 安装说明

### 1. 运行安装脚本
在浏览器中访问：`http://你的域名/install_wallet_feature.php`

或者在服务器上运行：
```bash
cd public
php install_wallet_feature.php
```

### 2. 安装脚本会自动完成：
- 创建 `xy_user_wallet` 数据库表
- 添加菜单项到系统菜单
- 设置正确的权限节点

### 3. 安装完成后
- 删除 `public/install_wallet_feature.php` 文件（安全考虑）
- 登录管理后台查看新功能

## 文件清单

### 修改的文件：
1. `application/admin/view/users/index.html` - 移除银行卡和地址按钮
2. `application/admin/controller/Users.php` - 添加钱包地址管理方法

### 新增的文件：
1. `application/admin/view/users/wallet_address.html` - 钱包地址列表页面
2. `application/admin/view/users/wallet_address_search.html` - 搜索表单
3. `application/admin/view/users/add_wallet_address.html` - 添加钱包地址页面
4. `application/admin/view/users/edit_wallet_address.html` - 编辑钱包地址页面
5. `install_wallet_feature.php` - 安装脚本

## 数据库表结构

```sql
CREATE TABLE `xy_user_wallet` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `wallet_address` varchar(255) NOT NULL COMMENT '钱包地址',
  `wallet_type` varchar(50) NOT NULL COMMENT '钱包类型',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:正常,0:禁用)',
  `addtime` int(11) NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `wallet_type` (`wallet_type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包地址表';
```

## 使用说明

### 1. 访问钱包地址管理
- 登录管理后台
- 导航到：会员管理 → 会员钱包地址

### 2. 添加钱包地址
- 点击"添加钱包地址"按钮
- 选择用户
- 输入钱包地址
- 选择钱包类型
- 保存

### 3. 编辑钱包地址
- 点击列表中的"编辑"按钮
- 修改钱包地址或类型
- 输入管理员密码进行验证
- 保存修改

### 4. 删除钱包地址
- 点击列表中的"删除"按钮
- 在弹出框中输入管理员密码
- 确认删除

## 注意事项

1. 编辑和删除操作都需要输入当前登录管理员的密码进行验证
2. 系统会防止为同一用户添加重复的钱包地址
3. 钱包地址支持长地址，列表中会自动截断显示，鼠标悬停可查看完整地址
4. 删除用户时，相关的钱包地址记录会自动删除（外键约束）

## 技术实现

- 遵循 DRY、KISS、SOLID、YAGNI 原则
- 使用 ThinkPHP 5.1 框架
- 采用 MVC 架构模式
- 实现了完整的 CRUD 操作
- 包含安全验证机制 