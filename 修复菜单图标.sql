-- 修复菜单图标和检查数据库
-- ===============================

-- 1. 首先检查xy_user_wallet表是否存在
SHOW TABLES LIKE 'xy_user_wallet';

-- 2. 如果表不存在，创建表
CREATE TABLE IF NOT EXISTS `xy_user_wallet` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `wallet_address` varchar(255) NOT NULL COMMENT '钱包地址',
  `wallet_type` varchar(50) NOT NULL COMMENT '钱包类型(USDT-TRC20,USDT-ERC20,BTC,ETH等)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:正常,0:禁用)',
  `addtime` int(11) NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `wallet_type` (`wallet_type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包地址表';

-- 3. 更新菜单图标
UPDATE system_menu SET icon = 'fa fa-credit-card' WHERE title = '会员钱包地址';

-- 4. 插入一些测试数据（可选）
-- 先查看是否有用户数据
SELECT id, tel, username FROM xy_users LIMIT 3;

-- 5. 如果有用户，可以插入测试数据（请根据实际用户ID修改）
-- INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
-- VALUES 
-- (1, 'TQn9Y2khEsLMWiWt2p9CN3Uw9kK9yTVtQH', 'USDT-TRC20', 1, UNIX_TIMESTAMP()),
-- (2, '******************************************', 'USDT-ERC20', 1, UNIX_TIMESTAMP());

-- 6. 检查菜单是否正确
SELECT id, pid, title, icon, url FROM system_menu WHERE title = '会员钱包地址'; 