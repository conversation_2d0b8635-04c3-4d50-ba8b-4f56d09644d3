-- 修复菜单问题的SQL语句
-- ================================

-- 第一步：删除错误的菜单项
DELETE FROM system_menu WHERE title = '会员钱包地址';
DELETE FROM system_menu WHERE title = '添加钱包地址';
DELETE FROM system_menu WHERE title = '编辑钱包地址';
DELETE FROM system_menu WHERE title = '删除钱包地址';

-- 第二步：查看正确的菜单结构
SELECT id, pid, title, url, sort FROM system_menu WHERE pid = 63 ORDER BY sort;

-- 第三步：重新添加主菜单（确保在会员管理下面）
INSERT INTO `system_menu` (`pid`, `title`, `node`, `url`, `params`, `icon`, `sort`, `status`) 
VALUES (63, '会员钱包地址', 'admin/users/wallet_address', 'admin/users/wallet_address', '', 'fa fa-wallet', 105, 1);

-- 第四步：查看新插入的菜单ID
SELECT id FROM system_menu WHERE title = '会员钱包地址' AND pid = 63;

-- 第五步：添加子菜单（请将XXX替换为上面查询到的ID）
-- 注意：子菜单通常不需要添加，因为它们是通过权限控制的

-- 第六步：检查权限节点是否存在
SELECT * FROM system_node WHERE node LIKE '%wallet_address%';

-- 如果没有权限节点，需要添加：
INSERT INTO `system_node` (`node`, `title`, `type`, `is_menu`, `is_auth`, `is_login`) 
VALUES ('admin/users/wallet_address', '会员钱包地址', 'controller', 1, 1, 1);

INSERT INTO `system_node` (`node`, `title`, `type`, `is_menu`, `is_auth`, `is_login`) 
VALUES ('admin/users/add_wallet_address', '添加钱包地址', 'action', 0, 1, 1);

INSERT INTO `system_node` (`node`, `title`, `type`, `is_menu`, `is_auth`, `is_login`) 
VALUES ('admin/users/edit_wallet_address', '编辑钱包地址', 'action', 0, 1, 1);

INSERT INTO `system_node` (`node`, `title`, `type`, `is_menu`, `is_auth`, `is_login`) 
VALUES ('admin/users/delete_wallet_address', '删除钱包地址', 'action', 0, 1, 1); 