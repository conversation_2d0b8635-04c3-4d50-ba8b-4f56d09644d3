# 抢单页面修改总结

## 修改需求
将页面上的"今日订单量已抢光"修改为"今日可抢单次数"，并根据后端VIP等级设置来规定用户每天可抢的次数。

## 修改内容

### 1. 控制器文件修改 (application/index/controller/RotOrder.php)

#### index方法修改：
- 添加了今日时间范围定义，确保只计算当天的数据
- 优化了今日已抢单数和今日已抢佣金的计算逻辑
- 添加了剩余可抢单次数的计算：`$remaining_orders = max(0, $order_num - $this->day_d_count);`
- 将剩余次数传递给视图：`$this->remaining_orders = $remaining_orders;`

#### submit_order方法修改：
- 修正了今日已抢单数的计算，添加了时间范围限制
- 优化了重复查询，避免多次计算同一数据
- 确保抢单限制检查基于准确的今日数据

### 2. 视图文件修改 (application/index/view/rot_order/index.html)

第785行修改：
```html
<!-- 修改前 -->
<div class="p_line2-item-title">{$Think.lang.Todayhasgrabbedtheoddnumber}</div>
<div class="p_line2-item-num">{$day_d_count}{$Think.lang.single}</div>

<!-- 修改后 -->
<div class="p_line2-item-title">{$Think.lang.TodayRemainingOrders}</div>
<div class="p_line2-item-num">{$remaining_orders}{$Think.lang.single}</div>
```

### 3. 语言文件更新

#### 中文语言文件 (application/index/lang/zh.php)
第155行添加：
```php
'TodayRemainingOrders'=>'今日可抢单次数',
```

#### 英文语言文件 (application/index/lang/en.php)
第155行添加：
```php
'TodayRemainingOrders'=>'Today remaining orders',
```

#### 英文美国语言文件 (application/index/lang/en-us.php)
第299行添加：
```php
'TodayRemainingOrders' => 'Today remaining orders',
```

## 实现逻辑

### VIP等级抢单限制
系统根据`xy_level`表中的`order_num`字段来设置每个VIP等级的每日抢单限制：
- VIP 0: 0单/天
- VIP 1: 5单/天  
- VIP 2: 20单/天
- VIP 3: 50单/天
- VIP 4: 200单/天
- VIP 5: 500单/天
- VIP 6: 1000单/天

### 剩余次数计算
```php
$remaining_orders = max(0, $order_num - $this->day_d_count);
```
- `$order_num`: 用户VIP等级对应的每日抢单限制
- `$this->day_d_count`: 今日已抢单数（限制在当天时间范围内）
- `max(0, ...)`: 确保剩余次数不为负数

### 时间范围限制
所有今日数据计算都限制在当天的时间范围内：
```php
$today_start = strtotime(date('Y-m-d 00:00:00'));
$today_end = strtotime(date('Y-m-d 23:59:59'));
$today_where = [
    ['uid','=',cookie('user_id')],
    ['addtime','between',[$today_start, $today_end]],
];
```

## 修改的文件列表

1. `application/index/controller/RotOrder.php` - 控制器逻辑修改
2. `application/index/view/rot_order/index.html` - 视图显示修改  
3. `application/index/lang/zh.php` - 中文语言文件
4. `application/index/lang/en.php` - 英文语言文件
5. `application/index/lang/en-us.php` - 英文美国语言文件

## 测试验证

创建了测试文件 `test_order_count.php` 来验证计算逻辑的正确性。

## 效果

修改完成后，页面现在显示"今日可抢单次数"而不是"今日已抢单数"，显示的数值是根据用户VIP等级的每日抢单限制减去今日已抢单数计算得出的剩余次数。系统会根据每个VIP等级设置的`order_num`字段来限制用户每天的抢单次数。 