会员钱包地址管理功能 - 安装完成说明
===========================================

✅ 已完成的功能：

1. 第一个问题：移除会员列表中的银行卡和地址按钮
   - 已从 application/admin/view/users/index.html 中移除"银行卡"和"地址"按钮
   - 简化了会员列表的操作界面

2. 第二个问题：新增会员钱包地址管理功能
   - 在 application/admin/controller/Users.php 中添加了钱包地址管理方法
   - 创建了完整的钱包地址管理页面
   - 编辑和删除操作需要管理员密码验证

📁 修改和新增的文件：

修改的文件：
- application/admin/view/users/index.html (移除银行卡和地址按钮)
- application/admin/controller/Users.php (添加钱包地址管理方法)

新增的文件：
- application/admin/view/users/wallet_address.html (钱包地址列表页面)
- application/admin/view/users/wallet_address_search.html (搜索表单)
- application/admin/view/users/add_wallet_address.html (添加钱包地址页面)
- application/admin/view/users/edit_wallet_address.html (编辑钱包地址页面)
- sdko0/public/install_wallet_feature.php (安装脚本)

🚀 手动安装步骤：

1. 创建数据库表：
   执行 手动安装SQL.sql 文件中的第一个CREATE TABLE语句

2. 添加菜单项：
   - 先查询 system_menu 表，找到会员管理相关的父级菜单ID
   - 修改 手动安装SQL.sql 中的 [父级菜单ID] 为实际ID
   - 执行主菜单INSERT语句
   - 查看刚插入的菜单ID
   - 修改 [钱包地址菜单ID] 为实际ID
   - 执行子菜单INSERT语句

3. 完成后登录管理后台查看新功能

🔧 功能特性：

- 显示用户等级、用户名、账号、钱包地址、钱包类型
- 支持多种钱包类型：USDT-TRC20、USDT-ERC20、BTC、ETH
- 编辑和删除操作需要输入管理员密码验证
- 防止重复添加相同的钱包地址
- 支持按账号、用户名、钱包类型搜索

📍 菜单位置：
登录管理后台 → 会员管理 → 会员钱包地址

⚠️ 注意事项：
1. 确保运行目录是 public
2. 安装完成后请删除安装脚本文件
3. 编辑和删除操作都需要管理员密码验证
4. 系统会防止为同一用户添加重复的钱包地址

如有问题，请检查：
- 数据库连接是否正常
- 菜单权限是否正确设置
- 文件路径是否正确 