# 宝塔计划任务配置解决方案

## 问题原因
网页访问和宝塔计划任务的执行环境不同，导致定时任务无法正常工作。

## 解决方案

### 方案1：改进的Shell脚本（推荐）

在宝塔面板 -> 计划任务 -> 添加计划任务：

**任务类型**: Shell脚本
**任务名称**: 利息宝每日收益
**执行周期**: 每天 13:00 执行一次
**脚本内容**:
```bash
#!/bin/bash
# 利息宝每日收益计算

# 设置日志文件
LOG_FILE="/tmp/lixibao_cron.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$DATE] 开始执行利息宝收益计算" >> $LOG_FILE

# 使用curl命令请求，添加必要的headers
curl -H "User-Agent: Mozilla/5.0 (compatible; BaoTaCron/1.0)" \
     -H "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" \
     -H "Accept-Language: zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3" \
     -s "https://tiktokpro.org/index/crontab/lixibao_js" \
     >> $LOG_FILE 2>&1

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "[$DATE] 利息宝收益计算执行成功" >> $LOG_FILE
else
    echo "[$DATE] 利息宝收益计算执行失败" >> $LOG_FILE
fi

echo "------------------------------------" >> $LOG_FILE
```

### 方案2：使用PHP脚本（备用方案）

如果方案1不行，使用直接的PHP脚本：

**任务类型**: PHP脚本
**脚本路径**: `/www/wwwroot/你的域名/force_daily_income.php`

### 方案3：指定完整PHP路径

在宝塔面板查找PHP路径（通常在 `/www/server/php/版本号/bin/php`），然后：

**任务类型**: Shell脚本
**脚本内容**:
```bash
#!/bin/bash
# 使用完整PHP路径执行

# 找到PHP路径（根据你的实际情况调整）
PHP_PATH="/www/server/php/74/bin/php"  # 或其他版本

# 进入网站目录
cd /www/wwwroot/你的域名

# 执行PHP脚本
$PHP_PATH force_daily_income.php >> /tmp/lixibao_php.log 2>&1
```

### 方案4：使用wget替代curl

有些服务器curl可能有问题，可以使用wget：

```bash
#!/bin/bash
wget -q -O /tmp/lixibao_result.log \
     --user-agent="Mozilla/5.0 (compatible; BaoTaCron/1.0)" \
     "https://tiktokpro.org/index/crontab/lixibao_js"
```

## 测试方案

### 1. 手动测试命令

在宝塔终端中先手动测试：

```bash
# 测试curl命令
curl -H "User-Agent: Mozilla/5.0 (compatible; BaoTaCron/1.0)" \
     -s "https://tiktokpro.org/index/crontab/lixibao_js"

# 检查返回结果是否包含"处理用户数"
```

### 2. 查看执行日志

检查以下位置的日志：
- `/tmp/lixibao_cron.log` 
- 宝塔面板 -> 计划任务 -> 日志
- 网站错误日志

## 排查步骤

### 1. 确认网络连通性
```bash
ping tiktokpro.org
```

### 2. 测试基本HTTP请求
```bash
curl -I https://tiktokpro.org/
```

### 3. 检查防火墙和安全设置
- 确认服务器出站访问正常
- 检查是否有安全软件阻止

### 4. 验证执行结果
手动访问 `https://tiktokpro.org/index/crontab/lixibao_js` 查看正常返回结果

## 监控建议

### 1. 添加结果检查
在脚本中添加结果验证：

```bash
RESULT=$(curl -s "https://tiktokpro.org/index/crontab/lixibao_js")
if echo "$RESULT" | grep -q "处理用户数"; then
    echo "[$DATE] 执行成功，结果: $RESULT" >> $LOG_FILE
else
    echo "[$DATE] 执行异常，返回: $RESULT" >> $LOG_FILE
fi
```

### 2. 邮件通知
在宝塔计划任务中启用邮件通知，当任务失败时发送邮件。

## 最终建议

1. **优先使用方案1（Shell脚本 + curl）**
2. **设置日志记录，便于排查问题**
3. **定期检查日志，确保任务正常执行**
4. **建议设置多个时间点执行（如每天1点和13点）**
5. **保留原有的PHP补发脚本作为备用**

如果以上方案都不行，可能需要检查服务器的网络配置或联系服务器管理员。 