-- 会员钱包地址管理功能 - 手动安装SQL
-- ==========================================

-- 1. 创建用户钱包地址表
CREATE TABLE IF NOT EXISTS `xy_user_wallet` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `wallet_address` varchar(255) NOT NULL COMMENT '钱包地址',
  `wallet_type` varchar(50) NOT NULL COMMENT '钱包类型(USDT-TRC20,USDT-ERC20,BTC,ETH等)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:正常,0:禁用)',
  `addtime` int(11) NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `wallet_type` (`wallet_type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包地址表';

-- 2. 添加菜单项（请根据您的实际菜单结构调整PID）
-- 首先查找会员管理的父级菜单ID，然后替换下面的 [父级菜单ID]

-- 添加主菜单：会员钱包地址
INSERT INTO `system_menu` (`pid`, `title`, `node`, `url`, `params`, `icon`, `sort`, `status`) 
VALUES ([父级菜单ID], '会员钱包地址', 'admin/users/wallet_address', 'admin/users/wallet_address', '', 'fa fa-wallet', 100, 1);

-- 获取刚插入的菜单ID，然后添加子菜单
-- 请将 [钱包地址菜单ID] 替换为上面插入的菜单ID

-- 添加子菜单：添加钱包地址
INSERT INTO `system_menu` (`pid`, `title`, `node`, `url`, `params`, `icon`, `sort`, `status`) 
VALUES ([钱包地址菜单ID], '添加钱包地址', 'admin/users/add_wallet_address', 'admin/users/add_wallet_address', '', '', 1, 1);

-- 添加子菜单：编辑钱包地址
INSERT INTO `system_menu` (`pid`, `title`, `node`, `url`, `params`, `icon`, `sort`, `status`) 
VALUES ([钱包地址菜单ID], '编辑钱包地址', 'admin/users/edit_wallet_address', 'admin/users/edit_wallet_address', '', '', 2, 1);

-- 添加子菜单：删除钱包地址
INSERT INTO `system_menu` (`pid`, `title`, `node`, `url`, `params`, `icon`, `sort`, `status`) 
VALUES ([钱包地址菜单ID], '删除钱包地址', 'admin/users/delete_wallet_address', 'admin/users/delete_wallet_address', '', '', 3, 1);

-- ==========================================
-- 手动安装步骤：
-- 1. 先执行第一个CREATE TABLE语句创建数据库表
-- 2. 查询您的system_menu表，找到会员管理相关的父级菜单ID
-- 3. 将上面SQL中的 [父级菜单ID] 替换为实际的ID
-- 4. 执行第一个INSERT语句添加主菜单
-- 5. 查看刚插入的菜单ID
-- 6. 将 [钱包地址菜单ID] 替换为实际的ID
-- 7. 执行剩余的INSERT语句添加子菜单
-- ========================================== 