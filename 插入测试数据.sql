-- 插入测试钱包地址数据
-- ========================

-- 为用户ID 1747 (18888888888 - hello, sir) 添加钱包地址
INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
VALUES 
(1747, 'TQn9Y2khEsLMWiWt2p9CN3Uw9kK9yTVtQH', 'USDT-TRC20', 1, UNIX_TIMESTAMP()),
(1747, '******************************************', 'USDT-ERC20', 1, UNIX_TIMESTAMP());

-- 为用户ID 1748 (+************ - asd) 添加钱包地址
INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
VALUES 
(1748, '******************************************', 'BTC', 1, UNIX_TIMESTAMP()),
(1748, '0x8ba1f109551bD432803012645Hac136c30F0b5c', 'ETH', 1, UNIX_TIMESTAMP());

-- 为用户ID 1749 (+8615626536555 - amara) 添加钱包地址
INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
VALUES 
(1749, 'TLsV52sRDL79HXGKs546DKw7nZkLNzNNTG', 'USDT-TRC20', 1, UNIX_TIMESTAMP()),
(1749, '******************************************', 'USDT-ERC20', 1, UNIX_TIMESTAMP());

-- 查看插入的数据
SELECT 
    w.id,
    w.uid,
    u.tel,
    u.username,
    w.wallet_address,
    w.wallet_type,
    w.status,
    FROM_UNIXTIME(w.addtime) as add_time
FROM xy_user_wallet w
LEFT JOIN xy_users u ON w.uid = u.id
ORDER BY w.id DESC; 