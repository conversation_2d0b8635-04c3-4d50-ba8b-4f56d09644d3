-- 检查钱包地址数据
-- ==================

-- 1. 查看xy_user_wallet表中的所有数据
SELECT * FROM xy_user_wallet ORDER BY id DESC;

-- 2. 查看用户表中的数据
SELECT id, tel, username FROM xy_users WHERE id IN (1747, 1748, 1749);

-- 3. 联合查询检查数据关联
SELECT 
    w.id,
    w.uid,
    u.tel,
    u.username,
    w.wallet_address,
    w.wallet_type,
    w.status,
    FROM_UNIXTIME(w.addtime) as add_time
FROM xy_user_wallet w
LEFT JOIN xy_users u ON w.uid = u.id
ORDER BY w.id DESC;

-- 4. 检查是否有其他用户的钱包地址
SELECT COUNT(*) as total_wallets FROM xy_user_wallet;
SELECT COUNT(DISTINCT uid) as unique_users FROM xy_user_wallet; 