-- 第一步：创建数据库表
CREATE TABLE IF NOT EXISTS `xy_user_wallet` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `wallet_address` varchar(255) NOT NULL COMMENT '钱包地址',
  `wallet_type` varchar(50) NOT NULL COMMENT '钱包类型(USDT-TRC20,USDT-ERC20,BTC,ETH等)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:正常,0:禁用)',
  `addtime` int(11) NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `wallet_type` (`wallet_type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包地址表';

-- 第二步：添加主菜单（父级菜单ID是63）
INSERT INTO `system_menu` (`pid`, `title`, `node`, `url`, `params`, `icon`, `sort`, `status`) 
VALUES (63, '会员钱包地址', 'admin/users/wallet_address', 'admin/users/wallet_address', '', 'fa fa-wallet', 100, 1);

-- 第三步：查看刚插入的菜单ID
SELECT id FROM system_menu WHERE title = '会员钱包地址' ORDER BY id DESC LIMIT 1;

-- 第四步：添加子菜单（假设新菜单ID是112，请根据上面查询结果修改）
-- 请将下面所有的 112 替换为上面查询到的实际ID

INSERT INTO `system_menu` (`pid`, `title`, `node`, `url`, `params`, `icon`, `sort`, `status`) 
VALUES (112, '添加钱包地址', 'admin/users/add_wallet_address', 'admin/users/add_wallet_address', '', '', 1, 1);

INSERT INTO `system_menu` (`pid`, `title`, `node`, `url`, `params`, `icon`, `sort`, `status`) 
VALUES (112, '编辑钱包地址', 'admin/users/edit_wallet_address', 'admin/users/edit_wallet_address', '', '', 2, 1);

INSERT INTO `system_menu` (`pid`, `title`, `node`, `url`, `params`, `icon`, `sort`, `status`) 
VALUES (112, '删除钱包地址', 'admin/users/delete_wallet_address', 'admin/users/delete_wallet_address', '', '', 3, 1); 