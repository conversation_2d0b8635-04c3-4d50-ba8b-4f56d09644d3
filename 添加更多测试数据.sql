-- 为其他用户添加钱包地址测试数据
-- ================================

-- 首先查看系统中的其他用户
SELECT id, tel, username FROM xy_users WHERE id NOT IN (1747, 1748, 1749) LIMIT 10;

-- 为更多用户添加钱包地址（请根据实际用户ID调整）
-- 假设有用户ID 1750-1755，可以根据实际情况修改

-- 用户1750
INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
VALUES 
(1750, 'TKzxdSv2FZKQrEqkKVgp5DcwEXBEKMg2Ax', 'USDT-TRC20', 1, UNIX_TIMESTAMP()),
(1750, '******************************************', 'USDT-ERC20', 1, UNIX_TIMESTAMP());

-- 用户1751
INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
VALUES 
(1751, '**********************************', 'BTC', 1, UNIX_TIMESTAMP()),
(1751, '******************************************', 'ETH', 1, UNIX_TIMESTAMP());

-- 用户1752
INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
VALUES 
(1752, 'TUEYcwW7GtHdNFDbdbaEz7VPqKsuMswvQs', 'USDT-TRC20', 0, UNIX_TIMESTAMP()),
(1752, '******************************************', 'USDT-ERC20', 1, UNIX_TIMESTAMP());

-- 用户1753
INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
VALUES 
(1753, '******************************************', 'BTC', 1, UNIX_TIMESTAMP()),
(1753, '******************************************', 'ETH', 1, UNIX_TIMESTAMP());

-- 用户1754
INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
VALUES 
(1754, 'TLa2f6VPqDgRE31TvDMkJNxHmg2d9yKgzj', 'USDT-TRC20', 1, UNIX_TIMESTAMP());

-- 用户1755
INSERT INTO xy_user_wallet (uid, wallet_address, wallet_type, status, addtime) 
VALUES 
(1755, '******************************************', 'USDT-ERC20', 0, UNIX_TIMESTAMP()),
(1755, '**********************************', 'BTC', 1, UNIX_TIMESTAMP());

-- 查看所有钱包地址数据
SELECT 
    w.id,
    w.uid,
    u.tel,
    u.username,
    w.wallet_address,
    w.wallet_type,
    CASE w.status 
        WHEN 1 THEN '正常' 
        WHEN 0 THEN '禁用' 
        ELSE '未知' 
    END as status_text,
    FROM_UNIXTIME(w.addtime) as add_time
FROM xy_user_wallet w
LEFT JOIN xy_users u ON w.uid = u.id
ORDER BY w.id DESC; 