宝塔计划任务配置 - 利息宝每日收益

=============================================
方法1：Shell脚本（推荐）
=============================================

1. 登录宝塔面板
2. 点击 "计划任务"
3. 点击 "添加任务"
4. 填写配置：
   - 任务类型：Shell脚本
   - 任务名称：利息宝每日收益
   - 执行周期：每天 13:00 执行一次
   - 脚本内容：（复制下面的内容）

curl -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" -s "https://tiktokpro.org/index/crontab/lixibao_js" > /tmp/lixibao_result.log 2>&1

5. 点击确定

=============================================
方法2：带日志的Shell脚本（详细版）
=============================================

脚本内容：

#!/bin/bash
LOG_FILE="/tmp/lixibao_cron.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')
echo "[$DATE] 开始执行利息宝收益计算" >> $LOG_FILE
curl -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" -s "https://tiktokpro.org/index/crontab/lixibao_js" >> $LOG_FILE 2>&1
echo "[$DATE] 执行完成" >> $LOG_FILE

=============================================
方法3：PHP脚本（备用）
=============================================

如果上面方法不行，使用这个：

1. 任务类型：PHP脚本
2. 脚本路径：/www/wwwroot/你的域名/force_daily_income.php

=============================================
如何检查是否成功
=============================================

1. 等待任务执行后，检查日志文件：
   - SSH登录服务器
   - 执行：cat /tmp/lixibao_cron.log
   - 或者：cat /tmp/lixibao_result.log

2. 查看宝塔计划任务日志：
   - 宝塔面板 -> 计划任务 -> 点击对应任务的"日志"

3. 手动测试（在SSH终端中执行）：
   curl -s "https://tiktokpro.org/index/crontab/lixibao_js"

=============================================
常见问题排查
=============================================

问题1：curl命令找不到
解决：在脚本前加上 /usr/bin/curl 或 which curl

问题2：网络连接问题
解决：检查服务器出站网络，ping tiktokpro.org

问题3：权限问题
解决：确保宝塔计划任务有执行权限

问题4：返回结果异常
解决：检查防火墙、安全软件设置

=============================================
建议的最终配置
=============================================

任务类型：Shell脚本
任务名称：利息宝每日收益
执行周期：每天 13:00
脚本内容：
curl -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" -s "https://tiktokpro.org/index/crontab/lixibao_js" > /tmp/lixibao_$(date +%Y%m%d).log 2>&1

这样每天会生成一个独立的日志文件，方便查看历史记录。 