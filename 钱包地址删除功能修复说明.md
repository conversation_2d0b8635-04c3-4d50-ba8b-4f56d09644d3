# 钱包地址删除功能修复说明

## 问题描述
后端会员钱包地址管理功能中，删除钱包地址时输入管理员密码后会提示密码错误，无法完成删除操作。

## 问题分析

### 根本原因
通过代码分析发现，钱包地址删除功能中的管理员密码验证逻辑存在两个关键问题：

1. **Session键名错误**
   - 代码中使用：`session('admin')`
   - 实际应该是：`session('admin_user')`

2. **密码加密方式不匹配**
   - 删除功能使用：`sha1($password . $salt . config('pwd_str'))`
   - 系统实际使用：`md5($password)`

### 技术细节

#### 管理员登录验证逻辑（Login.php）
```php
// 系统使用MD5加密存储管理员密码
if (md5($user['password'] . session('loginskey')) !== $data['password']) {
    $this->error('登录账号或密码错误，请重新输入!');
}

// 登录成功后设置session
session('admin_user', $user);
```

#### 原有错误的删除验证逻辑
```php
// 错误的session键名
$admin_info = session('admin');

// 错误的密码验证方式
$encrypted_password = sha1($admin_password . $admin['salt'] . config('pwd_str'));
```

## 修复方案

### 1. 修正Session键名
将 `session('admin')` 修改为 `session('admin_user')`，与系统登录逻辑保持一致。

### 2. 修正密码验证方式
将复杂的SHA1+Salt验证改为简单的MD5验证，与系统密码存储方式匹配。

### 3. 优化用户体验
改进前端密码输入提示，明确告知用户需要输入"管理员登录密码"。

## 修复内容

### 修改的文件

#### 1. application/admin/controller/Users.php
- **delete_wallet_address()** 方法
- **edit_wallet_address()** 方法

**修改前：**
```php
$admin_info = session('admin');
$encrypted_password = sha1($admin_password . $admin['salt'] . config('pwd_str'));
```

**修改后：**
```php
$admin_info = session('admin_user'); // 修正session键名
$encrypted_password = md5($admin_password); // 修正密码验证方式
```

#### 2. application/admin/view/users/wallet_address.html
- 优化删除确认对话框
- 改进密码输入提示信息

**修改前：**
```javascript
layer.prompt({
    title: '删除确认',
    formType: 1,
    value: '',
    area: ['300px', '100px']
}, function(password, index) {
```

**修改后：**
```javascript
layer.confirm('确定要删除这个钱包地址吗？', {
    title: '删除确认',
    btn: ['确定删除', '取消']
}, function(index) {
    layer.close(index);
    layer.prompt({
        title: '请输入管理员登录密码',
        formType: 1,
        value: '',
        area: ['350px', '120px']
    }, function(password, promptIndex) {
```

## 验证方法

### 1. 功能测试
1. 登录后台管理系统
2. 进入"会员管理" → "会员钱包地址"
3. 点击任意钱包地址的"删除"按钮
4. 在确认对话框中点击"确定删除"
5. 在密码输入框中输入管理员登录密码
6. 确认删除操作成功执行

### 2. 技术验证
运行测试文件 `test_wallet_delete.php` 验证密码验证逻辑是否正确。

## 安全说明

### 密码验证机制
- 删除钱包地址需要输入管理员登录密码
- 密码验证使用与登录相同的MD5加密方式
- 验证失败时会阻止删除操作并提示错误信息

### 数据安全
- 删除操作使用数据库事务确保数据一致性
- 同时清理 `xy_user_wallet` 和 `xy_bankinfo` 两个表的相关数据
- 操作失败时自动回滚，保证数据完整性

## 总结

本次修复解决了钱包地址删除功能中的密码验证问题，主要修正了：

1. ✅ Session键名错误
2. ✅ 密码加密方式不匹配
3. ✅ 用户体验优化

修复后，管理员可以正常使用登录密码删除钱包地址，功能完全恢复正常。

## 修改的文件列表
- `application/admin/controller/Users.php`
- `application/admin/view/users/wallet_address.html`
- `test_wallet_delete.php` (新增测试文件)
- `钱包地址删除功能修复说明.md` (本文档) 