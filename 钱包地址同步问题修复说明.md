# 钱包地址同步问题修复说明

## 问题描述

**核心问题：前端用户钱包地址与后端管理员看到的钱包地址不一致**

### 问题根源
1. **前端用户修改钱包地址**：数据保存到 `xy_bankinfo` 表的 `cardnum` 字段
2. **后端管理员查看钱包地址**：从 `xy_user_wallet` 表读取数据
3. **结果**：两个系统使用不同的数据表，导致数据不同步

## 修复方案

### 1. 诊断工具
创建了两个诊断脚本：
- `check_wallet_sync.php` - 检查两个表的数据差异
- `fix_wallet_sync.php` - 一键修复现有数据不同步问题

### 2. 代码修复

#### 前端用户绑定钱包功能修复
**文件：** `application/index/controller/My.php`
**方法：** `bind_wallet()`

**修复内容：**
- 使用数据库事务确保数据一致性
- 同时更新 `xy_bankinfo` 和 `xy_user_wallet` 两个表
- 如果操作失败，自动回滚所有更改

#### 后端管理员钱包地址管理功能修复
**文件：** `application/admin/controller/Users.php`

**修复的方法：**

1. **添加钱包地址** (`add_wallet_address()`)
   - 同时在两个表中添加数据
   - 使用事务确保数据一致性

2. **编辑钱包地址** (`edit_wallet_address()`)
   - 同时更新两个表的数据
   - 保持数据同步

3. **删除钱包地址** (`delete_wallet_address()`)
   - 删除 `xy_user_wallet` 表记录
   - 清空 `xy_bankinfo` 表对应用户的钱包信息

## 修复后的效果

### 数据同步
- ✅ 前端用户修改钱包地址后，后端管理员立即可以看到最新数据
- ✅ 后端管理员修改钱包地址后，前端用户也能看到最新数据
- ✅ 所有钱包地址操作都保持两个表数据同步

### 数据一致性
- ✅ 使用数据库事务确保操作的原子性
- ✅ 如果任何一个表操作失败，所有操作都会回滚
- ✅ 避免了数据不一致的情况

## 使用说明

### 1. 修复现有数据
1. 访问 `http://你的域名/check_wallet_sync.php` 检查当前数据状态
2. 访问 `http://你的域名/fix_wallet_sync.php` 一键修复现有数据不同步问题

### 2. 验证修复效果
1. 前端用户登录，修改钱包地址
2. 后端管理员查看该用户的钱包地址，确认数据已同步
3. 后端管理员修改用户钱包地址
4. 前端用户查看钱包地址，确认数据已同步

## 技术细节

### 数据表结构
- `xy_bankinfo` 表：存储用户银行卡和钱包信息（原有表）
- `xy_user_wallet` 表：专门存储用户钱包地址信息（新增表）

### 字段映射关系
| xy_bankinfo | xy_user_wallet | 说明 |
|-------------|----------------|------|
| cardnum | wallet_address | 钱包地址 |
| wallet_type | wallet_type | 钱包类型 |
| status | status | 状态 |
| uid | uid | 用户ID |

### 事务处理
所有涉及两个表的操作都使用数据库事务：
```php
Db::startTrans();
try {
    // 操作表1
    // 操作表2
    Db::commit();
} catch (\Exception $e) {
    Db::rollback();
}
```

## 修改的文件清单

1. **application/index/controller/My.php** - 前端用户钱包绑定功能
2. **application/admin/controller/Users.php** - 后端管理员钱包地址管理功能
3. **check_wallet_sync.php** - 数据同步检查工具（新增）
4. **fix_wallet_sync.php** - 数据同步修复工具（新增）
5. **钱包地址同步问题修复说明.md** - 本说明文档（新增）

## 注意事项

1. **备份数据**：在执行修复操作前，建议备份相关数据表
2. **测试环境**：建议先在测试环境验证修复效果
3. **清理工具**：修复完成后，可以删除诊断和修复脚本文件

## 问题解决确认

修复完成后，以下问题应该得到解决：
- ✅ 前端用户修改钱包地址后，后端能立即看到更新
- ✅ 后端管理员修改钱包地址后，前端能立即看到更新
- ✅ 两个系统显示的钱包地址完全一致
- ✅ 数据操作具有事务性，不会出现部分更新的情况 